{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Rdl-XW1KbPZPtMMXzH7YM", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "3666c74d3dc28b89e433e9f987bf2211", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8e53b97ba401f50ac6acd08fcb47ac42731e261179de87231220fecac0603066", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "46f6569b248a8d6c9ccab23c820e2180b3676e0edb714979490492ef19144bf5"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Rdl-XW1KbPZPtMMXzH7YM", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "3666c74d3dc28b89e433e9f987bf2211", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8e53b97ba401f50ac6acd08fcb47ac42731e261179de87231220fecac0603066", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "46f6569b248a8d6c9ccab23c820e2180b3676e0edb714979490492ef19144bf5"}}}, "sortedMiddleware": ["/"]}