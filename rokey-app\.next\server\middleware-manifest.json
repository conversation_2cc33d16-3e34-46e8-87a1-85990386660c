{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "M6Y-xagY5lthVWR3P7BTD", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "67dc34b81258fabb7ce0df5d2325382f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "de6a36d7de8a04c9ee767d5e1500c8e9fcd6947078f50dfc39114a684a731b35", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6d93ad0aa6662badfb677ad14011e87ba3a5b351ad0156be24694c0159da4905"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "M6Y-xagY5lthVWR3P7BTD", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "67dc34b81258fabb7ce0df5d2325382f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "de6a36d7de8a04c9ee767d5e1500c8e9fcd6947078f50dfc39114a684a731b35", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6d93ad0aa6662badfb677ad14011e87ba3a5b351ad0156be24694c0159da4905"}}}, "sortedMiddleware": ["/"]}