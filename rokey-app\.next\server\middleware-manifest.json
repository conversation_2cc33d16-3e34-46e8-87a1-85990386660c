{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "dQR2vJW3_Gu0rOWXTZNRd", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "f678c16810ee9419b89dbedc854d109e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "33bf356ef721df5df2e8e5d8e16c16838d4be23687c88a902157e9b0d1f5554b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f7b030f30600f30c36c215add7a2429d3e383335e16ab00d737aecb7faeac5bf"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "dQR2vJW3_Gu0rOWXTZNRd", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "f678c16810ee9419b89dbedc854d109e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "33bf356ef721df5df2e8e5d8e16c16838d4be23687c88a902157e9b0d1f5554b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f7b030f30600f30c36c215add7a2429d3e383335e16ab00d737aecb7faeac5bf"}}}, "sortedMiddleware": ["/"]}