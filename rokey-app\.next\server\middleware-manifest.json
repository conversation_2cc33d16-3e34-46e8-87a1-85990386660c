{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "VBd5oLzlxxTF2Rb79WPG-", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "184dec853e5d1976c15b7937ff1ee183", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e415f4a94b598c07a831ec57bba47c94d875c2e57e6addf4107df6b36f3ee71a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c88c2385beb974aab20825951d07ea8872994fae8a031d614eb209ebdb3000b7"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "VBd5oLzlxxTF2Rb79WPG-", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "184dec853e5d1976c15b7937ff1ee183", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e415f4a94b598c07a831ec57bba47c94d875c2e57e6addf4107df6b36f3ee71a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c88c2385beb974aab20825951d07ea8872994fae8a031d614eb209ebdb3000b7"}}}, "sortedMiddleware": ["/"]}