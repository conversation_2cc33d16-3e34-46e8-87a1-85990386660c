{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "avxo5H306L8yZuwcVUtGF", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "dea59a44240137da80518ead1e36604c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4d886a57b989954fc34d76375248def8c43208a6b26b21291bca54237fc88a86", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f38951bdcaa6bf2c9ecf4ea496aac802e122315e4532b5ea64f86ad67b039995"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "avxo5H306L8yZuwcVUtGF", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "dea59a44240137da80518ead1e36604c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4d886a57b989954fc34d76375248def8c43208a6b26b21291bca54237fc88a86", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f38951bdcaa6bf2c9ecf4ea496aac802e122315e4532b5ea64f86ad67b039995"}}}, "sortedMiddleware": ["/"]}