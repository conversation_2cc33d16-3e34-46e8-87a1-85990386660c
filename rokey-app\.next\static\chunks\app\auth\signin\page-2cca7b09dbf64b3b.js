(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{65594:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(95155),r=t(12115),i=t(6874),l=t.n(i),n=t(66766),o=t(10184),c=t(48987),d=t(74500),u=t(52643),m=t(35695),x=t(64198);function h(){let[e,s]=(0,r.useState)(""),[t,i]=(0,r.useState)(""),[h,p]=(0,r.useState)(!1),[b,g]=(0,r.useState)(!1),[f,y]=(0,r.useState)(""),[j,w]=(0,r.useState)(!1),[N,v]=(0,r.useState)(""),[k,_]=(0,r.useState)(!1),[S,C]=(0,r.useState)(""),E=(0,m.useRouter)(),R=(0,m.useSearchParams)(),P=(0,u.createSupabaseBrowserClient)(),{success:K,error:F}=(0,x.dj)();(0,r.useEffect)(()=>{},[E,R,P.auth]);let I=async s=>{s.preventDefault(),g(!0),y("");try{let{data:s,error:a}=await P.auth.signInWithPassword({email:e,password:t});if(a)throw a;if(s.user){await new Promise(e=>setTimeout(e,500));let e=R.get("redirectTo"),t=R.get("plan"),a=R.get("email"),r=R.get("checkout_user_id");try{let{data:t}=await P.from("user_profiles").select("subscription_tier, subscription_status").eq("id",s.user.id).single();if(t&&"active"===t.subscription_status)return void(e?E.push(e):E.push("/dashboard"));let a=s.user.user_metadata,r=null==a?void 0:a.payment_status,i=null==a?void 0:a.plan;if("pending"===r&&i&&["starter","professional","enterprise"].includes(i))return void E.push("/pricing")}catch(e){}if(r&&t&&["starter","professional","enterprise"].includes(t)){let e="/checkout?plan=".concat(t,"&user_id=").concat(s.user.id).concat(a?"&email=".concat(encodeURIComponent(a)):"");E.push(e)}else if(t&&["starter","professional","enterprise"].includes(t))try{let{data:e}=await P.from("user_profiles").select("subscription_tier, subscription_status").eq("id",s.user.id).single();if(e&&"free"===e.subscription_tier&&"active"===e.subscription_status)E.push("/dashboard");else{let e="/checkout?plan=".concat(t,"&user_id=").concat(s.user.id).concat(a?"&email=".concat(encodeURIComponent(a)):"");E.push(e)}}catch(e){E.push("/dashboard")}else e?E.push(e):E.push("/dashboard")}}catch(e){y(e.message||"Invalid email or password. Please try again.")}finally{g(!1)}},W=async e=>{if(e.preventDefault(),_(!0),C(""),!N.trim()){C("Please enter your email address"),_(!1);return}try{let{error:e}=await P.auth.resetPasswordForEmail(N,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(e)throw e;K("Password reset email sent!","Check your inbox for instructions to reset your password."),w(!1),v("")}catch(e){C(e.message||"Failed to send reset email. Please try again."),F("Failed to send reset email",e.message||"Please try again.")}finally{_(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen flex bg-gradient-to-br from-[#1C051C] to-[#040716]",children:[(0,a.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-[0.15]",style:{backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.3) 1px, transparent 1px)",backgroundSize:"40px 40px",maskImage:"radial-gradient(ellipse at center, black 30%, transparent 80%)",WebkitMaskImage:"radial-gradient(ellipse at center, black 30%, transparent 80%)"}}),(0,a.jsxs)("div",{className:"max-w-lg relative z-10",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(l(),{href:"/",className:"inline-flex items-center space-x-3",children:[(0,a.jsx)(n.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:"RouKey"})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-white leading-tight",children:"Welcome back!"}),(0,a.jsx)("p",{className:"text-xl text-white/80 leading-relaxed",children:"Sign in to your RouKey account and continue building with our powerful routing platform."}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsxs)("p",{className:"text-white/60 text-sm",children:["Don't have an account?"," ",(0,a.jsx)(l(),{href:"/auth/signup",className:"text-white font-medium hover:text-white/80 transition-colors",children:"Sign up here"})]})})]})]})]}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center p-8 bg-white",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,a.jsx)("div",{className:"lg:hidden text-center",children:(0,a.jsxs)(l(),{href:"/",className:"inline-flex items-center space-x-2",children:[(0,a.jsx)(n.default,{src:"/RouKey_Logo_NOGLOW.png",alt:"RouKey",width:32,height:32,className:"w-8 h-8"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"RouKey"})]})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Sign in"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Welcome back to RouKey"})]}),f&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:f})}),(0,a.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your email"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:h?"text":"password",autoComplete:"current-password",required:!0,value:t,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your password"}),(0,a.jsx)("button",{type:"button",onClick:()=>p(!h),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors",children:h?(0,a.jsx)(c.A,{className:"h-5 w-5"}):(0,a.jsx)(o.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("button",{type:"button",onClick:()=>{v(e),C(""),w(!0)},className:"text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors",children:"Forgot your password?"})}),(0,a.jsx)("button",{type:"submit",disabled:b,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:b?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Signing in..."]}):"Sign in"})]}),(0,a.jsx)("div",{className:"lg:hidden text-center",children:(0,a.jsxs)("p",{className:"text-gray-600 text-sm",children:["Don't have an account?"," ",(0,a.jsx)(l(),{href:"/auth/signup",className:"text-blue-600 hover:text-blue-500 font-medium transition-colors",children:"Sign up"})]})})]})}),j&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Reset Password"}),(0,a.jsx)("button",{onClick:()=>w(!1),className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(d.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("form",{onSubmit:W,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"resetEmail",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email address"}),(0,a.jsx)("input",{id:"resetEmail",type:"email",required:!0,value:N,onChange:e=>v(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your email"})]}),S&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:S})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>w(!1),className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:k,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:k?"Sending...":"Send Reset Email"})]})]})]})})]})}function p(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,a.jsx)(h,{})})}},96141:(e,s,t)=>{Promise.resolve().then(t.bind(t,65594))}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,5738,9968,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(96141)),_N_E=e.O()}]);