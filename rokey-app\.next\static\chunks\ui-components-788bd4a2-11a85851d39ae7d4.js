"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5313],{35020:(e,s,t)=>{t.d(s,{A:()=>i});var r=t(95155),l=t(12115),a=t(46813),n=t(76351);function i(e){let{workflowId:s,workflowName:t,isOpen:i,onClose:o}=e,[d,c]=(0,l.useState)([]),[x,m]=(0,l.useState)(!1),[h,p]=(0,l.useState)(""),[u,g]=(0,l.useState)("view"),[b,y]=(0,l.useState)(!1),[f,v]=(0,l.useState)(""),[j,N]=(0,l.useState)(null);(0,l.useEffect)(()=>{i&&w()},[i,s]);let w=async()=>{m(!0);try{let e=await fetch("/api/manual-build/workflows/".concat(s,"/shares"));if(e.ok){let s=await e.json();c(s.shares||[])}}catch(e){}finally{m(!1)}},k=async()=>{if(h||b)try{(await fetch("/api/manual-build/workflows/".concat(s,"/shares"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sharedWith:h?[h]:void 0,permissionLevel:u,isPublic:b,expiresAt:f?new Date(f).toISOString():void 0})})).ok&&(p(""),y(!1),v(""),await w())}catch(e){}},C=async e=>{try{(await fetch("/api/manual-build/workflows/".concat(s,"/shares/").concat(e),{method:"DELETE"})).ok&&await w()}catch(e){}},I=async e=>{let s="".concat(window.location.origin,"/manual-build/shared/").concat(e);try{await navigator.clipboard.writeText(s),N(e),setTimeout(()=>N(null),2e3)}catch(e){}},S=e=>{switch(e){case"admin":return(0,r.jsx)(a.Zu,{className:"w-4 h-4 text-red-400"});case"edit":return(0,r.jsx)(a.R2,{className:"w-4 h-4 text-yellow-400"});default:return(0,r.jsx)(a.bM,{className:"w-4 h-4 text-blue-400"})}},A=e=>{switch(e){case"admin":return"text-red-400 bg-red-900/20 border-red-700/30";case"edit":return"text-yellow-400 bg-yellow-900/20 border-yellow-700/30";default:return"text-blue-400 bg-blue-900/20 border-blue-700/30"}};return i?(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-700",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(a.li,{className:"w-6 h-6 text-[#ff6b35]"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:"Share Workflow"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:t})]})]}),(0,r.jsx)("button",{onClick:o,className:"text-gray-400 hover:text-white transition-colors",children:(0,r.jsx)(a.fK,{className:"w-6 h-6"})})]}),(0,r.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Create New Share"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",name:"shareType",checked:!b,onChange:()=>y(!1),className:"text-[#ff6b35] focus:ring-[#ff6b35]"}),(0,r.jsx)(a.K6,{className:"w-5 h-5 text-gray-400"}),(0,r.jsx)("span",{className:"text-white",children:"Share with specific users"})]}),(0,r.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",name:"shareType",checked:b,onChange:()=>y(!0),className:"text-[#ff6b35] focus:ring-[#ff6b35]"}),(0,r.jsx)(a.mS,{className:"w-5 h-5 text-gray-400"}),(0,r.jsx)("span",{className:"text-white",children:"Public link"})]})]}),!b&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,r.jsx)("input",{type:"email",value:h,onChange:e=>p(e.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Permission Level"}),(0,r.jsxs)("select",{value:u,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,r.jsx)("option",{value:"view",children:"View Only"}),(0,r.jsx)("option",{value:"edit",children:"Can Edit"}),(0,r.jsx)("option",{value:"admin",children:"Admin Access"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Expires At (Optional)"}),(0,r.jsx)("input",{type:"datetime-local",value:f,onChange:e=>v(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,r.jsx)("button",{onClick:k,disabled:!h&&!b,className:"w-full px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Create Share Link"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Active Shares"}),x?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("div",{className:"text-gray-400",children:"Loading shares..."})}):0===d.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(a.li,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),(0,r.jsx)("div",{className:"text-gray-400",children:"No active shares"})]}):(0,r.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,r.jsx)("div",{className:"bg-gray-700/50 border border-gray-600 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 flex-1",children:[e.is_public?(0,r.jsx)(a.mS,{className:"w-5 h-5 text-green-400"}):(0,r.jsx)(a.K6,{className:"w-5 h-5 text-blue-400"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("span",{className:"text-white font-medium",children:e.is_public?"Public Link":e.shared_with||"Unknown User"}),(0,r.jsxs)("span",{className:"px-2 py-0.5 text-xs rounded-full border ".concat(A(e.permission_level)),children:[S(e.permission_level),(0,r.jsx)("span",{className:"ml-1 capitalize",children:e.permission_level})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-400",children:[(0,r.jsxs)("span",{children:["Created ",new Date(e.created_at).toLocaleDateString()]}),e.expires_at&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(a.CT,{className:"w-3 h-3"}),"Expires ",new Date(e.expires_at).toLocaleDateString()]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>I(e.share_token),className:"p-2 text-gray-400 hover:text-white transition-colors",title:"Copy share link",children:j===e.share_token?(0,r.jsx)(n.S,{className:"w-4 h-4 text-green-400"}):(0,r.jsx)(a.Xx,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>C(e.id),className:"p-2 text-gray-400 hover:text-red-400 transition-colors",title:"Revoke share",children:(0,r.jsx)(a.uc,{className:"w-4 h-4"})})]})]})},e.id))})]})]}),(0,r.jsx)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-700",children:(0,r.jsx)("button",{onClick:o,className:"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Close"})})]})}):null}},63768:(e,s,t)=>{t.d(s,{A:()=>n});var r=t(95155),l=t(12115),a=t(9266);function n(e){let{workflow:s,isDirty:t,isSaving:n,onSave:i,onExecute:o,onBack:d,onShare:c}=e,[x,m]=(0,l.useState)(!1);return(0,r.jsxs)("div",{className:"bg-gray-900/80 backdrop-blur-sm border-b border-gray-700/50 px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("button",{onClick:d,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50",title:"Back to workflows",children:(0,r.jsx)(a.A6,{className:"w-5 h-5"})}),(0,r.jsx)("div",{className:"h-6 w-px bg-gray-700"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-white",children:(null==s?void 0:s.name)||"New Workflow"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-400",children:[t&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"Unsaved changes"]}),(null==s?void 0:s.updated_at)&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(a.O4,{className:"w-3 h-3"}),"Last saved ",new Date(s.updated_at).toLocaleTimeString()]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>m(!x),className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50",title:"Workflow settings",children:(0,r.jsx)(a.Vy,{className:"w-5 h-5"})}),(0,r.jsx)("button",{onClick:c,disabled:!(null==s?void 0:s.id)||!c,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50 disabled:opacity-50 disabled:cursor-not-allowed",title:"Share workflow",children:(0,r.jsx)(a.li,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("button",{onClick:i,disabled:n||!t,className:"px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ".concat(t&&!n?"bg-blue-600 hover:bg-blue-500 text-white":"bg-gray-700 text-gray-400 cursor-not-allowed"),children:[(0,r.jsx)(a.py,{className:"w-4 h-4"}),n?"Saving...":"Save"]}),(0,r.jsxs)("button",{onClick:()=>{(null==s?void 0:s.id)?window.open("/playground/workflows","_blank"):alert("Please save the workflow first to test it in the playground")},className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2",children:[(0,r.jsx)(a.ud,{className:"w-4 h-4"}),"Test in Playground"]})]})]}),x&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700/50",children:[(0,r.jsx)("h3",{className:"text-white font-medium mb-3",children:"Workflow Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Max Execution Time"}),(0,r.jsxs)("select",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,r.jsx)("option",{value:"300",children:"5 minutes"}),(0,r.jsx)("option",{value:"600",children:"10 minutes"}),(0,r.jsx)("option",{value:"1800",children:"30 minutes"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Retry Count"}),(0,r.jsxs)("select",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,r.jsx)("option",{value:"1",children:"1 retry"}),(0,r.jsx)("option",{value:"3",children:"3 retries"}),(0,r.jsx)("option",{value:"5",children:"5 retries"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Options"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0",defaultChecked:!0}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable memory"})]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0",defaultChecked:!0}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable streaming"})]})]})]})]})]})]})}},82087:(e,s,t)=>{t.d(s,{c_:()=>C});var r=t(95155),l=t(76653),a=t(61772);function n(e){let{data:s,children:t,icon:l,color:n="#ff6b35",hasInput:i=!0,hasOutput:o=!0,hasRoleInput:d=!1,hasToolsInput:c=!1,hasBrowsingInput:x=!1,inputLabel:m="Input",outputLabel:h="Output",roleInputLabel:p="Role",toolsInputLabel:u="Tools",browsingInputLabel:g="Browse",inputHandles:b=[],className:y=""}=e,f=s.isConfigured,v=s.hasError;return(0,r.jsxs)("div",{className:"relative ".concat(y),children:[i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"input",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:m})]}),d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"role",className:"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors",style:{left:-12,top:"30%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none",style:{left:-50,top:"25%"},children:p})]}),c&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"tools",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"70%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-50,top:"65%"},children:u})]}),x&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"browsing",className:"w-6 h-6 border-2 border-cyan-500 bg-cyan-700 hover:border-cyan-400 hover:bg-cyan-400 transition-colors",style:{left:-12,top:"85%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-cyan-200 font-medium pointer-events-none",style:{left:-50,top:"80%"},children:g})]}),b&&b.length>0&&b.map((e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:e.id,className:"w-8 h-8 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors cursor-pointer rounded-full",style:{left:-16,top:0===s?"30%":"60%",zIndex:10}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none whitespace-nowrap",style:{left:-70,top:0===s?"25%":"55%",zIndex:5},children:e.label})]},e.id)),(0,r.jsxs)("div",{className:"min-w-[200px] rounded-lg border-2 transition-all duration-200 ".concat(v?"border-red-500 bg-red-900/20":f?"border-gray-600 bg-gray-800/90":"border-yellow-500 bg-yellow-900/20"," backdrop-blur-sm shadow-lg hover:shadow-xl"),style:{borderColor:v?"#ef4444":f?n:"#eab308"},children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3",style:{background:v?"linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))":"linear-gradient(135deg, ".concat(n,"20, ").concat(n,"10)")},children:[l&&(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{backgroundColor:v?"#ef444420":"".concat(n,"20"),color:v?"#ef4444":n},children:(0,r.jsx)(l,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:s.label}),s.description&&(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:s.description})]}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:v?(0,r.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full",title:"Error"}):f?(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full",title:"Configured"}):(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full",title:"Needs configuration"})})]}),t&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50",children:t}),v&&s.errorMessage&&(0,r.jsx)("div",{className:"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg",children:(0,r.jsx)("div",{className:"text-xs text-red-300",children:s.errorMessage})})]}),o&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"source",position:a.yX.Right,className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:h})]})]})}var i=t(43194),o=t(40705);let d={openai:"#10b981",anthropic:"#f97316",google:"#3b82f6",deepseek:"#8b5cf6",xai:"#374151",openrouter:"linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)"},c={openai:"OpenAI",anthropic:"Anthropic",google:"Google",deepseek:"DeepSeek",xai:"xAI (Grok)",openrouter:"OpenRouter"};var x=t(11845);let m={openai:"#10b981",anthropic:"#f97316",google:"#3b82f6",deepseek:"#8b5cf6",xai:"#374151",openrouter:"linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)"},h={openai:"OpenAI",anthropic:"Anthropic",google:"Google",deepseek:"DeepSeek",xai:"xAI (Grok)",openrouter:"OpenRouter"};var p=t(43914),u=t(89464),g=t(1335),b=t(89136),y=t(48729);let f={google_drive:"\uD83D\uDCC1",google_docs:"\uD83D\uDCC4",zapier:"⚡",notion:"\uD83D\uDCDD",google_sheets:"\uD83D\uDCCA",calendar:"\uD83D\uDCC5",gmail:"\uD83D\uDCE7",youtube:"\uD83D\uDCFA",supabase:"\uD83D\uDDC4️"},v={google_drive:"Google Drive",google_docs:"Google Docs",zapier:"Zapier",notion:"Notion",google_sheets:"Google Sheets",calendar:"Calendar",gmail:"Gmail",youtube:"YouTube",supabase:"Supabase"};var j=t(27850),N=t(12115),w=t(57561),k=t(46780);let C={userRequest:function(e){let{data:s}=e;return(0,r.jsx)(n,{data:s,icon:l.n,color:"#10b981",hasInput:!1,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Entry point for user input"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"This node captures the initial user request and starts the workflow execution."})]})})},classifier:function(e){var s;let{data:t}=e;return(0,r.jsx)(n,{data:t,icon:i.Y,color:"#3b82f6",hasInput:!0,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"AI-powered request analysis"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Analyzes the user request to determine the best routing strategy and extract key information."}),(null==(s=t.config)?void 0:s.classifierType)&&(0,r.jsxs)("div",{className:"mt-2 px-2 py-1 bg-blue-900/30 rounded text-xs text-blue-300",children:["Type: ",t.config.classifierType]})]})})},provider:function(e){let{data:s,id:t}=e,l=(0,a.Yu)(),i=(0,a.pk)(),x=s.config,m=null==x?void 0:x.providerId,h=null==x?void 0:x.modelId,p=m?d[m]:"#ff6b35",u=m?c[m]:"AI Provider",g=l.filter(e=>e.target===t&&"role"===e.targetHandle).map(e=>{let s=i.find(s=>s.id===e.source);if(s&&"roleAgent"===s.type){let t=s.data.config;return{id:e.source,name:(null==t?void 0:t.roleName)||s.data.label||"Unknown Role",type:(null==t?void 0:t.roleType)||"predefined"}}return null}).filter(Boolean);return(0,r.jsx)(n,{data:s,icon:o.h,color:"string"==typeof p?p:"#ff6b35",hasInput:!1,hasOutput:!0,hasRoleInput:!0,hasToolsInput:!1,hasBrowsingInput:!0,children:(0,r.jsx)("div",{className:"space-y-3",children:m?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:u}),"openrouter"===m&&(0,r.jsx)("span",{className:"text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-0.5 rounded-full",children:"300+ Models"})]}),h&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Model: ",h]}),(null==x?void 0:x.parameters)&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-gray-400",children:["Temp: ",x.parameters.temperature||1]}),(0,r.jsxs)("div",{className:"text-gray-400",children:["Max: ",x.parameters.maxTokens||"Auto"]})]}),(null==x?void 0:x.fallbackProvider)&&(0,r.jsxs)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:["Fallback: ",c[x.fallbackProvider.providerId]]}),g.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Roles:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:g.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"AI Provider Connection"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure to connect to OpenAI, Anthropic, Google, DeepSeek, xAI, or OpenRouter."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},vision:function(e){let{data:s,id:t}=e,l=(0,a.Yu)(),i=(0,a.pk)(),o=s.config,d=null==o?void 0:o.providerId,c=null==o?void 0:o.modelId,p=d?m[d]:"#8b5cf6",u=d?h[d]:"Vision AI",g=l.filter(e=>e.target===t&&"role"===e.targetHandle).map(e=>{let s=i.find(s=>s.id===e.source);if(s&&"roleAgent"===s.type){let t=s.data.config;return{id:e.source,name:(null==t?void 0:t.roleName)||s.data.label||"Unknown Role",type:(null==t?void 0:t.roleType)||"predefined"}}return null}).filter(Boolean);return(0,r.jsx)(n,{data:s,icon:x.b,color:"string"==typeof p?p:"#8b5cf6",hasInput:!1,hasOutput:!0,hasRoleInput:!0,hasToolsInput:!1,children:(0,r.jsx)("div",{className:"space-y-3",children:d?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:u}),(0,r.jsx)("span",{className:"text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-0.5 rounded-full",children:"Vision"})]}),c&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Model: ",c]}),(null==o?void 0:o.parameters)&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-gray-400",children:["Temp: ",o.parameters.temperature||1]}),(0,r.jsxs)("div",{className:"text-gray-400",children:["Max: ",o.parameters.maxTokens||"Auto"]})]}),(null==o?void 0:o.fallbackProvider)&&(0,r.jsxs)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:["Fallback: ",h[o.fallbackProvider.providerId]]}),g.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Roles:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:g.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Vision AI Connection"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure to connect to multimodal AI models for image analysis and vision tasks."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},output:function(e){var s;let{data:t}=e;return(0,r.jsx)(n,{data:t,icon:p.A,color:"#ef4444",hasInput:!0,hasOutput:!1,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Final response output"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"This node formats and delivers the final response to the user. Every workflow must end here."}),(null==(s=t.config)?void 0:s.outputFormat)&&(0,r.jsxs)("div",{className:"mt-2 px-2 py-1 bg-red-900/30 rounded text-xs text-red-300",children:["Format: ",t.config.outputFormat]})]})})},roleAgent:function(e){let{data:s}=e,t=s.config,l=null==t?void 0:t.roleName,a=(null==t?void 0:t.tools)||[];return(0,r.jsx)(n,{data:s,icon:u.K,color:"#8b5cf6",hasInput:!1,hasOutput:!0,outputLabel:"Role",children:(0,r.jsx)("div",{className:"space-y-3",children:l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-white",children:l}),(null==t?void 0:t.customPrompt)&&(0,r.jsx)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:"Custom prompt configured"}),a.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Tools:"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.slice(0,3).map((e,s)=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded",children:e},s)),a.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:["+",a.length-3," more"]})]})]}),(null==t?void 0:t.memoryEnabled)&&(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✓ Memory enabled"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Role Plugin"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Connect to AI Provider nodes to assign specialized roles (e.g., Coder, Writer, Analyst)."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},centralRouter:function(e){let{data:s,id:t}=e,l=(0,a.Yu)(),n=(0,a.pk)(),o=s.config,d=l.filter(e=>e.target===t&&"providers"===e.targetHandle).map(e=>{let s=n.find(s=>s.id===e.source);if(s&&"provider"===s.type){let t=s.data.config;return{id:e.source,name:(null==t?void 0:t.providerId)||"Unknown Provider",model:(null==t?void 0:t.modelId)||"Unknown Model"}}return null}).filter(Boolean),c=l.filter(e=>e.target===t&&"vision"===e.targetHandle).map(e=>{let s=n.find(s=>s.id===e.source);if(s&&"vision"===s.type){let t=s.data.config;return{id:e.source,name:(null==t?void 0:t.providerId)||"Unknown Vision Provider",model:(null==t?void 0:t.modelId)||"Unknown Model"}}return null}).filter(Boolean),x=l.filter(e=>e.target===t&&"tools"===e.targetHandle).map(e=>{let s=n.find(s=>s.id===e.source);if(s&&"tool"===s.type){let t=s.data.config;return{id:e.source,name:(null==t?void 0:t.toolType)||"Unknown Tool",status:(null==t?void 0:t.connectionStatus)||"disconnected"}}return null}).filter(Boolean),m=l.some(e=>e.target===t&&"classifier"===e.targetHandle&&n.find(s=>s.id===e.source&&"classifier"===s.type));return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"providers",className:"w-6 h-6 border-2 border-blue-500 bg-blue-700 hover:border-blue-400 hover:bg-blue-400 transition-colors",style:{left:-12,top:"20%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-blue-200 font-medium pointer-events-none",style:{left:-80,top:"15%"},children:"AI Providers"}),(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"vision",className:"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors",style:{left:-12,top:"50%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none",style:{left:-80,top:"45%"},children:"Vision Models"}),(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"classifier",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"65%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-80,top:"60%"},children:"Classifier"}),(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"tools",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"75%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-50,top:"70%"},children:"Tools"}),(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"memory",className:"w-6 h-6 border-2 border-yellow-500 bg-yellow-700 hover:border-yellow-400 hover:bg-yellow-400 transition-colors",style:{left:-12,top:"90%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-yellow-200 font-medium pointer-events-none",style:{left:-60,top:"85%"},children:"Memory"}),(0,r.jsx)(a.h7,{type:"source",position:a.yX.Right,id:"output",className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:"Output"}),(0,r.jsxs)("div",{className:"min-w-[280px] rounded-lg border-2 transition-all duration-200 ".concat(s.hasError?"border-red-500 bg-red-900/20":s.isConfigured?"border-gray-600 bg-gray-800/90":"border-yellow-500 bg-yellow-900/20"," backdrop-blur-sm shadow-lg hover:shadow-xl"),style:{borderColor:s.hasError?"#ef4444":s.isConfigured?"#ff6b35":"#eab308"},children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3",style:{background:s.hasError?"linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))":"linear-gradient(135deg, #ff6b3520, #ff6b3510)"},children:[(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{backgroundColor:s.hasError?"#ef444420":"#ff6b3520",color:s.hasError?"#ef4444":"#ff6b35"},children:(0,r.jsx)(i.Y,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:"Central Router"}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Smart routing hub for AI providers"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[m&&(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full",title:"Classifier Connected"}),d.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full",title:"".concat(d.length," AI Providers")}),c.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full",title:"".concat(c.length," Vision Models")}),x.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-cyan-500 rounded-full",title:"".concat(x.length," Tools")})]})]}),(0,r.jsxs)("div",{className:"px-4 py-3 space-y-3",children:[d.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["AI Providers (",d.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:d.map(e=>(0,r.jsx)("span",{className:"text-xs bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full border border-blue-700/30",children:e.name},e.id))})]}),c.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Vision Models (",c.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:c.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]}),x.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Tools (",x.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:x.map(e=>(0,r.jsx)("span",{className:"text-xs px-2 py-0.5 rounded-full border ".concat("connected"===e.status?"bg-green-900/30 text-green-300 border-green-700/30":"bg-yellow-900/30 text-yellow-300 border-yellow-700/30"),children:e.name},e.id))})]}),(null==o?void 0:o.routingStrategy)&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Strategy: ",o.routingStrategy.replace("_"," ").toUpperCase()]}),!m&&(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Connect classifier for smart routing"}),0===d.length&&0===c.length&&(0,r.jsx)("div",{className:"text-xs text-red-300 bg-red-900/20 px-2 py-1 rounded",children:"❌ No AI providers connected"}),x.length>0&&(0,r.jsxs)("div",{className:"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded",children:["\uD83D\uDD27 ",x.length," tool",x.length>1?"s":""," available"]}),(d.length>0||c.length>0)&&m&&(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✅ Ready for smart routing"})]})]})]})},conditional:function(e){let{data:s}=e,t=s.config,l=null==t?void 0:t.condition,n=null==t?void 0:t.conditionType;return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:"Input"}),(0,r.jsxs)("div",{className:"min-w-[200px] rounded-lg border-2 border-amber-500 bg-amber-900/20 backdrop-blur-sm shadow-lg",children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-amber-500/20 to-amber-600/10",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-amber-500/20 text-amber-500",children:(0,r.jsx)(g.r,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:s.label}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Branch workflow based on conditions"})]}),(0,r.jsx)("div",{className:"w-2 h-2 bg-amber-500 rounded-full"})]}),(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50 space-y-3",children:l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-300",children:["Condition: ",n||"custom"]}),(0,r.jsx)("div",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded font-mono",children:l}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-green-300",children:["True: ",(null==t?void 0:t.trueLabel)||"Continue"]}),(0,r.jsxs)("div",{className:"text-red-300",children:["False: ",(null==t?void 0:t.falseLabel)||"Skip"]})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Conditional Logic"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure conditions to branch your workflow into different paths."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})]}),(0,r.jsx)(a.h7,{type:"source",position:a.yX.Right,id:"true",className:"w-6 h-6 border-2 border-green-500 bg-green-600 hover:border-green-400 hover:bg-green-500 transition-colors",style:{right:-12,top:"40%"}}),(0,r.jsx)(a.h7,{type:"source",position:a.yX.Right,id:"false",className:"w-6 h-6 border-2 border-red-500 bg-red-600 hover:border-red-400 hover:bg-red-500 transition-colors",style:{right:-12,top:"60%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-300 font-medium pointer-events-none",style:{right:-50,top:"35%"},children:"True"}),(0,r.jsx)("div",{className:"absolute text-xs text-red-300 font-medium pointer-events-none",style:{right:-50,top:"55%"},children:"False"})]})},merge:function(e){let{data:s}=e;return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"input1",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12,top:"30%"}}),(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"input2",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12,top:"50%"}}),(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,id:"input3",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12,top:"70%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"25%"},children:"Input 1"}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:"Input 2"}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"65%"},children:"Input 3"}),(0,r.jsxs)("div",{className:"min-w-[200px] rounded-lg border-2 border-cyan-500 bg-cyan-900/20 backdrop-blur-sm shadow-lg",children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-cyan-500/20 to-cyan-600/10",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-cyan-500/20 text-cyan-500",children:(0,r.jsx)(b.E,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:s.label}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Combine multiple inputs"})]}),(0,r.jsx)("div",{className:"w-2 h-2 bg-cyan-500 rounded-full"})]}),(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50",children:(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Merges outputs from multiple nodes into a single stream for further processing."})})]}),(0,r.jsx)(a.h7,{type:"source",position:a.yX.Right,className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:"Output"})]})},loop:function(e){let{data:s}=e,t=s.config,l=null==t?void 0:t.loopType,a=null==t?void 0:t.maxIterations;return(0,r.jsx)(n,{data:s,icon:b.E,color:"#f59e0b",hasInput:!0,hasOutput:!0,children:(0,r.jsx)("div",{className:"space-y-2",children:l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-300",children:["Type: ",l]}),a&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max iterations: ",a]}),(0,r.jsx)("div",{className:"text-xs text-amber-300 bg-amber-900/20 px-2 py-1 rounded",children:"⚠️ Use with caution - can cause infinite loops"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Loop Operations"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Repeat operations based on conditions or iterations."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},tool:function(e){let{data:s}=e,t=s.config,l=null==t?void 0:t.toolType,a=l?f[l]:"\uD83D\uDD27",i=l?v[l]:"External Tool",o=(null==t?void 0:t.connectionStatus)||"disconnected",d=()=>{switch(o){case"connected":return"text-green-400";case"error":return"text-red-400";default:return"text-yellow-400"}};return(0,r.jsx)(n,{data:s,icon:y.j,color:"#06b6d4",hasInput:!0,hasOutput:!0,children:(0,r.jsx)("div",{className:"space-y-3",children:l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-lg",children:a}),(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:i})]}),(0,r.jsx)("div",{className:"text-xs ".concat(d()),children:"●"})]}),(0,r.jsx)("div",{className:"text-xs ".concat(d()),children:(()=>{switch(o){case"connected":return"Connected";case"error":return"Error";default:return"Not Connected"}})()}),(null==t?void 0:t.timeout)&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Timeout: ",t.timeout,"s"]}),(0,r.jsx)("div",{className:"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded",children:"✓ Tool configured"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"External Tool Integration"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Connect to external services like Google Drive, databases, APIs, or browser automation."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},memory:function(e){let{data:s,id:t}=e,l=s.config,i=null==l?void 0:l.memoryName,o=(null==l?void 0:l.maxSize)||10240,d=(null==l?void 0:l.encryption)!==!1,c=(0,a.Yu)().filter(e=>e.source===t).map(e=>e.target),[x,m]=(0,N.useState)(null);return(0,N.useEffect)(()=>{i&&m({entriesCount:Math.floor(50*Math.random())+1,totalSize:"".concat((o/1024*Math.random()).toFixed(1),"MB"),lastUpdate:new Date().toLocaleTimeString()})},[i,o]),(0,r.jsx)(n,{data:s,icon:j.O,color:"#ec4899",hasInput:!0,hasOutput:!0,children:(0,r.jsx)("div",{className:"space-y-3",children:i?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-white",children:["\uD83E\uDDE0 ",i]}),(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Max: ",Math.round(o/1024),"MB | ",d?"\uD83D\uDD12 Encrypted":"\uD83D\uDD13 Plain"]}),x&&(0,r.jsxs)("div",{className:"text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded space-y-1",children:[(0,r.jsxs)("div",{children:["\uD83D\uDCCA ",x.entriesCount," entries"]}),(0,r.jsxs)("div",{children:["\uD83D\uDCBE ",x.totalSize," used"]}),(0,r.jsxs)("div",{children:["\uD83D\uDD52 Updated ",x.lastUpdate]})]}),c.length>0&&(0,r.jsxs)("div",{className:"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded",children:["\uD83D\uDD17 Connected to ",c.length," node",c.length>1?"s":""]}),(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✅ Active & Ready"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"\uD83E\uDDE0 Plug & Play Memory"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Intelligent memory brain for connected nodes. Automatically handles storage, retrieval, and persistence."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},switch:function(e){let{data:s}=e,t=s.config,l=null==t?void 0:t.switchType,n=(null==t?void 0:t.cases)||[];return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.h7,{type:"target",position:a.yX.Left,className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:"Input"}),(0,r.jsxs)("div",{className:"min-w-[200px] rounded-lg border-2 border-indigo-500 bg-indigo-900/20 backdrop-blur-sm shadow-lg",children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-indigo-500/20 to-indigo-600/10",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-indigo-500/20 text-indigo-500",children:(0,r.jsx)(w.D,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:s.label}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Route to different paths"})]}),(0,r.jsx)("div",{className:"w-2 h-2 bg-indigo-500 rounded-full"})]}),(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50 space-y-3",children:l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-300",children:["Type: ",l]}),n.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Cases:"}),n.slice(0,3).map((e,s)=>(0,r.jsx)("div",{className:"text-xs bg-indigo-900/30 text-indigo-300 px-2 py-0.5 rounded",children:e.label||"Case ".concat(s+1)},s)),n.length>3&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["+",n.length-3," more cases"]})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Switch Router"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Route workflow to different paths based on input values or conditions."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})]}),n.length>0?n.slice(0,4).map((e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsx)(a.h7,{type:"source",position:a.yX.Right,id:"case-".concat(s),className:"w-6 h-6 border-2 border-indigo-500 bg-indigo-600 hover:border-indigo-400 hover:bg-indigo-500 transition-colors",style:{right:-12,top:"".concat(30+15*s,"%")}}),(0,r.jsxs)("div",{className:"absolute text-xs text-indigo-300 font-medium pointer-events-none",style:{right:-60,top:"".concat(25+15*s,"%")},children:["Case ",s+1]})]},s)):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"source",position:a.yX.Right,className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:"Output"})]})]})},planner:function(e){var s;let{data:t}=e,l=t.config,a=null==l?void 0:l.providerId,o=null==l?void 0:l.modelId,d=(null==l?void 0:l.maxSubtasks)||10;return(0,r.jsx)(n,{data:t,icon:i.Y,color:"#8b5cf6",hasInput:!1,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-3",children:[a&&o?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:{openai:"OpenAI",anthropic:"Anthropic",google:"Google",groq:"Groq",deepseek:"DeepSeek",openrouter:"OpenRouter"}[a]||a}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:(e=>{if(!e)return"";let s=e.split("/");return s[s.length-1]})(o)})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max subtasks: ",d]}),(null==l||null==(s=l.parameters)?void 0:s.temperature)!==void 0&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Temperature: ",l.parameters.temperature]})]}):(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Configure AI model for planning"}),(0,r.jsx)("div",{className:"text-xs text-purple-300 bg-purple-900/30 px-2 py-1 rounded",children:"\uD83D\uDCCB Planning Agent"})]})})},browsing:function(e){var s,t;let{data:l}=e,a=l.config,i=(null==a?void 0:a.maxSites)||5,o=(null==a?void 0:a.timeout)||30,d=null==(s=null==a?void 0:a.enableScreenshots)||s,c=null==(t=null==a?void 0:a.enableFormFilling)||t,x=(null==a?void 0:a.searchEngines)||["google"],m=()=>{let e=[];return d&&e.push("\uD83D\uDCF8 Screenshots"),c&&e.push("\uD83D\uDCDD Forms"),(null==a?void 0:a.enableCaptchaSolving)&&e.push("\uD83D\uDD10 CAPTCHAs"),e};return(0,r.jsx)(n,{data:l,icon:k.m,color:"#10b981",hasInput:!1,hasOutput:!0,inputHandles:[{id:"plan",label:"Plan",position:"left"},{id:"memory",label:"Memory",position:"left"}],children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:"Intelligent Browsing"}),(0,r.jsx)("div",{className:"text-xs text-green-400",children:"● Ready"})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max sites: ",i," | Timeout: ",o,"s"]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Engines: ",x.join(", ")]}),m().length>0&&(0,r.jsx)("div",{className:"text-xs text-gray-400",children:m().join(" • ")}),(null==a?void 0:a.maxDepth)&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max depth: ",a.maxDepth," levels"]})]}),(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded",children:"\uD83C\uDF10 Autonomous Agent"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Requires: Planner + Memory inputs"})]})})}}}}]);