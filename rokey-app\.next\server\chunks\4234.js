exports.id=4234,exports.ids=[4234],exports.modules={750:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});class a{constructor(){this.apiKeys=[],this.currentKeyIndex=0,this.keyUsageCount=new Map,this.keyErrors=new Map,this.MAX_RETRIES=3,this.ERROR_THRESHOLD=5,this.ENDPOINT="https://production-sfo.browserless.io",this.initializeKeys()}static getInstance(){return a.instance||(a.instance=new a),a.instance}initializeKeys(){let e=process.env.BROWSERLESS_API_KEYS;e&&(this.apiKeys=e.split(",").map(e=>e.trim()).filter(Boolean),this.apiKeys.forEach(e=>{this.keyUsageCount.set(e,0),this.keyErrors.set(e,0)}))}getNextApiKey(){if(0===this.apiKeys.length)throw Error("No Browserless API keys available");let e=this.apiKeys[0],t=this.calculateKeyScore(e);for(let s of this.apiKeys){let a=this.calculateKeyScore(s);a<t&&(e=s,t=a)}return e}calculateKeyScore(e){return(this.keyUsageCount.get(e)||0)+10*(this.keyErrors.get(e)||0)}incrementKeyUsage(e){let t=this.keyUsageCount.get(e)||0;this.keyUsageCount.set(e,t+1)}incrementKeyError(e){let t=this.keyErrors.get(e)||0;this.keyErrors.set(e,t+1)}isKeyHealthy(e){return(this.keyErrors.get(e)||0)<this.ERROR_THRESHOLD}getHealthyKeys(){return this.apiKeys.filter(e=>this.isKeyHealthy(e))}async executeFunction(e,t,s){0===this.getHealthyKeys().length&&(this.keyErrors.clear(),this.apiKeys.forEach(e=>this.keyErrors.set(e,0)));let a=null;for(let r=0;r<this.MAX_RETRIES;r++)try{let a=this.getNextApiKey();return this.incrementKeyUsage(a),await this.makeRequest(a,e,t,s)}catch(e){if(a=e,this.isRateLimitError(e)){let e=this.getNextApiKey();this.incrementKeyError(e)}}throw a||Error("All Browserless API attempts failed")}async makeRequest(e,t,s,a){let r=`${this.ENDPOINT}/function?token=${e}`,o=s?{code:t,context:s}:t,i={"Content-Type":s?"application/json":"application/javascript","User-Agent":a?.userAgent||"RouKey-Browser-Agent/1.0"},c=await fetch(r,{method:"POST",headers:i,body:s?JSON.stringify(o):t,signal:AbortSignal.timeout(a?.timeout||3e4)});if(!c.ok){let e=await c.text();throw Error(`Browserless API error: ${c.status} - ${e}`)}return await c.json()}isRateLimitError(e){let t=e.message.toLowerCase();return t.includes("rate limit")||t.includes("quota")||t.includes("429")||t.includes("too many requests")}async navigateAndExtract(e,t){let s=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });
        
        const title = await page.title();
        const content = ${t?`await page.$eval("${t}", el => el.textContent || el.innerText)`:"await page.evaluate(() => document.body.innerText)"};
        
        return {
          data: {
            url: "${e}",
            title,
            content: content?.trim() || ""
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(s)}async searchAndExtract(e,t="google"){let s="google"===t?`https://www.google.com/search?q=${encodeURIComponent(e)}`:`https://www.bing.com/search?q=${encodeURIComponent(e)}`,a=`
      export default async function ({ page }) {
        await page.goto("${s}", { waitUntil: 'networkidle0' });
        
        // Wait for search results to load
        await page.waitForSelector('${"google"===t?"[data-ved]":".b_algo"}', { timeout: 10000 });
        
        const results = await page.evaluate(() => {
          const selector = '${"google"===t?"[data-ved] h3":".b_algo h2"}';
          const elements = document.querySelectorAll(selector);
          
          return Array.from(elements).slice(0, 5).map(el => ({
            title: el.textContent?.trim() || '',
            link: el.closest('a')?.href || ''
          }));
        });
        
        return {
          data: {
            query: "${e}",
            searchEngine: "${t}",
            results
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(a)}async takeScreenshot(e,t){let s=t?.fullPage??!1,a=t?.selector||"",r=t?.quality||80,o=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        let screenshot;
        if ("${a}") {
          // Screenshot specific element
          const element = await page.waitForSelector("${a}", { timeout: 10000 });
          screenshot = await element.screenshot({
            encoding: 'base64',
            type: 'png'
          });
        } else {
          // Screenshot full page or viewport
          screenshot = await page.screenshot({
            encoding: 'base64',
            fullPage: ${s},
            type: 'png',
            quality: ${r}
          });
        }

        return {
          data: {
            url: "${e}",
            screenshot: screenshot,
            selector: "${a}",
            fullPage: ${s},
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(o)}async fillForm(e,t,s){let a=s?.submitAfterFill??!1,r=s?.waitForNavigation??!1,o=s?.formSelector||"form",i=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        const formData = ${JSON.stringify(t)};
        const results = [];

        // Wait for form to be present
        await page.waitForSelector("${o}", { timeout: 10000 });

        // Fill each field intelligently
        for (const [fieldName, value] of Object.entries(formData)) {
          try {
            // Try multiple selector strategies
            const selectors = [
              \`input[name="\${fieldName}"]\`,
              \`input[id="\${fieldName}"]\`,
              \`textarea[name="\${fieldName}"]\`,
              \`select[name="\${fieldName}"]\`,
              \`input[placeholder*="\${fieldName}"]\`,
              \`input[aria-label*="\${fieldName}"]\`,
              \`[data-testid="\${fieldName}"]\`
            ];

            let filled = false;
            for (const selector of selectors) {
              const elements = await page.$$(selector);
              if (elements.length > 0) {
                const element = elements[0];
                const tagName = await element.evaluate(el => el.tagName.toLowerCase());

                if (tagName === 'select') {
                  await element.selectOption(value.toString());
                } else if (tagName === 'input') {
                  const inputType = await element.getAttribute('type');
                  if (inputType === 'checkbox' || inputType === 'radio') {
                    if (value) await element.check();
                  } else {
                    await element.fill(value.toString());
                  }
                } else {
                  await element.fill(value.toString());
                }

                results.push({
                  field: fieldName,
                  selector: selector,
                  value: value,
                  success: true
                });
                filled = true;
                break;
              }
            }

            if (!filled) {
              results.push({
                field: fieldName,
                value: value,
                success: false,
                error: 'Field not found'
              });
            }
          } catch (error) {
            results.push({
              field: fieldName,
              value: value,
              success: false,
              error: error.message
            });
          }
        }

        let submitResult = null;
        if (${a}) {
          try {
            const submitButton = await page.$('input[type="submit"], button[type="submit"], button:has-text("Submit")');
            if (submitButton) {
              ${r?"await Promise.all([page.waitForNavigation(), submitButton.click()]);":"await submitButton.click();"}
              submitResult = { success: true, message: 'Form submitted successfully' };
            } else {
              submitResult = { success: false, error: 'Submit button not found' };
            }
          } catch (error) {
            submitResult = { success: false, error: error.message };
          }
        }

        return {
          data: {
            url: "${e}",
            formFillResults: results,
            submitResult: submitResult,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(i)}async solveCaptcha(e,t="recaptcha"){let s=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        const captchaType = "${t}";
        let result = { success: false, type: captchaType };

        try {
          if (captchaType === 'recaptcha') {
            // Look for reCAPTCHA
            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');
            if (recaptcha) {
              // For now, we'll detect and report the presence
              // In production, integrate with 2captcha or similar service
              const sitekey = await recaptcha.getAttribute('data-sitekey');
              result = {
                success: false,
                type: 'recaptcha',
                detected: true,
                sitekey: sitekey,
                message: 'reCAPTCHA detected but solving not implemented yet'
              };
            }
          } else if (captchaType === 'hcaptcha') {
            // Look for hCaptcha
            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');
            if (hcaptcha) {
              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');
              result = {
                success: false,
                type: 'hcaptcha',
                detected: true,
                sitekey: sitekey,
                message: 'hCaptcha detected but solving not implemented yet'
              };
            }
          } else if (captchaType === 'text') {
            // Look for text-based CAPTCHA
            const textCaptcha = await page.$('img[src*="captcha"], img[alt*="captcha"], .captcha-image');
            if (textCaptcha) {
              result = {
                success: false,
                type: 'text',
                detected: true,
                message: 'Text CAPTCHA detected but solving not implemented yet'
              };
            }
          }

          // If no CAPTCHA detected
          if (!result.detected) {
            result = {
              success: true,
              type: captchaType,
              detected: false,
              message: 'No CAPTCHA detected on page'
            };
          }
        } catch (error) {
          result = {
            success: false,
            type: captchaType,
            error: error.message
          };
        }

        return {
          data: {
            url: "${e}",
            captchaResult: result,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(s)}async executeAdvancedScript(e,t,s){let a=s?.waitForSelector||"",r=s?.timeout||3e4,o=s?.returnType||"json",i=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        ${a?`await page.waitForSelector("${a}", { timeout: ${r} });`:""}

        // Execute custom script
        const scriptResult = await page.evaluate(() => {
          ${t}
        });

        let finalResult = scriptResult;

        if ("${o}" === 'screenshot') {
          const screenshot = await page.screenshot({
            encoding: 'base64',
            type: 'png'
          });
          finalResult = {
            scriptResult: scriptResult,
            screenshot: screenshot
          };
        }

        return {
          data: {
            url: "${e}",
            result: finalResult,
            returnType: "${o}",
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(i)}async smartExtract(e,t){let s=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        const goals = ${JSON.stringify(t)};
        const results = {};

        // Common extraction patterns
        const extractors = {
          prices: () => {
            const priceSelectors = [
              '[class*="price"]', '[id*="price"]', '.cost', '.amount',
              '[data-testid*="price"]', '.currency', '[class*="dollar"]'
            ];
            const prices = [];
            priceSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                const text = el.textContent?.trim();
                if (text && /[$\xa3€\xa5₹]|\\d+\\.\\d{2}/.test(text)) {
                  prices.push({
                    text: text,
                    selector: selector,
                    element: el.tagName
                  });
                }
              });
            });
            return prices;
          },

          contact: () => {
            const contactSelectors = [
              '[href^="mailto:"]', '[href^="tel:"]', '.contact', '.email', '.phone'
            ];
            const contacts = [];
            contactSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                contacts.push({
                  text: el.textContent?.trim(),
                  href: el.getAttribute('href'),
                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'
                });
              });
            });
            return contacts;
          },

          products: () => {
            const productSelectors = [
              '.product', '[class*="product"]', '.item', '[data-testid*="product"]'
            ];
            const products = [];
            productSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                const title = el.querySelector('h1, h2, h3, .title, [class*="title"]')?.textContent?.trim();
                const price = el.querySelector('[class*="price"], .cost')?.textContent?.trim();
                const image = el.querySelector('img')?.src;
                if (title) {
                  products.push({ title, price, image });
                }
              });
            });
            return products;
          },

          text: () => {
            // Extract main content
            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];
            let content = '';
            for (const selector of contentSelectors) {
              const el = document.querySelector(selector);
              if (el) {
                content = el.textContent?.trim() || '';
                break;
              }
            }
            if (!content) {
              content = document.body.textContent?.trim() || '';
            }
            return content.substring(0, 5000); // Limit to 5000 chars
          },

          links: () => {
            const links = [];
            document.querySelectorAll('a[href]').forEach(el => {
              const href = el.getAttribute('href');
              const text = el.textContent?.trim();
              if (href && text && !href.startsWith('#')) {
                links.push({
                  url: new URL(href, window.location.href).href,
                  text: text
                });
              }
            });
            return links.slice(0, 50); // Limit to 50 links
          }
        };

        // Execute extractors based on goals
        goals.forEach(goal => {
          const goalLower = goal.toLowerCase();
          if (goalLower.includes('price') || goalLower.includes('cost')) {
            results.prices = extractors.prices();
          }
          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {
            results.contact = extractors.contact();
          }
          if (goalLower.includes('product') || goalLower.includes('item')) {
            results.products = extractors.products();
          }
          if (goalLower.includes('text') || goalLower.includes('content')) {
            results.text = extractors.text();
          }
          if (goalLower.includes('link') || goalLower.includes('url')) {
            results.links = extractors.links();
          }
        });

        // If no specific goals, extract everything
        if (goals.length === 0) {
          Object.keys(extractors).forEach(key => {
            results[key] = extractors[key]();
          });
        }

        return {
          data: {
            url: "${e}",
            extractionGoals: goals,
            results: results,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(s)}getStats(){return{totalKeys:this.apiKeys.length,healthyKeys:this.getHealthyKeys().length,keyUsage:Object.fromEntries(this.keyUsageCount),keyErrors:Object.fromEntries(this.keyErrors)}}}let r=a},78335:()=>{},96487:()=>{}};