"use strict";exports.id=1596,exports.ids=[1596],exports.modules={61596:(e,s,t)=>{t.d(s,{c_:()=>k});var r=t(60687),a=t(64908),l=t(15022),n=t(59922);function o({data:e,children:s,icon:t,color:a="#ff6b35",hasInput:o=!0,hasOutput:i=!0,hasRoleInput:d=!1,hasToolsInput:c=!1,hasBrowsingInput:x=!1,inputLabel:p="Input",outputLabel:g="Output",roleInputLabel:m="Role",toolsInputLabel:h="Tools",browsingInputLabel:u="Browse",inputHandles:y=[],className:b=""}){let f=e.isConfigured,v=e.hasError;return(0,r.jsxs)("div",{className:`relative ${b}`,children:[o&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"input",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:p})]}),d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"role",className:"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors",style:{left:-12,top:"30%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none",style:{left:-50,top:"25%"},children:m})]}),c&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"tools",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"70%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-50,top:"65%"},children:h})]}),x&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"browsing",className:"w-6 h-6 border-2 border-cyan-500 bg-cyan-700 hover:border-cyan-400 hover:bg-cyan-400 transition-colors",style:{left:-12,top:"85%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-cyan-200 font-medium pointer-events-none",style:{left:-50,top:"80%"},children:u})]}),y&&y.length>0&&y.map((e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:e.id,className:"w-8 h-8 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors cursor-pointer rounded-full",style:{left:-16,top:0===s?"30%":"60%",zIndex:10}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none whitespace-nowrap",style:{left:-70,top:0===s?"25%":"55%",zIndex:5},children:e.label})]},e.id)),(0,r.jsxs)("div",{className:`min-w-[200px] rounded-lg border-2 transition-all duration-200 ${v?"border-red-500 bg-red-900/20":f?"border-gray-600 bg-gray-800/90":"border-yellow-500 bg-yellow-900/20"} backdrop-blur-sm shadow-lg hover:shadow-xl`,style:{borderColor:v?"#ef4444":f?a:"#eab308"},children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3",style:{background:v?"linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))":`linear-gradient(135deg, ${a}20, ${a}10)`},children:[t&&(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{backgroundColor:v?"#ef444420":`${a}20`,color:v?"#ef4444":a},children:(0,r.jsx)(t,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:e.label}),e.description&&(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:e.description})]}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:v?(0,r.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full",title:"Error"}):f?(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full",title:"Configured"}):(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full",title:"Needs configuration"})})]}),s&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50",children:s}),v&&e.errorMessage&&(0,r.jsx)("div",{className:"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg",children:(0,r.jsx)("div",{className:"text-xs text-red-300",children:e.errorMessage})})]}),i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.h7,{type:"source",position:n.yX.Right,className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:g})]})]})}var i=t(68589),d=t(48544);let c={openai:"#10b981",anthropic:"#f97316",google:"#3b82f6",deepseek:"#8b5cf6",xai:"#374151",openrouter:"linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)"},x={openai:"OpenAI",anthropic:"Anthropic",google:"Google",deepseek:"DeepSeek",xai:"xAI (Grok)",openrouter:"OpenRouter"};var p=t(66524);let g={openai:"#10b981",anthropic:"#f97316",google:"#3b82f6",deepseek:"#8b5cf6",xai:"#374151",openrouter:"linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)"},m={openai:"OpenAI",anthropic:"Anthropic",google:"Google",deepseek:"DeepSeek",xai:"xAI (Grok)",openrouter:"OpenRouter"};var h=t(61245),u=t(93635),y=t(96374),b=t(49579),f=t(36920);let v={google_drive:"\uD83D\uDCC1",google_docs:"\uD83D\uDCC4",zapier:"⚡",notion:"\uD83D\uDCDD",google_sheets:"\uD83D\uDCCA",calendar:"\uD83D\uDCC5",gmail:"\uD83D\uDCE7",youtube:"\uD83D\uDCFA",supabase:"\uD83D\uDDC4️"},j={google_drive:"Google Drive",google_docs:"Google Docs",zapier:"Zapier",notion:"Notion",google_sheets:"Google Sheets",calendar:"Calendar",gmail:"Gmail",youtube:"YouTube",supabase:"Supabase"};var N=t(74461),w=t(43210),I=t(62392),A=t(70143);let k={userRequest:function({data:e}){return(0,r.jsx)(o,{data:e,icon:a.A,color:"#10b981",hasInput:!1,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Entry point for user input"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"This node captures the initial user request and starts the workflow execution."})]})})},classifier:function({data:e}){return(0,r.jsx)(o,{data:e,icon:i.A,color:"#3b82f6",hasInput:!0,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"AI-powered request analysis"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Analyzes the user request to determine the best routing strategy and extract key information."}),e.config?.classifierType&&(0,r.jsxs)("div",{className:"mt-2 px-2 py-1 bg-blue-900/30 rounded text-xs text-blue-300",children:["Type: ",e.config.classifierType]})]})})},provider:function({data:e,id:s}){let t=(0,l.Yu)(),a=(0,l.pk)(),n=e.config,i=n?.providerId,p=n?.modelId,g=i?c[i]:"#ff6b35",m=i?x[i]:"AI Provider",h=t.filter(e=>e.target===s&&"role"===e.targetHandle).map(e=>{let s=a.find(s=>s.id===e.source);if(s&&"roleAgent"===s.type){let t=s.data.config;return{id:e.source,name:t?.roleName||s.data.label||"Unknown Role",type:t?.roleType||"predefined"}}return null}).filter(Boolean);return(0,r.jsx)(o,{data:e,icon:d.A,color:"string"==typeof g?g:"#ff6b35",hasInput:!1,hasOutput:!0,hasRoleInput:!0,hasToolsInput:!1,hasBrowsingInput:!0,children:(0,r.jsx)("div",{className:"space-y-3",children:i?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:m}),"openrouter"===i&&(0,r.jsx)("span",{className:"text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-0.5 rounded-full",children:"300+ Models"})]}),p&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Model: ",p]}),n?.parameters&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-gray-400",children:["Temp: ",n.parameters.temperature||1]}),(0,r.jsxs)("div",{className:"text-gray-400",children:["Max: ",n.parameters.maxTokens||"Auto"]})]}),n?.fallbackProvider&&(0,r.jsxs)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:["Fallback: ",x[n.fallbackProvider.providerId]]}),h.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Roles:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:h.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"AI Provider Connection"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure to connect to OpenAI, Anthropic, Google, DeepSeek, xAI, or OpenRouter."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},vision:function({data:e,id:s}){let t=(0,l.Yu)(),a=(0,l.pk)(),n=e.config,i=n?.providerId,d=n?.modelId,c=i?g[i]:"#8b5cf6",x=i?m[i]:"Vision AI",h=t.filter(e=>e.target===s&&"role"===e.targetHandle).map(e=>{let s=a.find(s=>s.id===e.source);if(s&&"roleAgent"===s.type){let t=s.data.config;return{id:e.source,name:t?.roleName||s.data.label||"Unknown Role",type:t?.roleType||"predefined"}}return null}).filter(Boolean);return(0,r.jsx)(o,{data:e,icon:p.A,color:"string"==typeof c?c:"#8b5cf6",hasInput:!1,hasOutput:!0,hasRoleInput:!0,hasToolsInput:!1,children:(0,r.jsx)("div",{className:"space-y-3",children:i?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:x}),(0,r.jsx)("span",{className:"text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-0.5 rounded-full",children:"Vision"})]}),d&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Model: ",d]}),n?.parameters&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-gray-400",children:["Temp: ",n.parameters.temperature||1]}),(0,r.jsxs)("div",{className:"text-gray-400",children:["Max: ",n.parameters.maxTokens||"Auto"]})]}),n?.fallbackProvider&&(0,r.jsxs)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:["Fallback: ",m[n.fallbackProvider.providerId]]}),h.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Roles:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:h.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Vision AI Connection"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure to connect to multimodal AI models for image analysis and vision tasks."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},output:function({data:e}){return(0,r.jsx)(o,{data:e,icon:h.A,color:"#ef4444",hasInput:!0,hasOutput:!1,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Final response output"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"This node formats and delivers the final response to the user. Every workflow must end here."}),e.config?.outputFormat&&(0,r.jsxs)("div",{className:"mt-2 px-2 py-1 bg-red-900/30 rounded text-xs text-red-300",children:["Format: ",e.config.outputFormat]})]})})},roleAgent:function({data:e}){let s=e.config,t=s?.roleName,a=s?.tools||[];return(0,r.jsx)(o,{data:e,icon:u.A,color:"#8b5cf6",hasInput:!1,hasOutput:!0,outputLabel:"Role",children:(0,r.jsx)("div",{className:"space-y-3",children:t?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-white",children:t}),s?.customPrompt&&(0,r.jsx)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:"Custom prompt configured"}),a.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Tools:"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.slice(0,3).map((e,s)=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded",children:e},s)),a.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:["+",a.length-3," more"]})]})]}),s?.memoryEnabled&&(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✓ Memory enabled"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Role Plugin"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Connect to AI Provider nodes to assign specialized roles (e.g., Coder, Writer, Analyst)."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},centralRouter:function({data:e,id:s}){let t=(0,l.Yu)(),a=(0,l.pk)(),o=e.config,d=t.filter(e=>e.target===s&&"providers"===e.targetHandle).map(e=>{let s=a.find(s=>s.id===e.source);if(s&&"provider"===s.type){let t=s.data.config;return{id:e.source,name:t?.providerId||"Unknown Provider",model:t?.modelId||"Unknown Model"}}return null}).filter(Boolean),c=t.filter(e=>e.target===s&&"vision"===e.targetHandle).map(e=>{let s=a.find(s=>s.id===e.source);if(s&&"vision"===s.type){let t=s.data.config;return{id:e.source,name:t?.providerId||"Unknown Vision Provider",model:t?.modelId||"Unknown Model"}}return null}).filter(Boolean),x=t.filter(e=>e.target===s&&"tools"===e.targetHandle).map(e=>{let s=a.find(s=>s.id===e.source);if(s&&"tool"===s.type){let t=s.data.config;return{id:e.source,name:t?.toolType||"Unknown Tool",status:t?.connectionStatus||"disconnected"}}return null}).filter(Boolean),p=t.some(e=>e.target===s&&"classifier"===e.targetHandle&&a.find(s=>s.id===e.source&&"classifier"===s.type));return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"providers",className:"w-6 h-6 border-2 border-blue-500 bg-blue-700 hover:border-blue-400 hover:bg-blue-400 transition-colors",style:{left:-12,top:"20%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-blue-200 font-medium pointer-events-none",style:{left:-80,top:"15%"},children:"AI Providers"}),(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"vision",className:"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors",style:{left:-12,top:"50%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none",style:{left:-80,top:"45%"},children:"Vision Models"}),(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"classifier",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"65%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-80,top:"60%"},children:"Classifier"}),(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"tools",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"75%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-50,top:"70%"},children:"Tools"}),(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"memory",className:"w-6 h-6 border-2 border-yellow-500 bg-yellow-700 hover:border-yellow-400 hover:bg-yellow-400 transition-colors",style:{left:-12,top:"90%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-yellow-200 font-medium pointer-events-none",style:{left:-60,top:"85%"},children:"Memory"}),(0,r.jsx)(l.h7,{type:"source",position:n.yX.Right,id:"output",className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:"Output"}),(0,r.jsxs)("div",{className:`min-w-[280px] rounded-lg border-2 transition-all duration-200 ${e.hasError?"border-red-500 bg-red-900/20":e.isConfigured?"border-gray-600 bg-gray-800/90":"border-yellow-500 bg-yellow-900/20"} backdrop-blur-sm shadow-lg hover:shadow-xl`,style:{borderColor:e.hasError?"#ef4444":e.isConfigured?"#ff6b35":"#eab308"},children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3",style:{background:e.hasError?"linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))":"linear-gradient(135deg, #ff6b3520, #ff6b3510)"},children:[(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{backgroundColor:e.hasError?"#ef444420":"#ff6b3520",color:e.hasError?"#ef4444":"#ff6b35"},children:(0,r.jsx)(i.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:"Central Router"}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Smart routing hub for AI providers"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[p&&(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full",title:"Classifier Connected"}),d.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full",title:`${d.length} AI Providers`}),c.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full",title:`${c.length} Vision Models`}),x.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-cyan-500 rounded-full",title:`${x.length} Tools`})]})]}),(0,r.jsxs)("div",{className:"px-4 py-3 space-y-3",children:[d.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["AI Providers (",d.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:d.map(e=>(0,r.jsx)("span",{className:"text-xs bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full border border-blue-700/30",children:e.name},e.id))})]}),c.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Vision Models (",c.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:c.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]}),x.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Tools (",x.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:x.map(e=>(0,r.jsx)("span",{className:`text-xs px-2 py-0.5 rounded-full border ${"connected"===e.status?"bg-green-900/30 text-green-300 border-green-700/30":"bg-yellow-900/30 text-yellow-300 border-yellow-700/30"}`,children:e.name},e.id))})]}),o?.routingStrategy&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Strategy: ",o.routingStrategy.replace("_"," ").toUpperCase()]}),!p&&(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Connect classifier for smart routing"}),0===d.length&&0===c.length&&(0,r.jsx)("div",{className:"text-xs text-red-300 bg-red-900/20 px-2 py-1 rounded",children:"❌ No AI providers connected"}),x.length>0&&(0,r.jsxs)("div",{className:"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded",children:["\uD83D\uDD27 ",x.length," tool",x.length>1?"s":""," available"]}),(d.length>0||c.length>0)&&p&&(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✅ Ready for smart routing"})]})]})]})},conditional:function({data:e}){let s=e.config,t=s?.condition,a=s?.conditionType;return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:"Input"}),(0,r.jsxs)("div",{className:"min-w-[200px] rounded-lg border-2 border-amber-500 bg-amber-900/20 backdrop-blur-sm shadow-lg",children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-amber-500/20 to-amber-600/10",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-amber-500/20 text-amber-500",children:(0,r.jsx)(y.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:e.label}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Branch workflow based on conditions"})]}),(0,r.jsx)("div",{className:"w-2 h-2 bg-amber-500 rounded-full"})]}),(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50 space-y-3",children:t?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-300",children:["Condition: ",a||"custom"]}),(0,r.jsx)("div",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded font-mono",children:t}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-green-300",children:["True: ",s?.trueLabel||"Continue"]}),(0,r.jsxs)("div",{className:"text-red-300",children:["False: ",s?.falseLabel||"Skip"]})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Conditional Logic"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure conditions to branch your workflow into different paths."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})]}),(0,r.jsx)(l.h7,{type:"source",position:n.yX.Right,id:"true",className:"w-6 h-6 border-2 border-green-500 bg-green-600 hover:border-green-400 hover:bg-green-500 transition-colors",style:{right:-12,top:"40%"}}),(0,r.jsx)(l.h7,{type:"source",position:n.yX.Right,id:"false",className:"w-6 h-6 border-2 border-red-500 bg-red-600 hover:border-red-400 hover:bg-red-500 transition-colors",style:{right:-12,top:"60%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-300 font-medium pointer-events-none",style:{right:-50,top:"35%"},children:"True"}),(0,r.jsx)("div",{className:"absolute text-xs text-red-300 font-medium pointer-events-none",style:{right:-50,top:"55%"},children:"False"})]})},merge:function({data:e}){return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"input1",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12,top:"30%"}}),(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"input2",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12,top:"50%"}}),(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,id:"input3",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12,top:"70%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"25%"},children:"Input 1"}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:"Input 2"}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"65%"},children:"Input 3"}),(0,r.jsxs)("div",{className:"min-w-[200px] rounded-lg border-2 border-cyan-500 bg-cyan-900/20 backdrop-blur-sm shadow-lg",children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-cyan-500/20 to-cyan-600/10",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-cyan-500/20 text-cyan-500",children:(0,r.jsx)(b.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:e.label}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Combine multiple inputs"})]}),(0,r.jsx)("div",{className:"w-2 h-2 bg-cyan-500 rounded-full"})]}),(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50",children:(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Merges outputs from multiple nodes into a single stream for further processing."})})]}),(0,r.jsx)(l.h7,{type:"source",position:n.yX.Right,className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:"Output"})]})},loop:function({data:e}){let s=e.config,t=s?.loopType,a=s?.maxIterations;return(0,r.jsx)(o,{data:e,icon:b.A,color:"#f59e0b",hasInput:!0,hasOutput:!0,children:(0,r.jsx)("div",{className:"space-y-2",children:t?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-300",children:["Type: ",t]}),a&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max iterations: ",a]}),(0,r.jsx)("div",{className:"text-xs text-amber-300 bg-amber-900/20 px-2 py-1 rounded",children:"⚠️ Use with caution - can cause infinite loops"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Loop Operations"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Repeat operations based on conditions or iterations."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},tool:function({data:e}){let s=e.config,t=s?.toolType,a=t?v[t]:"\uD83D\uDD27",l=t?j[t]:"External Tool",n=s?.connectionStatus||"disconnected",i=()=>{switch(n){case"connected":return"text-green-400";case"error":return"text-red-400";default:return"text-yellow-400"}};return(0,r.jsx)(o,{data:e,icon:f.A,color:"#06b6d4",hasInput:!0,hasOutput:!0,children:(0,r.jsx)("div",{className:"space-y-3",children:t?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-lg",children:a}),(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:l})]}),(0,r.jsx)("div",{className:`text-xs ${i()}`,children:"●"})]}),(0,r.jsx)("div",{className:`text-xs ${i()}`,children:(()=>{switch(n){case"connected":return"Connected";case"error":return"Error";default:return"Not Connected"}})()}),s?.timeout&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Timeout: ",s.timeout,"s"]}),(0,r.jsx)("div",{className:"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded",children:"✓ Tool configured"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"External Tool Integration"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Connect to external services like Google Drive, databases, APIs, or browser automation."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},memory:function({data:e,id:s}){let t=e.config,a=t?.memoryName,n=t?.maxSize||10240,i=t?.encryption!==!1,d=(0,l.Yu)().filter(e=>e.source===s).map(e=>e.target),[c,x]=(0,w.useState)(null);return(0,r.jsx)(o,{data:e,icon:N.A,color:"#ec4899",hasInput:!0,hasOutput:!0,children:(0,r.jsx)("div",{className:"space-y-3",children:a?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-white",children:["\uD83E\uDDE0 ",a]}),(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Max: ",Math.round(n/1024),"MB | ",i?"\uD83D\uDD12 Encrypted":"\uD83D\uDD13 Plain"]}),c&&(0,r.jsxs)("div",{className:"text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded space-y-1",children:[(0,r.jsxs)("div",{children:["\uD83D\uDCCA ",c.entriesCount," entries"]}),(0,r.jsxs)("div",{children:["\uD83D\uDCBE ",c.totalSize," used"]}),(0,r.jsxs)("div",{children:["\uD83D\uDD52 Updated ",c.lastUpdate]})]}),d.length>0&&(0,r.jsxs)("div",{className:"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded",children:["\uD83D\uDD17 Connected to ",d.length," node",d.length>1?"s":""]}),(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✅ Active & Ready"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"\uD83E\uDDE0 Plug & Play Memory"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Intelligent memory brain for connected nodes. Automatically handles storage, retrieval, and persistence."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},switch:function({data:e}){let s=e.config,t=s?.switchType,a=s?.cases||[];return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.h7,{type:"target",position:n.yX.Left,className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:"Input"}),(0,r.jsxs)("div",{className:"min-w-[200px] rounded-lg border-2 border-indigo-500 bg-indigo-900/20 backdrop-blur-sm shadow-lg",children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-indigo-500/20 to-indigo-600/10",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-indigo-500/20 text-indigo-500",children:(0,r.jsx)(I.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:e.label}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Route to different paths"})]}),(0,r.jsx)("div",{className:"w-2 h-2 bg-indigo-500 rounded-full"})]}),(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50 space-y-3",children:t?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-300",children:["Type: ",t]}),a.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Cases:"}),a.slice(0,3).map((e,s)=>(0,r.jsx)("div",{className:"text-xs bg-indigo-900/30 text-indigo-300 px-2 py-0.5 rounded",children:e.label||`Case ${s+1}`},s)),a.length>3&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["+",a.length-3," more cases"]})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Switch Router"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Route workflow to different paths based on input values or conditions."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})]}),a.length>0?a.slice(0,4).map((e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsx)(l.h7,{type:"source",position:n.yX.Right,id:`case-${s}`,className:"w-6 h-6 border-2 border-indigo-500 bg-indigo-600 hover:border-indigo-400 hover:bg-indigo-500 transition-colors",style:{right:-12,top:`${30+15*s}%`}}),(0,r.jsxs)("div",{className:"absolute text-xs text-indigo-300 font-medium pointer-events-none",style:{right:-60,top:`${25+15*s}%`},children:["Case ",s+1]})]},s)):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.h7,{type:"source",position:n.yX.Right,className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:"Output"})]})]})},planner:function({data:e}){let s=e.config,t=s?.providerId,a=s?.modelId,l=s?.maxSubtasks||10;return(0,r.jsx)(o,{data:e,icon:i.A,color:"#8b5cf6",hasInput:!1,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-3",children:[t&&a?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:{openai:"OpenAI",anthropic:"Anthropic",google:"Google",groq:"Groq",deepseek:"DeepSeek",openrouter:"OpenRouter"}[t]||t}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:(e=>{if(!e)return"";let s=e.split("/");return s[s.length-1]})(a)})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max subtasks: ",l]}),s?.parameters?.temperature!==void 0&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Temperature: ",s.parameters.temperature]})]}):(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Configure AI model for planning"}),(0,r.jsx)("div",{className:"text-xs text-purple-300 bg-purple-900/30 px-2 py-1 rounded",children:"\uD83D\uDCCB Planning Agent"})]})})},browsing:function({data:e}){let s=e.config,t=s?.maxSites||5,a=s?.timeout||30,l=s?.enableScreenshots??!0,n=s?.enableFormFilling??!0,i=s?.searchEngines||["google"],d=()=>{let e=[];return l&&e.push("\uD83D\uDCF8 Screenshots"),n&&e.push("\uD83D\uDCDD Forms"),s?.enableCaptchaSolving&&e.push("\uD83D\uDD10 CAPTCHAs"),e};return(0,r.jsx)(o,{data:e,icon:A.A,color:"#10b981",hasInput:!1,hasOutput:!0,inputHandles:[{id:"plan",label:"Plan",position:"left"},{id:"memory",label:"Memory",position:"left"}],children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:"Intelligent Browsing"}),(0,r.jsx)("div",{className:"text-xs text-green-400",children:"● Ready"})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max sites: ",t," | Timeout: ",a,"s"]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Engines: ",i.join(", ")]}),d().length>0&&(0,r.jsx)("div",{className:"text-xs text-gray-400",children:d().join(" • ")}),s?.maxDepth&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max depth: ",s.maxDepth," levels"]})]}),(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded",children:"\uD83C\uDF10 Autonomous Agent"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Requires: Planner + Memory inputs"})]})})}}}};