(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4150],{574:(e,s,a)=>{"use strict";a.d(s,{m:()=>t.m});var t=a(76804)},31547:(e,s,a)=>{"use strict";a.d(s,{QR:()=>r.A,Uz:()=>o.A,X_:()=>i.A,hc:()=>t.A,kU:()=>l.A});var t=a(1243),r=a(24357),l=a(92657),i=a(78749),o=a(69803)},44469:(e,s,a)=>{Promise.resolve().then(a.bind(a,71911))},71911:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>E});var t=a(95155),r=a(12115),l=a(35695),i=a(75922),o=a(47225),n=a(32461),d=a(6865),c=a(89959),m=a(37186),u=a(65529),x=a(67695),h=a(94038),g=a(61316),p=a(85037),b=a(57765),f=a(8246),y=a(31151),j=a(52589),v=a(55424),N=a(80377),w=a(87162),k=a(28003),_=a(79958),C=a(53951),A=a(99323),S=a(54547),I=a(60993);let P=i.MG.map(e=>({value:e.id,label:e.name}));function E(){var e,s;let a=(0,l.useParams)().configId,E=(0,w.Z)(),T=(0,A.bu)(),D=(null==T?void 0:T.navigateOptimistically)||(e=>{window.location.href=e}),{getCachedData:R,isCached:F,clearCache:M}=(0,k._)(),{createHoverPrefetch:K}=(0,C.c)(),[L,O]=(0,r.useState)(null),[z,U]=(0,r.useState)(!0),[G,B]=(0,r.useState)(!1),[J,q]=(0,r.useState)((null==(e=P[0])?void 0:e.value)||"openai"),[W,H]=(0,r.useState)(""),[V,Q]=(0,r.useState)(""),[X,Z]=(0,r.useState)(""),[Y,$]=(0,r.useState)(1),[ee,es]=(0,r.useState)(!1),[ea,et]=(0,r.useState)(null),[er,el]=(0,r.useState)(null),[ei,eo]=(0,r.useState)(null),[en,ed]=(0,r.useState)(!1),[ec,em]=(0,r.useState)(null),[eu,ex]=(0,r.useState)([]),[eh,eg]=(0,r.useState)(!0),[ep,eb]=(0,r.useState)(null),[ef,ey]=(0,r.useState)(null),[ej,ev]=(0,r.useState)(null),[eN,ew]=(0,r.useState)(null),[ek,e_]=(0,r.useState)(1),[eC,eA]=(0,r.useState)(""),[eS,eI]=(0,r.useState)(!1),[eP,eE]=(0,r.useState)([]),[eT,eD]=(0,r.useState)(!1),[eR,eF]=(0,r.useState)(null),[eM,eK]=(0,r.useState)(!1),[eL,eO]=(0,r.useState)(""),[ez,eU]=(0,r.useState)(""),[eG,eB]=(0,r.useState)(""),[eJ,eq]=(0,r.useState)(!1),[eW,eH]=(0,r.useState)(null),[eV,eQ]=(0,r.useState)(null),[eX,eZ]=(0,r.useState)("provider-keys"),eY=(0,r.useCallback)(async()=>{if(!a)return;let e=R(a);if(e&&e.configDetails){O(e.configDetails),U(!1);return}F(a)||B(!0),U(!0),et(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch configurations list")}let s=(await e.json()).find(e=>e.id===a);if(!s)throw Error("Configuration not found in the list.");O(s)}catch(e){et("Error loading model configuration: ".concat(e.message)),O(null)}finally{U(!1),B(!1)}},[a,R,F]);(0,r.useEffect)(()=>{eY()},[eY]);let e$=(0,r.useCallback)(async()=>{let e=R(a);if(e&&e.models){eo(e.models),ed(!1);return}ed(!0),em(null),eo(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),s=await e.json();if(!e.ok)throw Error(s.error||"Failed to fetch models from database.");s.models?eo(s.models):eo([])}catch(e){em("Error fetching models: ".concat(e.message)),eo([])}finally{ed(!1)}},[a,R]);(0,r.useEffect)(()=>{a&&e$()},[a,e$]);let e0=(0,r.useCallback)(async()=>{let e=R(a);if(e&&e.userCustomRoles){eE(e.userCustomRoles),eD(!1);return}eD(!0),eF(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let s=await e.json();eE(s)}else{let s;try{s=await e.json()}catch(a){s={error:await e.text().catch(()=>"HTTP error ".concat(e.status))}}let a=s.error||(s.issues?JSON.stringify(s.issues):"Failed to fetch custom roles (status: ".concat(e.status,")"));if(401===e.status)eF(a);else throw Error(a);eE([])}}catch(e){eF(e.message),eE([])}finally{eD(!1)}},[]),e5=(0,r.useCallback)(async()=>{if(!a||!eP)return;let e=R(a);if(e&&e.apiKeys&&void 0!==e.defaultChatKeyId){let s=e.apiKeys.map(async s=>{let a=await fetch("/api/keys/".concat(s.id,"/roles")),t=[];return a.ok&&(t=(await a.json()).map(e=>{let s=(0,o.Dc)(e.role_name);if(s)return s;let a=eP.find(s=>s.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...s,assigned_roles:t,is_default_general_chat_model:e.defaultChatKeyId===s.id}});ex(await Promise.all(s)),ey(e.defaultChatKeyId),eg(!1);return}eg(!0),et(e=>e&&e.startsWith("Error loading model configuration:")?e:null),el(null);try{let e=await fetch("/api/keys?custom_config_id=".concat(a));if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch API keys")}let s=await e.json(),t=await fetch("/api/custom-configs/".concat(a,"/default-chat-key"));t.ok;let r=200===t.status?await t.json():null;ey((null==r?void 0:r.id)||null);let l=s.map(async e=>{let s=await fetch("/api/keys/".concat(e.id,"/roles")),a=[];return s.ok&&(a=(await s.json()).map(e=>{let s=(0,o.Dc)(e.role_name);if(s)return s;let a=eP.find(s=>s.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:a,is_default_general_chat_model:(null==r?void 0:r.id)===e.id}}),i=await Promise.all(l);ex(i)}catch(e){et(s=>s?"".concat(s,"; ").concat(e.message):e.message)}finally{eg(!1)}},[a,eP]);(0,r.useEffect)(()=>{L&&e0()},[L,e0]),(0,r.useEffect)(()=>{L&&eP&&e5()},[L,eP,e5]);let e2=(0,r.useMemo)(()=>{if(ei){let e=i.MG.find(e=>e.id===J);if(!e)return[];if("openrouter"===e.id)return ei.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,s)=>(e.label||"").localeCompare(s.label||""));if("deepseek"===e.id){let e=[];return ei.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),ei.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,s)=>(e.label||"").localeCompare(s.label||""))}return ei.filter(s=>s.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,s)=>(e.label||"").localeCompare(s.label||""))}return[]},[ei,J]),e4=(0,r.useMemo)(()=>{if(ei&&eN){let e=i.MG.find(e=>e.id===eN.provider);if(!e)return[];if("openrouter"===e.id)return ei.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,s)=>(e.label||"").localeCompare(s.label||""));if("deepseek"===e.id){let e=[];return ei.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),ei.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,s)=>(e.label||"").localeCompare(s.label||""))}return ei.filter(s=>s.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,s)=>(e.label||"").localeCompare(s.label||""))}return[]},[ei,eN]);(0,r.useEffect)(()=>{e2.length>0?H(e2[0].value):H("")},[e2,J]),(0,r.useEffect)(()=>{J&&e$()},[J,e$]);let e1=async e=>{if(e.preventDefault(),!a)return void et("Configuration ID is missing.");if(eu.some(e=>e.predefined_model_id===W))return void et("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");es(!0),et(null),el(null);let s=[...eu];try{var t;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:a,provider:J,predefined_model_id:W,api_key_raw:V,label:X,temperature:Y})}),s=await e.json();if(!e.ok)throw Error(s.details||s.error||"Failed to save API key");let r={id:s.id,custom_api_config_id:a,provider:J,predefined_model_id:W,label:X,temperature:Y,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};ex(e=>[...e,r]),M(a),el('API key "'.concat(X,'" saved successfully!')),q((null==(t=P[0])?void 0:t.value)||"openai"),Q(""),Z(""),$(1),e2.length>0&&H(e2[0].value)}catch(e){ex(s),et("Save Key Error: ".concat(e.message))}finally{es(!1)}},e3=e=>{ew(e),e_(e.temperature||1),eA(e.predefined_model_id)},e6=async()=>{if(!eN)return;if(eu.some(e=>e.id!==eN.id&&e.predefined_model_id===eC))return void et("This model is already configured in this setup. Each model can only be used once per configuration.");eI(!0),et(null),el(null);let e=[...eu];ex(e=>e.map(e=>e.id===eN.id?{...e,temperature:ek,predefined_model_id:eC}:e));try{let s=await fetch("/api/keys?id=".concat(eN.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:ek,predefined_model_id:eC})}),t=await s.json();if(!s.ok)throw ex(e),Error(t.details||t.error||"Failed to update API key");M(a),el('API key "'.concat(eN.label,'" updated successfully!')),ew(null)}catch(e){et("Update Key Error: ".concat(e.message))}finally{eI(!1)}},e8=(e,s)=>{E.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(s,'"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{eb(e),et(null),el(null);let t=[...eu],r=eu.find(s=>s.id===e);ex(s=>s.filter(s=>s.id!==e)),(null==r?void 0:r.is_default_general_chat_model)&&ey(null);try{let r=await fetch("/api/keys/".concat(e),{method:"DELETE"}),l=await r.json();if(!r.ok){if(ex(t),ey(ef),404===r.status){ex(s=>s.filter(s=>s.id!==e)),el('API key "'.concat(s,'" was already deleted.'));return}throw Error(l.details||l.error||"Failed to delete API key")}M(a),el('API key "'.concat(s,'" deleted successfully!'))}catch(e){throw et("Delete Key Error: ".concat(e.message)),e}finally{eb(null)}})},e9=async e=>{if(!a)return;et(null),el(null);let s=[...eu];ex(s=>s.map(s=>({...s,is_default_general_chat_model:s.id===e}))),ey(e);try{let t=await fetch("/api/custom-configs/".concat(a,"/default-key-handler/").concat(e),{method:"PUT"}),r=await t.json();if(!t.ok)throw ex(s.map(e=>({...e}))),ey(ef),Error(r.details||r.error||"Failed to set default chat key");M(a),el(r.message||"Default general chat key updated!")}catch(e){et("Set Default Error: ".concat(e.message))}},e7=async(e,s,a)=>{et(null),el(null);let t="/api/keys/".concat(e.id,"/roles"),r=[...o.p2.map(e=>({...e,isCustom:!1})),...eP.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===s)||{id:s,name:s,description:""},l=eu.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),i=null;ej&&ej.id===e.id&&(i={...ej,assigned_roles:[...ej.assigned_roles.map(e=>({...e}))]}),ex(t=>t.map(t=>{if(t.id===e.id){let e=a?t.assigned_roles.filter(e=>e.id!==s):[...t.assigned_roles,r];return{...t,assigned_roles:e}}return t})),ej&&ej.id===e.id&&ev(e=>{if(!e)return null;let t=a?e.assigned_roles.filter(e=>e.id!==s):[...e.assigned_roles,r];return{...e,assigned_roles:t}});try{let o;o=a?await fetch("".concat(t,"/").concat(s),{method:"DELETE"}):await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:s})});let n=await o.json();if(!o.ok){if(ex(l),i)ev(i);else if(ej&&ej.id===e.id){let s=l.find(s=>s.id===e.id);s&&ev(s)}let s=409===o.status&&n.error?n.error:n.details||n.error||(a?"Failed to unassign role":"Failed to assign role");throw Error(s)}el(n.message||"Role '".concat(r.name,"' ").concat(a?"unassigned":"assigned"," successfully."))}catch(e){et("Role Update Error: ".concat(e.message))}},se=async()=>{if(!eL.trim()||eL.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eL.trim()))return void eH("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(o.p2.some(e=>e.id.toLowerCase()===eL.trim().toLowerCase())||eP.some(e=>e.role_id.toLowerCase()===eL.trim().toLowerCase()))return void eH("This Role ID is already in use (either predefined or as one of your custom roles).");if(!ez.trim())return void eH("Role Name is required.");eH(null),eq(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eL.trim(),name:ez.trim(),description:eG.trim()})});if(!e.ok){let s;try{s=await e.json()}catch(t){let a=await e.text().catch(()=>"HTTP status ".concat(e.status));s={error:"Server error, could not parse response.",details:a}}let a=s.error||"Failed to create custom role.";if(s.details)a+=" (Details: ".concat(s.details,")");else if(s.issues){let e=Object.entries(s.issues).map(e=>{let[s,a]=e;return"".concat(s,": ").concat(a.join(", "))}).join("; ");a+=" (Issues: ".concat(e,")")}throw Error(a)}let s=await e.json();eO(""),eU(""),eB(""),M(a);let t={id:s.id,role_id:s.role_id,name:s.name,description:s.description,user_id:s.user_id,created_at:s.created_at,updated_at:s.updated_at};eE(e=>[...e,t]),el("Custom role '".concat(s.name,"' created successfully! It is now available globally."))}catch(e){eH(e.message)}finally{eq(!1)}},ss=(e,s)=>{e&&E.showConfirmation({title:"Delete Custom Role",message:'Are you sure you want to delete the custom role "'.concat(s,"\"? This will unassign it from all API keys where it's currently used. This action cannot be undone."),confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eQ(e),eF(null),eH(null),el(null);try{let t=await fetch("/api/user/custom-roles/".concat(e),{method:"DELETE"}),r=await t.json();if(!t.ok)throw Error(r.error||"Failed to delete custom role");eE(s=>s.filter(s=>s.id!==e)),M(a),el(r.message||'Global custom role "'.concat(s,'" deleted successfully.')),a&&e5()}catch(e){throw eF("Error deleting role: ".concat(e.message)),e}finally{eQ(null)}})};return G&&!F(a)?(0,t.jsx)(_.A,{}):z&&!L?(0,t.jsx)(_._,{}):(0,t.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,t.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("button",{onClick:()=>D("/my-models"),className:"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,t.jsx)(n.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,t.jsx)("div",{className:"flex-1",children:L?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,t.jsx)(m.A,{className:"h-6 w-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white",children:L.name}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Model Configuration"})]})]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit",children:[(0,t.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",L.id]})]}):ea&&!z?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4",children:(0,t.jsx)(j.A,{className:"h-6 w-6 text-red-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-400",children:"Configuration Error"}),(0,t.jsx)("p",{className:"text-red-300 mt-1",children:ea.replace("Error loading model configuration: ","")})]})]}):(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4",children:(0,t.jsx)(c.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Loading Configuration..."}),(0,t.jsx)("p",{className:"text-gray-400 mt-1",children:"Please wait while we fetch your model details"})]})]})}),L&&(0,t.jsx)("div",{className:"flex flex-col sm:flex-row gap-3",children:(0,t.jsxs)("button",{onClick:()=>D("/routing-setup/".concat(a,"?from=model-config")),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",...K(a),children:[(0,t.jsx)(m.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})})]})}),er&&(0,t.jsx)("div",{className:"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-400"}),(0,t.jsx)("p",{className:"text-green-300 font-medium",children:er})]})}),ea&&(0,t.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-red-400"}),(0,t.jsx)("p",{className:"text-red-300 font-medium",children:ea})]})})]}),L&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2",children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("button",{onClick:()=>eZ("provider-keys"),className:"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ".concat("provider-keys"===eX?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"),children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Provider API Keys"})]})}),(0,t.jsx)("button",{onClick:()=>eZ("user-api-keys"),className:"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ".concat("user-api-keys"===eX?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"),children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Generated API Keys"})]})})]})}),"provider-keys"===eX&&(0,t.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,t.jsx)("div",{className:"xl:col-span-2",children:(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8",children:[(0,t.jsxs)("div",{className:"flex items-center mb-6",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,t.jsx)(b.A,{className:"h-5 w-5 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-white",children:"Add Provider API Key"}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"Configure new provider key"})]})]}),(0,t.jsxs)("form",{onSubmit:e1,className:"space-y-5",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,t.jsx)("select",{id:"provider",value:J,onChange:e=>{q(e.target.value)},className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm",children:P.map(e=>(0,t.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key"}),(0,t.jsx)("input",{id:"apiKeyRaw",type:"password",value:V,onChange:e=>Q(e.target.value),className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"Enter your API key"}),en&&null===ei&&(0,t.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),ec&&(0,t.jsx)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:ec})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,t.jsx)("select",{id:"predefinedModelId",value:W,onChange:e=>H(e.target.value),disabled:!e2.length,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500",children:e2.length>0?e2.map(e=>(0,t.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value)):(0,t.jsx)("option",{value:"",disabled:!0,className:"bg-gray-800 text-gray-400",children:null===ei&&en?"Loading models...":"Select a provider first"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,t.jsx)("input",{type:"text",id:"label",value:X,onChange:e=>Z(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,t.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:Y,onChange:e=>$(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:Y,onChange:e=>$(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,t.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,t.jsx)("button",{type:"submit",disabled:ee||!W||""===W||!V.trim()||!X.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:ee?(0,t.jsxs)("span",{className:"flex items-center justify-center",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,t.jsxs)("span",{className:"flex items-center justify-center",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-blue-300 mb-1",children:"Key Configuration Rules"}),(0,t.jsxs)("div",{className:"text-xs text-blue-200 space-y-1",children:[(0,t.jsxs)("p",{children:["✅ ",(0,t.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,t.jsxs)("p",{children:["✅ ",(0,t.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,t.jsxs)("p",{children:["❌ ",(0,t.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,t.jsx)("div",{className:"xl:col-span-3",children:(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-6",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,t.jsx)(h.A,{className:"h-5 w-5 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-white",children:"API Keys & Roles"}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"Manage existing keys"})]})]}),eh&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(c.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Loading API keys..."})]}),!eh&&0===eu.length&&(!ea||ea&&ea.startsWith("Error loading model configuration:"))&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,t.jsx)(h.A,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!eh&&eu.length>0&&(0,t.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:eu.map((e,s)=>(0,t.jsx)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(50*s,"ms")},children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-white truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0",children:[(0,t.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("p",{className:"text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:[e.provider," (",e.predefined_model_id,")"]}),(0,t.jsxs)("p",{className:"text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50",children:["Temp: ",e.temperature]})]}),(0,t.jsx)(I.sU,{feature:"custom_roles",fallback:(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:(0,t.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"Roles available on Starter plan+"})}),children:(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,t.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50",children:e.name},e.id)):(0,t.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"No roles"})})})]}),!e.is_default_general_chat_model&&(0,t.jsx)("button",{onClick:()=>e9(e.id),className:"text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"global-tooltip","data-tooltip-content":"Set as default chat model",children:"Set Default"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,t.jsx)("button",{onClick:()=>e3(e),disabled:ep===e.id,className:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Edit Model & Settings",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})}),(0,t.jsx)(I.sU,{feature:"custom_roles",fallback:(0,t.jsx)("button",{disabled:!0,className:"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Role management requires Starter plan or higher",children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),children:(0,t.jsx)("button",{onClick:()=>ev(e),disabled:ep===e.id,className:"p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Manage Roles",children:(0,t.jsx)(m.A,{className:"h-4 w-4"})})}),(0,t.jsx)("button",{onClick:()=>e8(e.id,e.label),disabled:ep===e.id,className:"p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Delete Key",children:ep===e.id?(0,t.jsx)(y.A,{className:"h-4 w-4 animate-pulse"}):(0,t.jsx)(y.A,{className:"h-4 w-4"})})]})]})},e.id))}),!eh&&ea&&!ea.startsWith("Error loading model configuration:")&&(0,t.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-red-400"}),(0,t.jsxs)("p",{className:"text-red-300 font-medium text-sm",children:["Could not load API keys/roles: ",ea]})]})})]})})]}),"user-api-keys"===eX&&(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:(0,t.jsx)(S.z,{configId:a,configName:L.name})})]}),ej&&(()=>{if(!ej)return null;let e=[...o.p2.map(e=>({...e,isCustom:!1})),...eP.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,s)=>e.name.localeCompare(s.name));return(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,t.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-white",children:["Manage Roles for: ",(0,t.jsx)("span",{className:"text-orange-400",children:ej.label})]}),(0,t.jsx)("button",{onClick:()=>{ev(null),eK(!1),eH(null)},className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,t.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[eR&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,t.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",eR]})}),(0,t.jsxs)(I.sU,{feature:"custom_roles",customMessage:"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.",children:[(0,t.jsx)("div",{className:"flex justify-end mb-4",children:(0,t.jsxs)("button",{onClick:()=>eK(!eM),className:"btn-primary text-sm inline-flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),eM?"Cancel New Role":"Create New Custom Role"]})}),eM&&(0,t.jsxs)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4",children:[(0,t.jsx)("h3",{className:"text-md font-medium text-white mb-3",children:"Create New Custom Role for this Configuration"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,t.jsx)("input",{type:"text",id:"newCustomRoleId",value:eL,onChange:e=>eO(e.target.value.replace(/\s/g,"")),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-300 mb-1",children:"Display Name (max 100 chars)"}),(0,t.jsx)("input",{type:"text",id:"newCustomRoleName",value:ez,onChange:e=>eU(e.target.value),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-300 mb-1",children:"Description (optional, max 500 chars)"}),(0,t.jsx)("textarea",{id:"newCustomRoleDescription",value:eG,onChange:e=>eB(e.target.value),rows:2,className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eW&&(0,t.jsx)("div",{className:"bg-red-900/50 border border-red-800/50 rounded-lg p-3",children:(0,t.jsx)("p",{className:"text-red-300 text-sm",children:eW})}),(0,t.jsx)("button",{onClick:se,disabled:eJ,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eJ?"Saving Role...":"Save Custom Role"})]})]})]})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-300 mb-3",children:"Select roles to assign:"}),(0,t.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eT&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let s=ej.assigned_roles.some(s=>s.id===e.id);return(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ".concat(s?"bg-orange-500/20 border-orange-500/30 shadow-sm":"bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm"),children:[(0,t.jsxs)("label",{htmlFor:"role-".concat(e.id),className:"flex items-center cursor-pointer flex-grow",children:[(0,t.jsx)("input",{type:"checkbox",id:"role-".concat(e.id),checked:s,onChange:()=>e7(ej,e.id,s),className:"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700"}),(0,t.jsx)("span",{className:"ml-3 text-sm font-medium ".concat(s?"text-orange-300":"text-white"),children:e.name}),e.isCustom&&(0,t.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,t.jsx)("button",{onClick:()=>ss(e.databaseId,e.name),disabled:eV===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eV===e.databaseId?(0,t.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(y.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{onClick:()=>{ev(null),eK(!1),eH(null)},className:"btn-secondary",children:"Done"})})})]})})})(),eN&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,t.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Edit API Key"}),(0,t.jsx)("button",{onClick:()=>ew(null),className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,t.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:eN.label}),(0,t.jsxs)("p",{className:"text-sm text-gray-400",children:["Current: ",eN.provider," (",eN.predefined_model_id,")"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,t.jsx)("div",{className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300",children:(null==(s=i.MG.find(e=>e.id===eN.provider))?void 0:s.name)||eN.provider}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Provider cannot be changed"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,t.jsx)("select",{id:"editModelId",value:eC,onChange:e=>eA(e.target.value),disabled:!e4.length,className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30",children:e4.length>0?e4.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value)):(0,t.jsx)("option",{value:"",disabled:!0,children:en?"Loading models...":"No models available"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",ek]}),(0,t.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:ek,onChange:e=>e_(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,t.jsx)("span",{children:"0.0 (Focused)"}),(0,t.jsx)("span",{children:"1.0 (Balanced)"}),(0,t.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,t.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-3",children:(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{onClick:()=>ew(null),className:"btn-secondary",disabled:eS,children:"Cancel"}),(0,t.jsx)("button",{onClick:e6,disabled:eS,className:"btn-primary",children:eS?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!L&&!z&&!ea&&(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(x.A,{className:"h-8 w-8 text-gray-400"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,t.jsxs)("button",{onClick:()=>D("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,t.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,t.jsx)(N.A,{isOpen:E.isOpen,onClose:E.hideConfirmation,onConfirm:E.onConfirm,title:E.title,message:E.message,confirmText:E.confirmText,cancelText:E.cancelText,type:E.type,isLoading:E.isLoading}),(0,t.jsx)(v.m_,{id:"global-tooltip"})]})})}},75898:(e,s,a)=>{"use strict";a.d(s,{FW:()=>r.A,Uz:()=>t.A,e9:()=>l.A});var t=a(69803),r=a(84616),l=a(53904)},76288:(e,s,a)=>{"use strict";a.d(s,{X:()=>t.A});var t=a(54416)},87266:(e,s,a)=>{"use strict";a.d(s,{Il:()=>t.A,QR:()=>l.A,TB:()=>n.A,Vv:()=>r.A,ek:()=>o.A,qz:()=>i.A});var t=a(79397),r=a(69074),l=a(24357),i=a(34869),o=a(75525),n=a(62525)}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,7874,5738,9968,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,2662,8669,4703,622,2432,408,8925,7068,2076,1670,914,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(44469)),_N_E=e.O()}]);