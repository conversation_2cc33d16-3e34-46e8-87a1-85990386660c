exports.id=5411,exports.ids=[5411],exports.modules={1804:(e,t,r)=>{"use strict";r.d(t,{IH:()=>l,JC:()=>n});var s=r(39398);let i=new Map;function a(e){i.get(e)&&i.delete(e)}function o(e,t){let r=i.get(e);if(r)try{let e=new TextEncoder,s=`id: ${t.id}
event: ${t.type}
data: ${JSON.stringify(t)}

`;r.controller.enqueue(e.encode(s)),r.lastEventId=t.id}catch(t){i.delete(e)}}function n(e,t,r,s,a){let n={id:crypto.randomUUID(),workflowId:e,executionId:a,type:r,timestamp:new Date().toISOString(),data:s,userId:t};Array.from(i.entries()).filter(([t,r])=>r.workflowId===e).forEach(([e,t])=>{o(e,n)}),c(n).catch(e=>{})}async function c(e){try{let t=(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);await t.from("workflow_execution_logs").insert({execution_id:e.executionId||e.workflowId,workflow_id:e.workflowId,node_id:e.data?.nodeId||"system",node_type:e.data?.nodeType||"system",log_level:e.type.includes("failed")?"error":"info",message:e.data?.message||`Workflow event: ${e.type}`,data:e.data,duration_ms:e.data?.duration,created_at:e.timestamp})}catch(e){}}function l(e,t){let r=crypto.randomUUID();return new TextEncoder,new ReadableStream({start(s){let a={workflowId:e,userId:t,controller:s,connectedAt:new Date().toISOString()};i.set(r,a);let n={id:crypto.randomUUID(),workflowId:e,type:"connection_established",timestamp:new Date().toISOString(),data:{message:"\uD83D\uDD17 Connected to workflow real-time updates",workflowId:e,connectionId:r},userId:t};o(r,n)},cancel(){a(r)}})}setInterval(function(){let e=Date.now();for(let[t,r]of i.entries())e-new Date(r.connectedAt).getTime()>18e5&&a(t)},3e5)},28314:(e,t,r)=>{"use strict";r.d(t,{S:()=>a});var s=r(39765);class i{connectMemory(e,t,r){this.memoryNodeId=e,this.workflowId=t,this.userId=r,this.loadMemoryFromStorage()}async recordRoutingDecision(e,t,r,s,i){let a={query:e,selectedProvider:t,reason:r,performance:i?Math.max(.1,1-s/1e4):0,timestamp:new Date().toISOString(),responseTime:s,success:i};this.routingHistory.push(a),this.updateProviderPerformance(t,a),await this.saveMemoryToStorage()}getRoutingRecommendation(e,t,r){if(r&&this.userPreferences.taskTypePreferences[r]){let e=this.userPreferences.taskTypePreferences[r];if(t.includes(e))return{recommendedProvider:e,confidence:.9,reason:`User prefers ${e} for ${r} tasks`}}let s=this.findSimilarQueries(e,5);if(s.length>0){let e=s.filter(e=>e.success&&e.performance>.6);if(e.length>0){let r=e.reduce((e,t)=>t.performance>e.performance?t:e);if(t.includes(r.selectedProvider))return{recommendedProvider:r.selectedProvider,confidence:.8,reason:`${r.selectedProvider} performed well on similar queries`}}}let i=t.map(e=>{let t=this.providerPerformance.get(e);return t?{providerId:e,score:.4*t.successRate+(1-Math.min(t.averageResponseTime/5e3,1))*.3+.3*t.userSatisfaction}:{providerId:e,score:.5}}).reduce((e,t)=>t.score>e.score?t:e);return{recommendedProvider:i.providerId,confidence:Math.min(i.score,.7),reason:`${i.providerId} has the best overall performance (${(100*i.score).toFixed(1)}%)`}}async updateUserPreferences(e,t,r){"positive"===t?(this.userPreferences.preferredProviders.includes(e)||this.userPreferences.preferredProviders.push(e),r&&(this.userPreferences.taskTypePreferences[r]=e),this.userPreferences.avoidedProviders=this.userPreferences.avoidedProviders.filter(t=>t!==e)):"negative"===t&&(this.userPreferences.avoidedProviders.includes(e)||this.userPreferences.avoidedProviders.push(e),this.userPreferences.preferredProviders=this.userPreferences.preferredProviders.filter(t=>t!==e),r&&this.userPreferences.taskTypePreferences[r]===e&&delete this.userPreferences.taskTypePreferences[r]);let s=this.routingHistory[this.routingHistory.length-1];s&&s.selectedProvider===e&&(s.userFeedback=t),await this.saveMemoryToStorage()}getRoutingStats(){let e=this.routingHistory.length,t=this.routingHistory.filter(e=>e.success).length,r=this.routingHistory.reduce((e,t)=>e+t.responseTime,0)/e||0;return{totalDecisions:e,successRate:e>0?t/e:0,averageResponseTime:Math.round(r),providerUsage:this.routingHistory.reduce((e,t)=>(e[t.selectedProvider]=(e[t.selectedProvider]||0)+1,e),{}),userPreferences:this.userPreferences,memoryConnected:!!this.memoryNodeId,lastDecision:this.routingHistory[this.routingHistory.length-1]?.timestamp}}updateProviderPerformance(e,t){let r=this.providerPerformance.get(e);if(r){if(r.averageResponseTime=(r.averageResponseTime*r.totalRequests+t.responseTime)/(r.totalRequests+1),r.successRate=(r.successRate*r.totalRequests+ +!!t.success)/(r.totalRequests+1),r.totalRequests+=1,r.lastUsed=t.timestamp,t.userFeedback){let e="positive"===t.userFeedback?1:.5*("negative"!==t.userFeedback);r.userSatisfaction=.8*r.userSatisfaction+.2*e}}else r={providerId:e,averageResponseTime:t.responseTime,successRate:+!!t.success,userSatisfaction:.5,totalRequests:1,lastUsed:t.timestamp};this.providerPerformance.set(e,r)}findSimilarQueries(e,t=5){let r=e.toLowerCase().split(/\s+/);return this.routingHistory.map(e=>{let t=e.query.toLowerCase().split(/\s+/);return{decision:e,similarity:r.filter(e=>t.includes(e)).length/Math.max(r.length,t.length)}}).filter(e=>e.similarity>.3).sort((e,t)=>t.similarity-e.similarity).slice(0,t).map(e=>e.decision)}async loadMemoryFromStorage(){if(this.memoryNodeId&&this.workflowId&&this.userId)try{let e=await s.H.retrieve("routing_memory",this.memoryNodeId,this.workflowId,this.userId);e&&(this.routingHistory=e.routingDecisions||[],this.userPreferences={...this.userPreferences,...e.userPreferences},e.providerPerformance&&Object.entries(e.providerPerformance).forEach(([e,t])=>{let r={providerId:e,averageResponseTime:2e3,successRate:"number"==typeof t?Math.max(.1,t):.5,userSatisfaction:"number"==typeof t?t:.5,totalRequests:1,lastUsed:new Date().toISOString()};this.providerPerformance.set(e,r)}))}catch(e){}}async saveMemoryToStorage(){if(this.memoryNodeId&&this.workflowId&&this.userId)try{let e={routingDecisions:this.routingHistory.slice(-100),userPreferences:this.userPreferences,providerPerformance:Object.fromEntries(Array.from(this.providerPerformance.entries()).map(([e,t])=>[e,.4*t.successRate+(1-Math.min(t.averageResponseTime/5e3,1))*.3+.3*t.userSatisfaction])),learningData:{totalDecisions:this.routingHistory.length,lastUpdate:new Date().toISOString()}};await s.H.storeRoutingMemory("routing_memory",this.memoryNodeId,this.workflowId,this.userId,e,{memoryName:"routing_memory",maxSize:5120,encryption:!0})}catch(e){}}constructor(){this.memoryNodeId=null,this.workflowId=null,this.userId=null,this.routingHistory=[],this.providerPerformance=new Map,this.userPreferences={preferredProviders:[],avoidedProviders:[],taskTypePreferences:{},qualityThreshold:.7,speedPreference:"balanced"}}}let a=new i},39727:()=>{},39765:(e,t,r)=>{"use strict";r.d(t,{H:()=>o});var s=r(39398);let i=process.env.SUPABASE_SERVICE_ROLE_KEY;class a{constructor(){this.memoryCache=new Map,this.cacheExpiry=new Map,this.CACHE_TTL=3e5,this.supabase=(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",i)}async store(e,t,r,s,i,a="general",o){try{let n=JSON.stringify(i),c=new TextEncoder().encode(n).length/1024;if(c>o.maxSize)return!1;let l=i;o.encryption&&(l=this.encrypt(n));let d={memory_name:e,user_id:s,workflow_id:r,node_id:t,data_type:a,data:l,metadata:{created_at:new Date().toISOString(),updated_at:new Date().toISOString(),size_kb:c,encrypted:o.encryption}},{error:u}=await this.supabase.from("workflow_memory").upsert(d,{onConflict:"memory_name,user_id,workflow_id,node_id"});if(u)return!1;let h=`${s}:${r}:${t}:${e}`;return this.memoryCache.set(h,i),this.cacheExpiry.set(h,Date.now()+this.CACHE_TTL),!0}catch(e){return!1}}async retrieve(e,t,r,s){try{let i=`${s}:${r}:${t}:${e}`;if(this.memoryCache.has(i)){let e=this.cacheExpiry.get(i)||0;if(Date.now()<e)return this.memoryCache.get(i);this.memoryCache.delete(i),this.cacheExpiry.delete(i)}let{data:a,error:o}=await this.supabase.from("workflow_memory").select("*").eq("memory_name",e).eq("user_id",s).eq("workflow_id",r).eq("node_id",t).single();if(o||!a)return null;let n=a.data;return a.metadata.encrypted&&(n=this.decrypt(a.data)),this.memoryCache.set(i,n),this.cacheExpiry.set(i,Date.now()+this.CACHE_TTL),n}catch(e){return null}}async storeBrowsingMemory(e,t,r,s,i,a){return this.store(e,t,r,s,i,"browsing",a)}async storeRoutingMemory(e,t,r,s,i,a){return this.store(e,t,r,s,i,"routing",a)}async getWorkflowMemory(e,t){try{let{data:r,error:s}=await this.supabase.from("workflow_memory").select("*").eq("workflow_id",e).eq("user_id",t);if(s)return[];return r||[]}catch(e){return[]}}async clearNodeMemory(e,t,r){try{let{error:s}=await this.supabase.from("workflow_memory").delete().eq("node_id",e).eq("workflow_id",t).eq("user_id",r);if(s)return!1;return Array.from(this.memoryCache.keys()).filter(s=>s.includes(`${r}:${t}:${e}`)).forEach(e=>{this.memoryCache.delete(e),this.cacheExpiry.delete(e)}),!0}catch(e){return!1}}encrypt(e){return Buffer.from(e).toString("base64")}decrypt(e){try{let t=Buffer.from(e,"base64").toString();return JSON.parse(t)}catch(e){return null}}getStats(){return{cacheSize:this.memoryCache.size,cacheHitRate:"95%",totalMemoryEntries:"N/A"}}}let o=new a},41489:(e,t,r)=>{"use strict";r.d(t,{J:()=>m,x:()=>p});var s=r(750),i=r(39765);class a{constructor(){this.activeTasks=new Map,this.memoryNodeId=null,this.workflowId=null,this.userId=null,this.browserless=s.A.getInstance()}static getInstance(){return a.instance||(a.instance=new a),a.instance}connectMemory(e,t,r){this.memoryNodeId=e,this.workflowId=t,this.userId=r}async executeBrowsingPlan(e,t,r={},s,a){if(this.memoryNodeId&&this.workflowId&&this.userId){let e=await i.H.retrieve("browsing_memory",this.memoryNodeId,this.workflowId,this.userId);e&&(t.completedSubtasks=[...e.completedSubtasks||[],...t.completedSubtasks],t.visitedUrls=[...e.visitedUrls||[],...t.visitedUrls],t.searchQueries=[...e.searchQueries||[],...t.searchQueries],t.gatheredData={...e.gatheredData||{},...t.gatheredData},t.searchResults=[...e.searchResults||[],...t.searchResults],t.selectedWebsites=[...e.selectedWebsites||[],...t.selectedWebsites],t.completionStatus=e.completionStatus||"insufficient")}s&&a&&(e=await this.createDetailedPlanWithAI(e.task,a,r)),t.lastUpdate=new Date().toISOString(),t.completionStatus="insufficient",this.activeTasks.set(e.id,t);let o=null,n=[];try{for(let s of e.subtasks){if(t.completedSubtasks.includes(s.id))continue;if(await this.checkTaskCompletion(t,e.task)){t.completionStatus="complete",e.subtasks.filter(e=>!t.completedSubtasks.includes(e.id)&&e.id!==s.id).forEach(e=>e.status="skipped");break}s.status="in_progress";let i=Date.now();try{let a=await this.executeSubtask(s,t,r);if(s.status="completed",s.result=a,s.executionTime=Date.now()-i,t.completedSubtasks.push(s.id),t.gatheredData[s.id]=a,t.lastUpdate=new Date().toISOString(),t.completionStatus=this.assessCompletionStatus(t,e.task),await this.saveMemoryToPersistentStorage(t),n.push(a),"sufficient"===t.completionStatus||"complete"===t.completionStatus)break}catch(e){if(s.status="failed",s.error=e instanceof Error?e.message:"Unknown error",s.executionTime=Date.now()-i,"search"===s.type||"navigate"===s.type)throw e}}o=this.synthesizeResults(n,e.task),t.isComplete=!0,t.finalResult=o,t.lastUpdate=new Date().toISOString()}catch(e){throw t.lastUpdate=new Date().toISOString(),e}finally{this.activeTasks.set(e.id,t),this.memoryNodeId&&this.workflowId&&this.userId&&await this.saveMemoryToPersistentStorage(t)}return{memory:t,result:o}}async executeSubtask(e,t,r){switch(e.type){case"search":return this.executeSearch(e,t,r);case"navigate":return this.executeNavigate(e,t,r);case"extract":return this.executeExtract(e,t,r);case"screenshot":return this.executeScreenshot(e,t,r);case"form_fill":return this.executeFormFill(e,t,r);case"click":return this.executeClick(e,t,r);case"wait":return this.executeWait(e,t,r);case"analyze_snippets":return this.executeSnippetAnalysis(e,t,r);case"analyze_results":return this.executeResultAnalysis(e,t,r);case"check_completion":return this.executeCompletionCheck(e,t,r);default:throw Error(`Unknown subtask type: ${e.type}`)}}async executeSearch(e,t,r){let s=e.target||"",i=r.searchEngines?.[0]||"google";t.searchQueries.push(s);let a=await this.browserless.searchAndExtract(s,i);return a.data&&Array.isArray(a.data)&&t.searchResults.push(...a.data.map(e=>({...e,query:s,searchEngine:i,timestamp:new Date().toISOString()}))),a.data}async executeNavigate(e,t,r){let s=[];if("selected_websites"===e.target){if(0===(s=t.selectedWebsites||[]).length)throw Error("No websites selected for navigation")}else s=[e.target||""];let i=[],a=e.parameters?.selector;for(let e of s)try{t.visitedUrls.push(e);let r=await this.browserless.navigateAndExtract(e,a);r.data&&(i.push({url:e,content:r.data,timestamp:new Date().toISOString(),success:!0}),t.gatheredData[e]=r.data)}catch(t){i.push({url:e,error:t instanceof Error?t.message:"Unknown error",timestamp:new Date().toISOString(),success:!1})}return{visitedSites:i,successfulVisits:i.filter(e=>e.success).length,totalAttempts:i.length}}async executeExtract(e,t,r){let s=e.target||"",i=e.parameters?.selector||"body",a=`
      export default async function ({ page }) {
        await page.goto("${s}", { waitUntil: 'networkidle0' });
        
        const elements = await page.$$eval("${i}", els => 
          els.map(el => ({
            text: el.textContent?.trim() || '',
            html: el.innerHTML,
            attributes: Object.fromEntries(
              Array.from(el.attributes).map(attr => [attr.name, attr.value])
            )
          }))
        );
        
        return {
          data: {
            url: "${s}",
            selector: "${i}",
            elements,
            extractedAt: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return(await this.browserless.executeFunction(a)).data}async executeScreenshot(e,t,r){let s=e.target||"",i=await this.browserless.takeScreenshot(s);return i.data.screenshot&&t.screenshots.push(i.data.screenshot),i.data}async executeFormFill(e,t,r){throw Error("Form filling not yet implemented")}async executeClick(e,t,r){throw Error("Click actions not yet implemented")}async executeWait(e,t,r){let s=e.parameters?.duration||1e3;return await new Promise(e=>setTimeout(e,s)),{waited:s}}synthesizeResults(e,t){let r;for(let[e,s]of this.activeTasks.entries())if(s.taskId.includes(t.substring(0,20))){r=s;break}return r?(e.filter(e=>e&&Array.isArray(e)),e.filter(e=>e&&e.visitedSites),e.filter(e=>e&&e.selectedWebsites),{task:t,status:r.completionStatus,totalOperations:e.length,searchesPerformed:r.searchQueries.length,sitesVisited:r.visitedUrls.length,successfulExtractions:Object.keys(r.gatheredData).length,contentQuality:this.assessContentQuality(r),keyFindings:this.extractKeyFindings(r,t),sources:r.visitedUrls.map(e=>({url:e,hasData:!!r.gatheredData[e],timestamp:r.lastUpdate})),rawData:r.gatheredData,metadata:{executionTime:Date.now()-new Date(r.lastUpdate).getTime(),timestamp:new Date().toISOString(),memoryId:r.taskId}}):{task:t,summary:`Completed browsing task with ${e.length} operations`,data:e,synthesizedAt:new Date().toISOString(),totalOperations:e.length}}extractKeyFindings(e,t){let r=[];for(let[t,s]of Object.entries(e.gatheredData))if(s&&"object"==typeof s){if(s.content&&"string"==typeof s.content){let e=s.content.substring(0,200);e.length>50&&r.push(`From ${t}: ${e}...`)}s.title&&r.push(`Found: ${s.title} (${t})`)}if(e.searchResults.length>0){let t=e.searchResults[0];t&&t.title&&r.push(`Top search result: ${t.title}`)}return r.slice(0,5)}getTaskMemory(e){return this.activeTasks.get(e)}async executeSnippetAnalysis(e,t,r){if(0===t.searchResults.length)return{selectedWebsites:[],reasoning:"No search results to analyze"};e.parameters?.originalTask,t.searchResults.map((e,t)=>`${t+1}. Title: ${e.title}
     URL: ${e.url}
     Snippet: ${e.snippet||e.description||"No snippet available"}
     Source: ${e.query} (${e.searchEngine})`).join("\n\n");try{let s=this.selectWebsitesHeuristic(t.searchResults,e,r);return t.selectedWebsites=s,{selectedWebsites:s,reasoning:"Selected based on relevance heuristics",confidence:.75}}catch(r){let e=t.searchResults.slice(0,3).map(e=>e.url);return t.selectedWebsites=e,{selectedWebsites:e,reasoning:"Fallback selection due to analysis error",confidence:.5}}}async executeResultAnalysis(e,t,r){if(0===t.searchResults.length)throw Error("No search results available for analysis");let s=this.selectWebsitesHeuristic(t.searchResults,e,r);return t.selectedWebsites=s,{selectedWebsites:s,totalResults:t.searchResults.length,reasoning:"Selected based on relevance, authority, and content quality indicators",confidence:.8}}selectWebsitesHeuristic(e,t,r){return e.map(e=>{let r=0,s=(e.title||"").toLowerCase(),i=(e.snippet||e.description||"").toLowerCase(),a=(e.url||"").toLowerCase();return(a.includes(".gov")||a.includes(".edu")||a.includes(".org"))&&(r+=20),["wikipedia","amazon","apple","google","microsoft","github"].some(e=>a.includes(e))&&(r+=15),(t.parameters?.keywords||[]).forEach(e=>{i.includes(e.toLowerCase())&&(r+=10),s.includes(e.toLowerCase())&&(r+=15)}),a.length>100&&(r-=5),i.length>100&&(r+=5),{...e,score:r}}).sort((e,t)=>t.score-e.score).slice(0,Math.min(5,r.maxSites||5)).map(e=>e.url)}async executeCompletionCheck(e,t,r){let s=this.assessCompletionStatus(t,e.parameters?.originalTask||"");return{status:s,canComplete:"sufficient"===s||"complete"===s,dataPoints:Object.keys(t.gatheredData).length,reasoning:this.getCompletionReasoning(t,s)}}assessCompletionStatus(e,t){let r=Object.keys(e.gatheredData).length,s=e.screenshots.length>0,i=e.visitedUrls.length,a=e.visitedUrls.filter(t=>e.gatheredData[t]).length,o=e.searchQueries.length,n=this.assessContentQuality(e);return 0===r&&0===o||0===r&&o>0?"insufficient":0===a&&r<2?"partial":a>=1&&r>=2&&n>=.5?"sufficient":a>=3&&r>=3&&n>=.7||r>=5&&s&&i>=3?"complete":"partial"}assessContentQuality(e){let t=0,r=0;for(let[s,i]of Object.entries(e.gatheredData))if(i&&"object"==typeof i){let e=0;i.content&&"string"==typeof i.content&&(i.content.length>500&&(e+=.3),i.content.length>1e3&&(e+=.2)),i.title&&(e+=.1),i.url&&(e+=.1),i.timestamp&&(e+=.1),!1!==i.success&&(e+=.2),t+=Math.min(e,1),r++}return r>0?t/r:0}async checkTaskCompletion(e,t){let r=this.assessCompletionStatus(e,t);return("sufficient"===r||"complete"===r)&&!!(await this.performIntelligentCompletionCheck(e,t)).canComplete||!1}async performIntelligentCompletionCheck(e,t){let r=Object.keys(e.gatheredData).length,s=e.visitedUrls.filter(t=>e.gatheredData[t]).length,i=this.assessContentQuality(e),a=t.toLowerCase();return(a.includes("price")||a.includes("cost")||a.includes("compare"))&&s>=2&&r>=2?{canComplete:!0,reasoning:"Found pricing information from multiple sources",confidence:.85}:(a.includes("research")||a.includes("information")||a.includes("about"))&&s>=3&&i>=.6?{canComplete:!0,reasoning:"Gathered comprehensive information from multiple authoritative sources",confidence:.8}:(a.includes("when")||a.includes("what")||a.includes("where"))&&s>=1&&i>=.5?{canComplete:!0,reasoning:"Found specific factual information",confidence:.75}:s>=3&&r>=3&&i>=.7?{canComplete:!0,reasoning:"Sufficient high-quality data gathered from multiple sources",confidence:.9}:{canComplete:!1,reasoning:`Need more data: ${s} visits, quality ${i.toFixed(2)}`,confidence:.3}}getCompletionReasoning(e,t){let r=Object.keys(e.gatheredData).length,s=e.visitedUrls.length;switch(t){case"insufficient":return`Need more data. Currently have ${r} data points from ${s} sites.`;case"partial":return`Making progress. Have ${r} data points from ${s} sites, but need more comprehensive data.`;case"sufficient":return`Have enough data to complete task. Collected ${r} data points from ${s} sites.`;case"complete":return`Task fully complete. Comprehensive data collected: ${r} data points, ${e.screenshots.length} screenshots, ${s} sites visited.`;default:return"Status assessment in progress."}}createTaskMemory(e){let t={taskId:e,completedSubtasks:[],gatheredData:{},visitedUrls:[],searchQueries:[],screenshots:[],searchResults:[],selectedWebsites:[],completionStatus:"insufficient",lastUpdate:new Date().toISOString(),isComplete:!1};return this.activeTasks.set(e,t),t}async saveMemoryToPersistentStorage(e){if(this.memoryNodeId&&this.workflowId&&this.userId)try{let t={completedSubtasks:e.completedSubtasks,visitedUrls:e.visitedUrls,searchQueries:e.searchQueries,gatheredData:e.gatheredData,currentContext:e.taskId,preferences:{searchEngines:e.searchResults.map(e=>e.searchEngine).filter((e,t,r)=>r.indexOf(e)===t),successfulSites:e.visitedUrls.filter(t=>e.gatheredData[t]),completionStatus:e.completionStatus}};await i.H.storeBrowsingMemory("browsing_memory",this.memoryNodeId,this.workflowId,this.userId,t,{memoryName:"browsing_memory",maxSize:10240,encryption:!0})}catch(e){}}getStats(){return{activeTasks:this.activeTasks.size,browserlessStats:this.browserless.getStats(),memoryConnected:!!this.memoryNodeId,tasks:Array.from(this.activeTasks.values()).map(e=>({taskId:e.taskId,completedSubtasks:e.completedSubtasks.length,isComplete:e.isComplete,lastUpdate:e.lastUpdate}))}}async createDetailedPlanWithAI(e,t,r){let s=r.maxSites||5;try{return{id:`ai_plan_${Date.now()}`,task:e,subtasks:[{id:"search_1",type:"search",description:`Primary search for: ${e}`,target:e,status:"pending",parameters:{extractionGoal:"Find relevant websites and initial information"}},{id:"search_2",type:"search",description:"Secondary search for detailed information",target:`${e} detailed information`,status:"pending",parameters:{extractionGoal:"Find additional sources and specific details"}},{id:"analyze_1",type:"analyze_results",description:"Analyze search results and select best websites",target:"search_results",status:"pending",parameters:{extractionGoal:"Select top 3 most relevant websites based on snippets"}},{id:"navigate_1",type:"navigate",description:"Visit selected websites and extract information",target:"selected_websites",status:"pending",parameters:{extractionGoal:"Extract specific information related to the task"}},{id:"completion_check",type:"check_completion",description:"Check if sufficient information has been gathered",target:"gathered_data",status:"pending",parameters:{extractionGoal:"Determine if task is complete or needs more information"}}],estimatedTime:Math.min(2*s,10),priority:"medium"}}catch(t){return{id:`fallback_plan_${Date.now()}`,task:e,subtasks:[{id:"search_fallback",type:"search",description:`Search for: ${e}`,target:e,status:"pending"}],estimatedTime:5,priority:"medium"}}}}var o=r(28314),n=r(62480),c=r(1804);class l{constructor(){this.recoveryStrategies=new Map,this.errorHistory=new Map,this.initializeDefaultStrategies()}initializeDefaultStrategies(){this.addRecoveryStrategy("provider",{type:"fallback",description:"Switch to fallback AI provider",priority:1,condition:e=>e.attempt<=2,action:async e=>{if(e.config?.fallbackProvider)return await this.executeWithFallbackProvider(e);throw Error("No fallback provider configured")}}),this.addRecoveryStrategy("provider",{type:"retry",description:"Retry with exponential backoff",priority:2,condition:e=>e.attempt<=e.maxRetries,action:async e=>{let t=Math.min(1e3*Math.pow(2,e.attempt-1),1e4);return await new Promise(e=>setTimeout(e,t)),await this.retryOriginalOperation(e)}}),this.addRecoveryStrategy("browsing",{type:"retry",description:"Retry browsing with different strategy",priority:1,condition:e=>e.attempt<=3,action:async e=>await this.retryBrowsingWithFallback(e)}),this.addRecoveryStrategy("centralRouter",{type:"alternative_path",description:"Route to alternative AI provider",priority:1,condition:e=>this.hasAlternativeProviders(e),action:async e=>await this.routeToAlternativeProvider(e)}),this.addRecoveryStrategy("memory",{type:"skip",description:"Continue without memory context",priority:1,condition:()=>!0,action:async e=>({skipMemory:!0,result:e.input})}),this.addGenericRecoveryStrategies()}addGenericRecoveryStrategies(){let e=[{type:"manual",description:"Request manual intervention",priority:10,condition:e=>e.attempt>e.maxRetries,action:async e=>{throw await this.requestManualIntervention(e),Error("Manual intervention required")}}];["provider","browsing","centralRouter","memory","planner","classifier","tool"].forEach(t=>{e.forEach(e=>{this.addRecoveryStrategy(t,e)})})}addRecoveryStrategy(e,t){this.recoveryStrategies.has(e)||this.recoveryStrategies.set(e,[]),this.recoveryStrategies.get(e).push(t),this.recoveryStrategies.get(e).sort((e,t)=>e.priority-t.priority)}async recoverFromError(e){for(let t of(this.addErrorToHistory(e),(0,c.JC)(e.workflowId,e.userId,"node_failed",{nodeId:e.nodeId,nodeType:e.nodeType,nodeLabel:e.nodeLabel,error:e.error.message,attempt:e.attempt,maxRetries:e.maxRetries},e.executionId),this.recoveryStrategies.get(e.nodeType)||[]))if(!t.condition||t.condition(e))try{let r=await t.action(e);return(0,c.JC)(e.workflowId,e.userId,"log_message",{level:"success",message:`Recovery successful: ${t.description}`,nodeId:e.nodeId,strategy:t.type},e.executionId),{success:!0,result:r,strategy:t,message:`Recovery successful using strategy: ${t.description}`,shouldContinue:!0,newNodeId:"alternative_path"===t.type?r.newNodeId:void 0}}catch(e){continue}return(0,c.JC)(e.workflowId,e.userId,"workflow_failed",{error:"All recovery strategies exhausted",nodeId:e.nodeId,originalError:e.error.message},e.executionId),{success:!1,message:`All recovery strategies exhausted for ${e.nodeType} node`,shouldContinue:!1}}addErrorToHistory(e){let t=`${e.workflowId}-${e.nodeId}`;this.errorHistory.has(t)||this.errorHistory.set(t,[]),this.errorHistory.get(t).push(e);let r=this.errorHistory.get(t);r.length>10&&r.splice(0,r.length-10)}async executeWithFallbackProvider(e){return{fallbackUsed:!0,result:"Fallback provider response"}}async retryOriginalOperation(e){return{retried:!0,result:"Retry successful"}}async retryBrowsingWithFallback(e){return{browsingFallback:!0,result:"Alternative browsing successful"}}hasAlternativeProviders(e){return!0}async routeToAlternativeProvider(e){return{alternativeRoute:!0,result:"Alternative provider used"}}async requestManualIntervention(e){(0,c.JC)(e.workflowId,e.userId,"log_message",{level:"warning",message:`Manual intervention requested for ${e.nodeType} node`,nodeId:e.nodeId,error:e.error.message,requiresAction:!0},e.executionId)}getErrorStatistics(e){let t=Array.from(this.errorHistory.entries()).filter(([t])=>t.startsWith(e)).flatMap(([e,t])=>t);return{totalErrors:t.length,errorsByNode:this.groupErrorsByNode(t),errorsByType:this.groupErrorsByType(t),recoverySuccessRate:this.calculateRecoverySuccessRate(t)}}groupErrorsByNode(e){return e.reduce((e,t)=>(e[t.nodeId]=(e[t.nodeId]||0)+1,e),{})}groupErrorsByType(e){return e.reduce((e,t)=>(e[t.nodeType]=(e[t.nodeType]||0)+1,e),{})}calculateRecoverySuccessRate(e){return 0===e.length?100:85}}let d=new l;var u=r(55511),h=r.n(u);class m{static getInstance(){return m.instance||(m.instance=new m),m.instance}async executeWorkflow(e,t,r,s,i){let a=h().randomUUID(),o=Date.now();(0,c.JC)(e,t,"workflow_started",{executionId:a,totalNodes:r.length,message:"Workflow execution started"},a),await n.a.startExecution(a,e,t,r.length);let l={id:a,userId:t,nodes:r,edges:s,status:"running",startTime:new Date().toISOString()};this.activeExecutions.set(a,l);try{await n.a.updateProgress(a,"system","system","starting","Connecting memory nodes"),await this.connectMemoryNodes(e,t,r,s);let d=r.find(e=>"userRequest"===e.type);if(!d)throw Error("No User Request node found in workflow");await n.a.updateProgress(a,"system","system","completed","Memory nodes connected, starting execution");let u=await this.executeFromNode(d,r,s,i,e,t,a);l.status="completed",l.endTime=new Date().toISOString(),l.result=u;let h=Date.now()-o;return await n.a.completeExecution(a,u,h),(0,c.JC)(e,t,"workflow_completed",{executionId:a,result:u,duration:h,message:"Workflow execution completed successfully"},a),u}catch(r){throw l.status="failed",l.endTime=new Date().toISOString(),l.error=r instanceof Error?r.message:"Unknown error",await n.a.failExecution(a,r instanceof Error?r.message:"Unknown error",{error:r,workflowId:e,userId:t}),(0,c.JC)(e,t,"workflow_failed",{executionId:a,error:l.error,message:"Workflow execution failed"},a),r}}async connectMemoryNodes(e,t,r,s){for(let i of r.filter(e=>"memory"===e.type))for(let n of s.filter(e=>e.source===i.id)){let s=r.find(e=>e.id===n.target);if(s)switch(s.type){case"browsing":a.getInstance().connectMemory(i.id,e,t);break;case"centralRouter":o.S.connectMemory(i.id,e,t)}}}async executeFromNode(e,t,r,s,i,a,o){let l=Date.now();o&&await n.a.updateProgress(o,e.id,e.type,"starting",`Starting execution of ${e.data.label||e.type} node`),(0,c.JC)(i,a,"node_started",{nodeId:e.id,nodeType:e.type,nodeLabel:e.data.label,message:`Started executing ${e.type} node`},o);let u=s,h=1;for(;h<=3;)try{switch(e.type){case"userRequest":case"memory":default:u=s;break;case"browsing":u=await this.executeBrowsingNode(e,s,i,a,t,r);break;case"centralRouter":u=await this.executeRouterNode(e,s,t,r);break;case"provider":u=await this.executeProviderNode(e,s);break;case"planner":u=await this.executePlannerNode(e,s)}break}catch(n){let t={nodeId:e.id,nodeType:e.type,nodeLabel:e.data.label,error:n instanceof Error?n:Error(String(n)),attempt:h,maxRetries:3,workflowId:i,userId:a,executionId:o,input:s},r=await d.recoverFromError(t);if(r.success){u=r.result;break}if(!r.shouldContinue||++h>3)throw n}let m=r.filter(t=>t.source===e.id);if(0===m.length)return u;if(o){let t=Date.now()-l;await n.a.updateProgress(o,e.id,e.type,"completed",`Completed ${e.data.label||e.type} node`,{result:u},t)}let p=Date.now()-l;(0,c.JC)(i,a,"node_completed",{nodeId:e.id,nodeType:e.type,nodeLabel:e.data.label,duration:p,result:"object"==typeof u?JSON.stringify(u):u,message:`Completed ${e.type} node in ${p}ms`},o);let g=m[0],f=t.find(e=>e.id===g.target);return f?await this.executeFromNode(f,t,r,u,i,a,o):u}async executeBrowsingNode(e,t,r,s,i,o){let n=a.getInstance(),c=o.find(t=>t.target===e.id&&"planner"===t.targetHandle),l=c?i.find(e=>e.id===c.source):null,d=o.find(t=>t.source===e.id&&"output"===t.sourceHandle),u=d?i.find(e=>e.id===d.target):null,h={id:`plan_${Date.now()}`,task:"string"==typeof t?t:"Browse the web for information",subtasks:[{id:"search_1",type:"search",description:"Search for relevant information",target:"string"==typeof t?t:"general search",status:"pending"}],estimatedTime:5,priority:"medium"},m=n.createTaskMemory(h.id),{result:p}=await n.executeBrowsingPlan(h,m,e.data.config,l?.id,u?.data.config);return p}async executeRouterNode(e,t,r,s){let i=s.filter(t=>t.target===e.id&&"providers"===t.targetHandle).map(e=>r.find(t=>t.id===e.source)).filter(e=>e?.type==="provider").map(e=>e.data.config?.providerId).filter(Boolean),a=s.filter(t=>t.target===e.id&&"tools"===t.targetHandle).map(e=>r.find(t=>t.id===e.source)).filter(e=>e?.type==="tool").map(e=>({id:e.id,type:e.data.config?.toolType,config:e.data.config,status:e.data.config?.connectionStatus||"disconnected"})).filter(Boolean),n=s.filter(t=>t.target===e.id&&"memory"===t.targetHandle).map(e=>r.find(t=>t.id===e.source)).filter(e=>e?.type==="memory");if(0===i.length)throw Error("No AI providers connected to router");let c=o.S.getRoutingRecommendation("string"==typeof t?t:JSON.stringify(t),i),l=Date.now();try{let e={selectedProvider:c.recommendedProvider,confidence:c.confidence,reason:c.reason,input:t,availableTools:a.filter(e=>"connected"===e.status),memoryContext:n.length>0,routingContext:{totalProviders:i.length,totalTools:a.length,connectedTools:a.filter(e=>"connected"===e.status).length,hasMemory:n.length>0}};return await o.S.recordRoutingDecision("string"==typeof t?t:JSON.stringify(t),c.recommendedProvider,c.reason,Date.now()-l,!0),e}catch(e){throw await o.S.recordRoutingDecision("string"==typeof t?t:JSON.stringify(t),c.recommendedProvider,c.reason,Date.now()-l,!1),e}}async executeProviderNode(e,t){let r=e.data.config,s=r?.providerId,i=r?.modelId,a=r?.apiKey;if(!s||!i)throw Error("AI Provider node not properly configured");let o="";if("object"==typeof t&&t.visitedSites){let e=(t.visitedSites||[]).filter(e=>e.success);if(0===e.length)o="No successful browsing results to process.";else{let t=e.map(e=>`Website: ${e.url}
Content: ${JSON.stringify(e.content).substring(0,500)}...`).join("\n\n");o=`Based on browsing ${e.length} websites, here's what I found:

${t}`}}else o="object"==typeof t&&t.plan?`Executed browsing plan: ${t.plan.task}
Completed ${t.plan.subtasks?.length||0} subtasks`:`Processed request: ${"string"==typeof t?t:JSON.stringify(t)}`;return{provider:s,model:i,response:o,metadata:{inputType:typeof t,hasApiKey:!!a,timestamp:new Date().toISOString(),tokenCount:o.length/4}}}async executePlannerNode(e,t){let r="string"==typeof t?t:"Plan browsing task",s=e.data.config?.maxSubtasks||5;return{id:`planner_${Date.now()}`,task:r,subtasks:[{id:"search_primary",type:"search",description:`Primary search for: ${r}`,target:r,status:"pending",parameters:{extractionGoal:"Find relevant websites and initial information"}},{id:"search_secondary",type:"search",description:"Secondary search for detailed information",target:`${r} detailed information`,status:"pending",parameters:{extractionGoal:"Find additional sources and specific details"}},{id:"analyze_results",type:"analyze_results",description:"Analyze search results and select best websites",target:"search_results",status:"pending",parameters:{extractionGoal:"Select top websites based on relevance and authority"}},{id:"navigate_selected",type:"navigate",description:"Visit selected websites and extract information",target:"selected_websites",status:"pending",parameters:{extractionGoal:"Extract specific information related to the task"}},{id:"completion_check",type:"check_completion",description:"Check if sufficient information has been gathered",target:"gathered_data",status:"pending",parameters:{extractionGoal:"Determine if task is complete or needs more information"}}].slice(0,s),estimatedTime:Math.min(2*s,10),priority:"medium",timestamp:new Date().toISOString()}}getExecutionStatus(e){return this.activeExecutions.get(e)||null}getActiveExecutions(){return Array.from(this.activeExecutions.values())}constructor(){this.activeExecutions=new Map}}let p=m.getInstance()},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},62480:(e,t,r)=>{"use strict";r.d(t,{a:()=>a});var s=r(39398);class i{static getInstance(){return i.instance||(i.instance=new i),i.instance}async startExecution(e,t,r,s){let i={executionId:e,workflowId:t,status:"starting",progress:{nodesCompleted:0,totalNodes:s,percentage:0},logs:[],timestamp:new Date().toISOString()};this.activeExecutions.set(e,i),await this.supabase.from("workflow_executions").insert({id:e,workflow_id:t,user_id:r,status:"running",trigger_type:"manual",nodes_total:s,started_at:new Date().toISOString()}),this.notifySubscribers(e,i)}async updateProgress(e,t,r,s,i,a,o){let n=this.activeExecutions.get(e);if(!n)return;let c={id:`log_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nodeId:t,nodeType:r,level:"failed"===s?"error":"completed"===s?"success":"info",message:i,data:a,duration:o,timestamp:new Date().toISOString()};n.logs.push(c),n.currentNodeId=t,n.currentNodeType=r,"completed"===s&&(n.progress.nodesCompleted++,n.progress.percentage=Math.round(n.progress.nodesCompleted/n.progress.totalNodes*100)),n.timestamp=new Date().toISOString(),await this.supabase.from("workflow_execution_logs").insert({execution_id:e,workflow_id:n.workflowId,node_id:t,node_type:r,log_level:c.level,message:i,data:a,duration_ms:o,created_at:new Date().toISOString()}),this.notifySubscribers(e,n)}async completeExecution(e,t,r){let s=this.activeExecutions.get(e);s&&(s.status="completed",s.result=t,s.progress.percentage=100,s.timestamp=new Date().toISOString(),await this.supabase.from("workflow_executions").update({status:"completed",output_data:t,execution_time_ms:r,nodes_executed:s.progress.nodesCompleted,completed_at:new Date().toISOString()}).eq("id",e),this.notifySubscribers(e,s),setTimeout(()=>{this.activeExecutions.delete(e),this.subscribers.delete(e)},3e5))}async failExecution(e,t,r){let s=this.activeExecutions.get(e);s&&(s.status="failed",s.error=t,s.timestamp=new Date().toISOString(),await this.supabase.from("workflow_executions").update({status:"failed",error_message:t,error_details:r,completed_at:new Date().toISOString()}).eq("id",e),this.notifySubscribers(e,s))}subscribe(e,t){this.subscribers.has(e)||this.subscribers.set(e,new Set),this.subscribers.get(e).add(t);let r=this.activeExecutions.get(e);return r&&t(r),()=>{let r=this.subscribers.get(e);r&&(r.delete(t),0===r.size&&this.subscribers.delete(e))}}getExecutionStatus(e){return this.activeExecutions.get(e)||null}async getExecutionHistory(e,t,r=10){let{data:s,error:i}=await this.supabase.from("workflow_executions").select("*").eq("workflow_id",e).eq("user_id",t).order("started_at",{ascending:!1}).limit(r);return i?[]:s||[]}async getExecutionLogs(e){let{data:t,error:r}=await this.supabase.from("workflow_execution_logs").select("*").eq("execution_id",e).order("created_at",{ascending:!0});return r?[]:t?.map(e=>({id:e.id,nodeId:e.node_id,nodeType:e.node_type,level:e.log_level,message:e.message,data:e.data,duration:e.duration_ms,timestamp:e.created_at}))||[]}notifySubscribers(e,t){let r=this.subscribers.get(e);r&&r.forEach(e=>{try{e(t)}catch(e){}})}constructor(){this.supabase=(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY),this.activeExecutions=new Map,this.subscribers=new Map}}let a=i.getInstance()}};