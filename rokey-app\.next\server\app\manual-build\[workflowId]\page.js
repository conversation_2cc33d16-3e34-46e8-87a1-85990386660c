(()=>{var e={};e.id=4833,e.ids=[4833],e.modules={2969:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24245:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))})},26403:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35133:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=r(65239),s=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["manual-build",{children:["[workflowId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72539)),"C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\[workflowId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\[workflowId]\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/manual-build/[workflowId]/page",pathname:"/manual-build/[workflowId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},37132:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},44108:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))})},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},50159:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>en});var a=r(60687),s=r(43210),n=r(16189),i=r(15022),l=r(59922);r(71763);var o=r(44725),d=r(50515),c=r(51426),m=r(6854),u=r(89114),x=r(24245);function p({workflow:e,isDirty:t,isSaving:r,onSave:n,onExecute:i,onBack:l,onShare:p}){let[g,h]=(0,s.useState)(!1);return(0,a.jsxs)("div",{className:"bg-gray-900/80 backdrop-blur-sm border-b border-gray-700/50 px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50",title:"Back to workflows",children:(0,a.jsx)(o.A,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:"h-6 w-px bg-gray-700"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-lg font-semibold text-white",children:e?.name||"New Workflow"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-400",children:[t&&(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"Unsaved changes"]}),e?.updated_at&&(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(d.A,{className:"w-3 h-3"}),"Last saved ",new Date(e.updated_at).toLocaleTimeString()]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>h(!g),className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50",title:"Workflow settings",children:(0,a.jsx)(c.A,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:p,disabled:!e?.id||!p,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50 disabled:opacity-50 disabled:cursor-not-allowed",title:"Share workflow",children:(0,a.jsx)(m.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("button",{onClick:n,disabled:r||!t,className:`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${t&&!r?"bg-blue-600 hover:bg-blue-500 text-white":"bg-gray-700 text-gray-400 cursor-not-allowed"}`,children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),r?"Saving...":"Save"]}),(0,a.jsxs)("button",{onClick:()=>{e?.id?window.open("/playground/workflows","_blank"):alert("Please save the workflow first to test it in the playground")},className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),"Test in Playground"]})]})]}),g&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700/50",children:[(0,a.jsx)("h3",{className:"text-white font-medium mb-3",children:"Workflow Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Max Execution Time"}),(0,a.jsxs)("select",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,a.jsx)("option",{value:"300",children:"5 minutes"}),(0,a.jsx)("option",{value:"600",children:"10 minutes"}),(0,a.jsx)("option",{value:"1800",children:"30 minutes"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Retry Count"}),(0,a.jsxs)("select",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,a.jsx)("option",{value:"1",children:"1 retry"}),(0,a.jsx)("option",{value:"3",children:"3 retries"}),(0,a.jsx)("option",{value:"5",children:"5 retries"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Options"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0",defaultChecked:!0}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable memory"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0",defaultChecked:!0}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable streaming"})]})]})]})]})]})]})}var g=r(64908),h=r(68589),b=r(61245),f=r(48544),y=r(66524),v=r(93635),j=r(55510),w=r(96374),N=r(49579),k=r(62392),C=r(36920),A=r(74461),S=r(70143),T=r(27010),E=r(37132);let I={core:{label:"Core Nodes",description:"Essential workflow components",nodes:[{type:"userRequest",label:"User Request",description:"Starting point for user input",icon:g.A,isAvailable:!0,defaultData:{label:"User Request",config:{},isConfigured:!0}},{type:"classifier",label:"Classifier",description:"Analyzes and categorizes requests",icon:h.A,isAvailable:!0,defaultData:{label:"Classifier",config:{},isConfigured:!0}},{type:"output",label:"Output",description:"Final response to user",icon:b.A,isAvailable:!0,defaultData:{label:"Output",config:{},isConfigured:!0}}]},ai:{label:"AI Providers",description:"AI model integrations",nodes:[{type:"provider",label:"AI Provider",description:"Connect to AI models (OpenAI, Claude, etc.)",icon:f.A,isAvailable:!0,defaultData:{label:"AI Provider",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},isConfigured:!1}},{type:"vision",label:"Vision AI",description:"Multimodal AI for image analysis and vision tasks",icon:y.A,isAvailable:!0,defaultData:{label:"Vision AI",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},isConfigured:!1}},{type:"roleAgent",label:"Role Agent",description:"Role plugin for AI providers (connect to role input)",icon:v.A,isAvailable:!0,defaultData:{label:"Role Agent",config:{roleId:"",roleName:"",roleType:"predefined",customPrompt:"",memoryEnabled:!1},isConfigured:!1}},{type:"centralRouter",label:"Central Router",description:"Smart routing hub for multiple AI providers and vision models",icon:h.A,isAvailable:!0,defaultData:{label:"Central Router",config:{routingStrategy:"smart",fallbackProvider:"",maxRetries:3,timeout:3e4,enableCaching:!0,debugMode:!1},isConfigured:!0}},{type:"planner",label:"Planner",description:"AI model that creates browsing strategies and todo lists",icon:j.A,isAvailable:!0,defaultData:{label:"Planner",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:.7,maxTokens:1e3},maxSubtasks:10},isConfigured:!1}}]},logic:{label:"Logic & Control",description:"Flow control and decision making",nodes:[{type:"conditional",label:"Conditional",description:"Branch workflow based on conditions",icon:w.A,isAvailable:!0,defaultData:{label:"Conditional",config:{},isConfigured:!1}},{type:"merge",label:"Merge",description:"Combine multiple inputs",icon:N.A,isAvailable:!0,defaultData:{label:"Merge",config:{},isConfigured:!0}},{type:"switch",label:"Switch",description:"Route to different paths",icon:k.A,isAvailable:!0,defaultData:{label:"Switch",config:{},isConfigured:!1}},{type:"loop",label:"Loop",description:"Repeat operations",icon:N.A,isAvailable:!0,defaultData:{label:"Loop",config:{},isConfigured:!1}}]},tools:{label:"Tools & Integrations",description:"External service integrations",nodes:[{type:"tool",label:"Tools",description:"External tool integrations (Google Drive, Zapier, etc.)",icon:C.A,isAvailable:!0,defaultData:{label:"Tools",config:{toolType:"",toolConfig:{},timeout:30,connectionStatus:"disconnected",isAuthenticated:!1},isConfigured:!1}},{type:"memory",label:"Memory",description:"Store and retrieve data across workflow executions",icon:A.A,isAvailable:!0,defaultData:{label:"Memory",config:{memoryName:"",maxSize:10240,encryption:!0,description:""},isConfigured:!1}}]},browsing:{label:"Web Browsing",description:"Intelligent web browsing and automation",nodes:[{type:"browsing",label:"Browsing Agent",description:"Intelligent web browsing agent with multi-step automation",icon:S.A,isAvailable:!0,defaultData:{label:"Browsing Agent",config:{maxSites:5,timeout:30,enableScreenshots:!0,enableFormFilling:!0,enableCaptchaSolving:!1,searchEngines:["google"],maxDepth:2,respectRobots:!0,enableJavaScript:!0},isConfigured:!0}}]}};function D({node:e,onAddNode:t}){let r=e.icon;return(0,a.jsx)("div",{draggable:!0,onDragStart:t=>{t.dataTransfer.setData("application/reactflow",e.type),t.dataTransfer.effectAllowed="move"},onClick:()=>{t(e.type)},className:`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${e.isAvailable?"bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50":"bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed"}`,title:e.description,children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:`p-2 rounded-lg ${e.isAvailable?"bg-[#ff6b35]/20 text-[#ff6b35]":"bg-gray-700/50 text-gray-500"}`,children:(0,a.jsx)(r,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:`font-medium text-sm ${e.isAvailable?"text-white":"text-gray-500"}`,children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-400 truncate",children:e.description})]})]})})}function M({category:e,data:t,isExpanded:r,onToggle:s,onAddNode:n}){return(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("button",{onClick:s,className:"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[r?(0,a.jsx)(T.A,{className:"w-4 h-4 text-gray-400"}):(0,a.jsx)(E.A,{className:"w-4 h-4 text-gray-400"}),(0,a.jsx)("span",{className:"font-medium text-white",children:t.label})]}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:t.nodes.length})]}),r&&(0,a.jsx)("div",{className:"mt-2 space-y-2",children:t.nodes.map(e=>(0,a.jsx)(D,{node:e,onAddNode:n},e.type))})]})}function R({onAddNode:e}){let[t,r]=(0,s.useState)(new Set(["core","ai"])),n=e=>{let a=new Set(t);a.has(e)?a.delete(e):a.add(e),r(a)},i=t=>{e(t,{x:400,y:200})};return(0,a.jsxs)("div",{className:"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-2",children:"Node Palette"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Drag nodes to the canvas or click to add at center"})]}),(0,a.jsx)("div",{className:"space-y-1",children:Object.entries(I).map(([e,r])=>(0,a.jsx)(M,{category:e,data:r,isExpanded:t.has(e),onToggle:()=>n(e),onAddNode:i},e))}),(0,a.jsxs)("div",{className:"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-blue-300 font-medium mb-1",children:"\uD83D\uDCA1 Pro Tip"}),(0,a.jsx)("div",{className:"text-xs text-blue-200",children:"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node."})]})]})}var P=r(95753),_=r(81836),L=r(66368),F=r(62525);let O=L.MG.map(e=>({value:e.id,label:e.name}));function q({node:e,onUpdate:t,onClose:r}){let[n,i]=(0,s.useState)(e.data.config),[l,o]=(0,s.useState)(null),[d,m]=(0,s.useState)(!1),[u,x]=(0,s.useState)(null),[p,g]=(0,s.useState)([]),[h,b]=(0,s.useState)(!1),[f,y]=(0,s.useState)(null);(0,s.useCallback)(async()=>{m(!0),x(null),o(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?o(t.models):o([])}catch(e){x(e.message),o([])}finally{m(!1)}},[]),(0,s.useCallback)(async()=>{b(!0),y(null);try{let e=await fetch("/api/user/custom-roles");if(!e.ok)throw Error("Failed to fetch custom roles");let t=await e.json();g(t)}catch(e){y(e.message),g([])}finally{b(!1)}},[]);let v=(r,a)=>{let s={...n,[r]:a};i(s),t({config:s,isConfigured:k(e.type,s)})},j=(r,a)=>{let s={...n,[r]:a};"parameters"!==r&&n.parameters||(s.parameters={maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0,temperature:1,...n.parameters,..."parameters"===r?a:{}}),i(s),t({config:s,isConfigured:k(e.type,s)})},w=(0,s.useMemo)(()=>{if(l&&("provider"===e.type||"vision"===e.type||"planner"===e.type)){let t=L.MG.find(e=>e.id===n.providerId);if(!t)return[];let r=t=>"vision"===e.type?t.filter(e=>e.modality&&(e.modality.includes("multimodal")||e.modality.includes("vision")||e.modality.includes("image"))):t;if("openrouter"===t.id)return r(l).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===t.id){let t=[],r=l.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id);r&&("provider"===e.type||"planner"===e.type||"vision"===e.type&&r.modality?.includes("multimodal"))&&t.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"});let a=l.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id);return a&&("provider"===e.type||"planner"===e.type||"vision"===e.type&&a.modality?.includes("multimodal"))&&t.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),t.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return r(l.filter(e=>e.provider_id===t.id)).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[l,n,e.type]),N=(0,s.useMemo)(()=>{if(!l||"provider"!==e.type&&"vision"!==e.type&&"planner"!==e.type||!n?.modelId)return{maxTokens:4096,minTokens:1};let t=l.find(e=>e.id===n.modelId);return t?{maxTokens:t.output_token_limit||t.context_window||4096,minTokens:1}:{maxTokens:4096,minTokens:1}},[l,n,e.type]),k=(e,t)=>{switch(e){case"provider":case"vision":case"planner":return!!(t.providerId&&t.modelId&&t.apiKey);case"roleAgent":if("new"===t.roleType)return!!(t.newRoleName&&t.customPrompt);return!!(t.roleId&&t.roleName);case"centralRouter":return!!t.routingStrategy;case"conditional":return!!(t.condition&&t.conditionType);case"tool":return!!t.toolType;case"browsing":default:return!0;case"memory":return!!t.memoryName;case"switch":return!!(t.switchType&&t.cases?.length>0);case"loop":return!!t.loopType}},C=()=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,a.jsxs)("select",{value:n?.providerId||"",onChange:r=>{let a={...n,providerId:r.target.value,modelId:"",parameters:n.parameters||{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,a.jsx)("option",{value:"",children:"Select Provider"}),O.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,a.jsx)("input",{type:"password",value:n?.apiKey||"",onChange:e=>j("apiKey",e.target.value),placeholder:"Enter your API key for this provider",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",required:!0}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"}),d&&null===l&&(0,a.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),u&&(0,a.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",u]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,a.jsx)("select",{value:n?.modelId||"",onChange:r=>{let a=r.target.value,s={...n,modelId:a};if(a&&l){let e=l.find(e=>e.id===a);if(e){let t=e.output_token_limit||e.context_window||4096,r=Math.min(t,Math.max(1024,Math.floor(.75*t))),a=n?.parameters||{};s={...s,parameters:{...a,maxTokens:r}}}}i(s),t({config:s,isConfigured:k(e.type,s)})},disabled:!n?.providerId||!w.length,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30",children:n?.providerId?w.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("option",{value:"",children:"Select Model"}),w.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,a.jsx)("option",{value:"",disabled:!0,children:d?"Loading models...":"No models available"}):(0,a.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,a.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||1,onChange:e=>{let t=parseFloat(e.target.value);j("parameters",{...n?.parameters||{},temperature:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||1,onChange:e=>{let t=Math.min(2,Math.max(0,parseFloat(e.target.value)||1));j("parameters",{...n?.parameters||{},temperature:t})},className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"maxTokens",className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens",(0,a.jsxs)("span",{className:"text-xs text-gray-400 ml-1",children:["(",N.minTokens," - ",N.maxTokens.toLocaleString(),")"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"range",id:"maxTokens",min:N.minTokens,max:N.maxTokens,step:"1",value:n?.parameters?.maxTokens||N.maxTokens,onChange:e=>{let t=parseInt(e.target.value);j("parameters",{...n?.parameters||{},maxTokens:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Minimal"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"number",min:N.minTokens,max:N.maxTokens,step:"1",value:n?.parameters?.maxTokens||N.maxTokens,onChange:e=>{let t=Math.min(N.maxTokens,Math.max(N.minTokens,parseInt(e.target.value)||N.maxTokens));j("parameters",{...n?.parameters||{},maxTokens:t})},className:"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"}),(0,a.jsx)("button",{type:"button",onClick:()=>{j("parameters",{...n?.parameters||{},maxTokens:N.maxTokens})},className:"text-xs text-orange-400 hover:text-orange-300 underline",children:"Max"})]}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Maximum"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more."})]})]}),n?.providerId==="openrouter"&&(0,a.jsxs)("div",{className:"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-blue-300 font-medium mb-1",children:"\uD83C\uDF10 OpenRouter"}),(0,a.jsx)("div",{className:"text-xs text-blue-200",children:"Access to 300+ models from multiple providers with a single API key."})]})]}),A=()=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,a.jsxs)("select",{value:n?.providerId||"",onChange:r=>{let a={...n,providerId:r.target.value,modelId:"",parameters:n.parameters||{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,a.jsx)("option",{value:"",children:"Select Provider"}),O.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,a.jsx)("input",{type:"password",value:n?.apiKey||"",onChange:e=>j("apiKey",e.target.value),placeholder:"Enter your API key for this provider",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",required:!0}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"}),d&&null===l&&(0,a.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,a.jsx)(P.A,{className:"w-4 h-4 mr-2"}),"Fetching models..."]}),u&&(0,a.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",u]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Vision Model",(0,a.jsx)("span",{className:"text-xs text-purple-400 ml-1",children:"(Multimodal Only)"})]}),(0,a.jsx)("select",{value:n?.modelId||"",onChange:r=>{let a=r.target.value,s={...n,modelId:a};if(a&&l){let e=l.find(e=>e.id===a);if(e){let t=e.output_token_limit||e.context_window||4096,r=Math.min(t,Math.max(1024,Math.floor(.75*t))),a=n?.parameters||{};s={...s,parameters:{...a,maxTokens:r}}}}i(s),t({config:s,isConfigured:k(e.type,s)})},disabled:!n?.providerId||!w.length,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30",children:n?.providerId?w.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("option",{value:"",children:"Select Vision Model"}),w.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,a.jsx)("option",{value:"",disabled:!0,children:d?"Loading models...":"No vision models available"}):(0,a.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})}),0===w.length&&n?.providerId&&!d&&(0,a.jsx)("p",{className:"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg",children:"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:"Temperature (0.0 - 2.0)"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||1,onChange:e=>{let t=parseFloat(e.target.value);j("parameters",{...n?.parameters||{},temperature:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||1,onChange:e=>{let t=Math.min(2,Math.max(0,parseFloat(e.target.value)||1));j("parameters",{...n?.parameters||{},temperature:t})},className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"maxTokens",className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens",(0,a.jsxs)("span",{className:"text-xs text-gray-400 ml-1",children:["(",N.minTokens," - ",N.maxTokens.toLocaleString(),")"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"range",id:"maxTokens",min:N.minTokens,max:N.maxTokens,step:"1",value:n?.parameters?.maxTokens||N.maxTokens,onChange:e=>{let t=parseInt(e.target.value);j("parameters",{...n?.parameters||{},maxTokens:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Minimal"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"number",min:N.minTokens,max:N.maxTokens,step:"1",value:n?.parameters?.maxTokens||N.maxTokens,onChange:e=>{let t=Math.min(N.maxTokens,Math.max(N.minTokens,parseInt(e.target.value)||N.maxTokens));j("parameters",{...n?.parameters||{},maxTokens:t})},className:"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"}),(0,a.jsx)("button",{type:"button",onClick:()=>{j("parameters",{...n?.parameters||{},maxTokens:N.maxTokens})},className:"text-xs text-orange-400 hover:text-orange-300 underline",children:"Max"})]}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"Maximum"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Controls the maximum number of tokens the model can generate for vision analysis."})]})]}),n?.providerId==="openrouter"&&(0,a.jsxs)("div",{className:"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-purple-300 font-medium mb-1",children:"\uD83D\uDC41️ Vision Models"}),(0,a.jsx)("div",{className:"text-xs text-purple-200",children:"Access to multimodal models from multiple providers for image analysis and vision tasks."})]})]}),S=()=>{let r=[...F.p2.map(e=>({id:e.id,name:e.name,description:e.description,type:"predefined"})),...p.map(e=>({id:e.role_id,name:e.name,description:e.description,type:"custom"}))],s=a=>{if("create_new"===a){let r={...n,roleType:"new",roleId:"",roleName:"",newRoleName:"",newRoleDescription:"",customPrompt:""};i(r),t({config:r,isConfigured:k(e.type,r)})}else{let s=r.find(e=>e.id===a);if(s){let r={...n,roleType:s.type,roleId:s.id,roleName:s.name,customPrompt:s.description||""};i(r),t({config:r,isConfigured:k(e.type,r)})}}},l=(r,a)=>{let s={...n,[r]:a};i(s),t({config:s,isConfigured:k(e.type,s)})};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Role"}),h?(0,a.jsx)("div",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400",children:"Loading roles..."}):(0,a.jsxs)("select",{value:n?.roleType==="new"?"create_new":n?.roleId||"",onChange:e=>s(e.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,a.jsx)("option",{value:"",children:"Select a role..."}),(0,a.jsx)("optgroup",{label:"System Roles",children:F.p2.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))}),p.length>0&&(0,a.jsx)("optgroup",{label:"Your Custom Roles",children:p.map(e=>(0,a.jsx)("option",{value:e.role_id,children:e.name},e.role_id))}),(0,a.jsx)("optgroup",{label:"Create New",children:(0,a.jsx)("option",{value:"create_new",children:"+ Create New Role"})})]}),f&&(0,a.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error loading roles: ",f]})]}),n?.roleType!=="new"&&n?.roleId&&(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-white mb-1",children:n.roleName}),n.customPrompt&&(0,a.jsx)("div",{className:"text-xs text-gray-300",children:n.customPrompt})]}),n?.roleType==="new"&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Role Name"}),(0,a.jsx)("input",{type:"text",value:n.newRoleName||"",onChange:e=>l("newRoleName",e.target.value),placeholder:"e.g., Data Analyst, Creative Writer",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Role Description"}),(0,a.jsx)("input",{type:"text",value:n.newRoleDescription||"",onChange:e=>l("newRoleDescription",e.target.value),placeholder:"Brief description of this role's purpose",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),(0,a.jsx)("textarea",{value:n.customPrompt||"",onChange:e=>l("customPrompt",e.target.value),placeholder:"Enter detailed instructions for this role...",rows:4,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.memoryEnabled||!1,onChange:e=>v("memoryEnabled",e.target.checked),className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable memory"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1 ml-6",children:"Allow this role to remember context from previous interactions"})]})]})},T=()=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Condition Type"}),(0,a.jsxs)("select",{value:n.conditionType||"",onChange:e=>v("conditionType",e.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,a.jsx)("option",{value:"",children:"Select Type"}),(0,a.jsx)("option",{value:"contains",children:"Contains"}),(0,a.jsx)("option",{value:"equals",children:"Equals"}),(0,a.jsx)("option",{value:"regex",children:"Regex"}),(0,a.jsx)("option",{value:"length",children:"Length"}),(0,a.jsx)("option",{value:"custom",children:"Custom"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Condition"}),(0,a.jsx)("input",{type:"text",value:n.condition||"",onChange:e=>v("condition",e.target.value),placeholder:"Enter condition...",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"True Label"}),(0,a.jsx)("input",{type:"text",value:n.trueLabel||"",onChange:e=>v("trueLabel",e.target.value),placeholder:"Continue",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"False Label"}),(0,a.jsx)("input",{type:"text",value:n.falseLabel||"",onChange:e=>v("falseLabel",e.target.value),placeholder:"Skip",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]})]}),E=()=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,a.jsx)("input",{type:"text",value:e.data.label,onChange:e=>t({label:e.target.value}),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description"}),(0,a.jsx)("textarea",{value:e.data.description||"",onChange:e=>t({description:e.target.value}),rows:3,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]}),I=()=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Routing Strategy"}),(0,a.jsxs)("select",{value:n?.routingStrategy||"smart",onChange:r=>{let a={...n,routingStrategy:r.target.value};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,a.jsx)("option",{value:"smart",children:"Smart Routing"}),(0,a.jsx)("option",{value:"round_robin",children:"Round Robin"}),(0,a.jsx)("option",{value:"load_balanced",children:"Load Balanced"}),(0,a.jsx)("option",{value:"priority",children:"Priority Based"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"How the router selects between available AI providers"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Retries"}),(0,a.jsx)("input",{type:"number",min:"0",max:"10",value:n?.maxRetries||3,onChange:r=>{let a={...n,maxRetries:parseInt(r.target.value)||3};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Number of retry attempts on failure"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (ms)"}),(0,a.jsx)("input",{type:"number",min:"1000",max:"300000",step:"1000",value:n?.timeout||3e4,onChange:r=>{let a={...n,timeout:parseInt(r.target.value)||3e4};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Request timeout in milliseconds"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Enable Caching"}),(0,a.jsx)("input",{type:"checkbox",checked:n?.enableCaching??!0,onChange:r=>{let a={...n,enableCaching:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Cache responses to improve performance"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Debug Mode"}),(0,a.jsx)("input",{type:"checkbox",checked:n?.debugMode??!1,onChange:r=>{let a={...n,debugMode:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Enable detailed logging for debugging"})]})]}),D=()=>{let r=[{value:"",label:"Select a tool..."},{value:"google_drive",label:"\uD83D\uDCC1 Google Drive",description:"Access and manage Google Drive files"},{value:"google_docs",label:"\uD83D\uDCC4 Google Docs",description:"Create and edit Google Documents"},{value:"google_sheets",label:"\uD83D\uDCCA Google Sheets",description:"Work with Google Spreadsheets"},{value:"zapier",label:"⚡ Zapier",description:"Connect with 5000+ apps via Zapier"},{value:"notion",label:"\uD83D\uDCDD Notion",description:"Access Notion databases and pages"},{value:"calendar",label:"\uD83D\uDCC5 Calendar",description:"Manage calendar events and schedules"},{value:"gmail",label:"\uD83D\uDCE7 Gmail",description:"Send and manage emails"},{value:"youtube",label:"\uD83D\uDCFA YouTube",description:"Access YouTube data and analytics"},{value:"supabase",label:"\uD83D\uDDC4️ Supabase",description:"Direct database operations"}];return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Tool Type"}),(0,a.jsx)("select",{value:n?.toolType||"",onChange:r=>{let a={...n,toolType:r.target.value,toolConfig:{},connectionStatus:"disconnected",isAuthenticated:!1};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:r.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))}),n?.toolType&&(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:r.find(e=>e.value===n.toolType)?.description})]}),n?.toolType&&(0,a.jsxs)("div",{className:"space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"text-yellow-400",children:"●"}),(0,a.jsx)("span",{className:"text-sm font-medium text-yellow-400",children:"Authentication Required"})]}),(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-400 mb-2",children:[r.find(e=>e.value===n.toolType)?.label," integration coming soon!"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"This tool will require account linking and authentication."})]})]}),n?.toolType&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),(0,a.jsx)("input",{type:"number",min:"5",max:"300",value:n?.timeout||30,onChange:r=>{let a={...n,timeout:parseInt(r.target.value)||30};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum time to wait for the tool operation to complete"})]})]})},M=()=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,a.jsxs)("select",{value:n?.providerId||"",onChange:r=>{let a={...n,providerId:r.target.value,modelId:"",parameters:n.parameters||{temperature:.7,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select Provider"}),(0,a.jsx)("option",{value:"openai",children:"OpenAI"}),(0,a.jsx)("option",{value:"anthropic",children:"Anthropic"}),(0,a.jsx)("option",{value:"google",children:"Google"}),(0,a.jsx)("option",{value:"deepseek",children:"DeepSeek"}),(0,a.jsx)("option",{value:"xai",children:"xAI"}),(0,a.jsx)("option",{value:"openrouter",children:"OpenRouter"})]})]}),n?.providerId&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,a.jsx)("select",{value:n?.modelId||"",onChange:r=>{let a=r.target.value,s={...n,modelId:a};if(a&&l){let e=l.find(e=>e.id===a);if(e){let t=e.output_token_limit||e.context_window||4096,r=Math.min(t,Math.max(1024,Math.floor(.75*t))),a=n?.parameters||{};s={...s,parameters:{...a,maxTokens:r}}}}i(s),t({config:s,isConfigured:k(e.type,s)})},disabled:!n?.providerId||!w.length,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent disabled:opacity-50 disabled:bg-gray-800/30",children:n?.providerId?w.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("option",{value:"",children:"Select Model"}),w.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,a.jsx)("option",{value:"",disabled:!0,children:d?"Loading models...":"No models available"}):(0,a.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})}),d&&(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Loading models..."}),u&&(0,a.jsx)("p",{className:"text-xs text-red-400 mt-1",children:u})]}),n?.modelId&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,a.jsx)("input",{type:"password",value:n?.apiKey||"",onChange:r=>{let a={...n,apiKey:r.target.value};i(a),t({config:a,isConfigured:k(e.type,a)})},placeholder:"Enter your API key for this provider",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",required:!0}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"})]}),n?.modelId&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens: ",n?.parameters?.maxTokens||"Auto"]}),(0,a.jsx)("input",{type:"range",min:N.minTokens,max:N.maxTokens,value:n?.parameters?.maxTokens||N.maxTokens,onChange:r=>{let a={...n,parameters:{...n.parameters,maxTokens:parseInt(r.target.value)}};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,a.jsx)("span",{children:N.minTokens}),(0,a.jsx)("span",{children:N.maxTokens})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",n?.parameters?.temperature||.7]}),(0,a.jsx)("input",{type:"range",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||.7,onChange:r=>{let a={...n,parameters:{...n.parameters,temperature:parseFloat(r.target.value)}};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,a.jsx)("span",{children:"0 (Focused)"}),(0,a.jsx)("span",{children:"2 (Creative)"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Subtasks"}),(0,a.jsx)("input",{type:"number",min:"1",max:"50",value:n?.maxSubtasks||10,onChange:r=>{let a={...n,maxSubtasks:parseInt(r.target.value)||10};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum number of subtasks the planner can create"})]})]}),R=()=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-green-900/20 border border-green-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"text-green-400",children:"●"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-400",children:"Intelligent Browsing Agent"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-300",children:"This node automatically plans and executes complex web browsing tasks using AI."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Sites to Visit"}),(0,a.jsx)("input",{type:"number",min:"1",max:"20",value:n?.maxSites||5,onChange:r=>{let a={...n,maxSites:parseInt(r.target.value)||5};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout per Operation (seconds)"}),(0,a.jsx)("input",{type:"number",min:"10",max:"300",value:n?.timeout||30,onChange:r=>{let a={...n,timeout:parseInt(r.target.value)||30};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Basic Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Sites"}),(0,a.jsx)("input",{type:"number",min:"1",max:"20",value:n?.maxSites||5,onChange:r=>{let a={...n,maxSites:parseInt(r.target.value)};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),(0,a.jsx)("input",{type:"number",min:"10",max:"300",value:n?.timeout||30,onChange:r=>{let a={...n,timeout:parseInt(r.target.value)};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Engines"}),(0,a.jsx)("div",{className:"flex gap-2",children:["google","bing"].map(r=>(0,a.jsxs)("label",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.searchEngines?.includes(r)??"google"===r,onChange:a=>{let s=n?.searchEngines||["google"],l=a.target.checked?[...s.filter(e=>e!==r),r]:s.filter(e=>e!==r),o={...n,searchEngines:l.length>0?l:["google"]};i(o),t({config:o,isConfigured:k(e.type,o)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300 capitalize",children:r})]},r))})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Capabilities"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.enableScreenshots??!0,onChange:r=>{let a={...n,enableScreenshots:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCF8 Take Screenshots"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.enableFormFilling??!0,onChange:r=>{let a={...n,enableFormFilling:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCDD Fill Forms Automatically"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.enableCaptchaSolving??!1,onChange:r=>{let a={...n,enableCaptchaSolving:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDD10 Solve CAPTCHAs"}),(0,a.jsx)("span",{className:"text-xs text-yellow-400",children:"(Beta)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.enableJavaScript??!0,onChange:r=>{let a={...n,enableJavaScript:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"⚡ Enable JavaScript"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.respectRobots??!0,onChange:r=>{let a={...n,respectRobots:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83E\uDD16 Respect robots.txt"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Advanced Settings"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Browsing Depth"}),(0,a.jsx)("input",{type:"number",min:"1",max:"5",value:n?.maxDepth||2,onChange:r=>{let a={...n,maxDepth:parseInt(r.target.value)};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"How many levels deep to follow links (1-5)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom User Agent"}),(0,a.jsx)("input",{type:"text",value:n?.userAgent||"",onChange:r=>{let a={...n,userAgent:r.target.value};i(a),t({config:a,isConfigured:k(e.type,a)})},placeholder:"Leave empty for default",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Custom user agent string (optional)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Extraction Goals"}),(0,a.jsx)("textarea",{value:n?.extractionGoals?.join(", ")||"",onChange:r=>{let a=r.target.value.split(",").map(e=>e.trim()).filter(e=>e),s={...n,extractionGoals:a};i(s),t({config:s,isConfigured:k(e.type,s)})},placeholder:"prices, contact info, products, links",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Comma-separated list of what to extract (e.g., prices, contact, products)"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.enableFormFilling??!0,onChange:r=>{let a={...n,enableFormFilling:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCDD Fill Forms Automatically"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.enableCaptchaSolving??!1,onChange:r=>{let a={...n,enableCaptchaSolving:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDD10 Attempt CAPTCHA Solving"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Engines"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.searchEngines?.includes("google")??!0,onChange:r=>{let a=n?.searchEngines||["google"],s=r.target.checked?[...a.filter(e=>"google"!==e),"google"]:a.filter(e=>"google"!==e),l={...n,searchEngines:s.length>0?s:["google"]};i(l),t({config:l,isConfigured:k(e.type,l)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"Google"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.searchEngines?.includes("bing")??!1,onChange:r=>{let a=n?.searchEngines||["google"],s=r.target.checked?[...a.filter(e=>"bing"!==e),"bing"]:a.filter(e=>"bing"!==e),l={...n,searchEngines:s.length>0?s:["google"]};i(l),t({config:l,isConfigured:k(e.type,l)})},className:"rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:"Bing"})]})]})]})]}),q=()=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-300",children:"Plug & Play Memory"})]}),(0,a.jsx)("p",{className:"text-xs text-blue-200/80",children:"This memory node automatically acts as a brain for any connected node. It handles storing, retrieving, session data, and persistent memory intelligently without manual configuration."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Memory Name *"}),(0,a.jsx)("input",{type:"text",value:n?.memoryName||"",onChange:r=>{let a={...n,memoryName:r.target.value};i(a),t({config:a,isConfigured:k(e.type,a)})},placeholder:"Enter a name for this memory (e.g., Browsing Memory, Router Memory)",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",required:!0}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Give this memory a descriptive name for easy identification"})]}),n?.memoryName&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Storage Size (MB)"}),(0,a.jsx)("input",{type:"number",min:"1",max:"100",value:Math.round((n?.maxSize||10240)/1024),onChange:r=>{let a=parseInt(r.target.value)||10,s={...n,maxSize:1024*a};i(s),t({config:s,isConfigured:k(e.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum storage size limit (default: 10MB)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:n?.encryption!==!1,onChange:r=>{let a={...n,encryption:r.target.checked};i(a),t({config:a,isConfigured:k(e.type,a)})},className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable encryption (recommended)"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1 ml-6",children:"Encrypt stored data for security (enabled by default)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),(0,a.jsx)("textarea",{value:n?.description||"",onChange:r=>{let a={...n,description:r.target.value};i(a),t({config:a,isConfigured:k(e.type,a)})},placeholder:"Describe what this memory will be used for...",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Optional description of this memory's purpose"})]})]})]});return(0,a.jsxs)("div",{className:"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-[#ff6b35]/20 rounded-lg",children:(0,a.jsx)(c.A,{className:"w-5 h-5 text-[#ff6b35]"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Configure Node"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:e.data.label})]})]}),(0,a.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-white transition-colors p-1 rounded",children:(0,a.jsx)(_.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"space-y-6",children:(()=>{switch(e.type){case"provider":return C();case"vision":return A();case"roleAgent":return S();case"centralRouter":return I();case"conditional":return T();case"tool":return D();case"planner":return M();case"browsing":return R();case"memory":return q();default:return E()}})()}),(0,a.jsxs)("div",{className:"mt-6 p-3 rounded-lg border border-gray-700/50",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${e.data.isConfigured?"bg-green-500":"bg-yellow-500"}`}),(0,a.jsx)("span",{className:"text-sm font-medium text-white",children:e.data.isConfigured?"Configured":"Needs Configuration"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:e.data.isConfigured?"This node is properly configured and ready to use.":"Complete the configuration to use this node in your workflow."})]})]})}var B=r(65963),z=r(26403);let V=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.181 8.68a4.503 4.503 0 0 1 1.903 6.405m-9.768-2.782L3.56 14.06a4.5 4.5 0 0 0 6.364 6.365l3.129-3.129m5.614-5.615 1.757-1.757a4.5 4.5 0 0 0-6.364-6.365l-4.5 4.5c-.258.26-.479.541-.661.84m1.903 6.405a4.495 4.495 0 0 1-1.242-.88 4.483 4.483 0 0 1-1.062-1.683m6.587 2.345 5.907 5.907m-5.907-5.907L8.898 8.898M2.991 2.99 8.898 8.9"}))});function $({id:e,top:t,left:r,right:n,bottom:i,type:l,nodeType:o,onClose:d,onDelete:m,onDuplicate:u,onConfigure:x,onDisconnect:p}){let g=(0,s.useCallback)(e=>{e(),d()},[d]),h="edge"===l||!["userRequest","classifier","output"].includes(o||"");return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:d}),(0,a.jsxs)("div",{className:"fixed z-50 bg-gray-800 border border-gray-700 rounded-lg shadow-xl py-1 min-w-[160px]",style:{top:i?void 0:t,left:n?void 0:r,right:n?window.innerWidth-n:void 0,bottom:i?window.innerHeight-i:void 0},children:["node"===l&&(0,a.jsxs)(a.Fragment,{children:[x&&(0,a.jsxs)("button",{onClick:()=>g(()=>x(e)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"w-4 h-4"}),"Configure"]}),u&&(0,a.jsxs)("button",{onClick:()=>g(()=>u(e)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,a.jsx)(B.A,{className:"w-4 h-4"}),"Duplicate"]}),h&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,a.jsxs)("button",{onClick:()=>g(()=>m(e)),className:"w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),"Delete Node"]})]}),!h&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,a.jsx)("div",{className:"px-3 py-2 text-xs text-gray-500",children:"Core nodes cannot be deleted"})]})]}),"edge"===l&&(0,a.jsxs)(a.Fragment,{children:[p&&(0,a.jsxs)("button",{onClick:()=>g(()=>p(e)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,a.jsx)(V,{className:"w-4 h-4"}),"Disconnect"]}),(0,a.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,a.jsxs)("button",{onClick:()=>g(()=>m(e)),className:"w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),"Delete Connection"]})]})]})]})}var G=r(61596),W=r(59168);let K=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 12.75c1.148 0 2.278.08 3.383.237 1.037.146 1.866.966 1.866 2.013 0 3.728-2.35 6.75-5.25 6.75S6.75 18.728 6.75 15c0-1.046.83-1.867 1.866-2.013A24.204 24.204 0 0 1 12 12.75Zm0 0c2.883 0 5.647.508 8.207 1.44a23.91 23.91 0 0 1-1.152 6.06M12 12.75c-2.883 0-5.647.508-8.208 1.44.125 2.104.52 4.136 1.153 6.06M12 12.75a2.25 2.25 0 0 0 2.248-2.354M12 12.75a2.25 2.25 0 0 1-2.248-2.354M12 8.25c.995 0 1.971-.08 2.922-.236.403-.066.74-.358.795-.762a3.778 3.778 0 0 0-.399-2.25M12 8.25c-.995 0-1.97-.08-2.922-.236-.402-.066-.74-.358-.795-.762a3.734 3.734 0 0 1 .4-2.253M12 8.25a2.25 2.25 0 0 0-2.248 2.146M12 8.25a2.25 2.25 0 0 1 2.248 2.146M8.683 5a6.032 6.032 0 0 1-1.155-1.002c.07-.63.27-1.222.574-1.747m.581 2.749A3.75 3.75 0 0 1 15.318 5m0 0c.427-.283.815-.62 1.155-.999a4.471 4.471 0 0 0-.575-1.752M4.921 6a24.048 24.048 0 0 0-.392 3.314c1.668.546 3.416.914 5.223 1.082M19.08 6c.205 1.08.337 2.187.392 3.314a23.882 23.882 0 0 1-5.223 1.082"}))});class H extends s.Component{constructor(e){super(e),this.reportError=async(e,t)=>{try{e.message,e.stack,t.componentStack,this.state.errorId,new Date().toISOString(),navigator.userAgent,window.location.href}catch(e){}},this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null,errorId:""})},this.handleReload=()=>{window.location.reload()},this.copyErrorDetails=()=>{let e={error:this.state.error?.message,stack:this.state.error?.stack,componentStack:this.state.errorInfo?.componentStack,errorId:this.state.errorId,timestamp:new Date().toISOString()};navigator.clipboard.writeText(JSON.stringify(e,null,2)).then(()=>{alert("Error details copied to clipboard")}).catch(()=>{alert("Failed to copy error details")})},this.state={hasError:!1,error:null,errorInfo:null,errorId:""}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorId:`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t}),this.props.onError&&this.props.onError(e,t),this.reportError(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center p-6",children:(0,a.jsx)("div",{className:"max-w-2xl w-full",children:(0,a.jsxs)("div",{className:"bg-red-900/20 border border-red-700/50 rounded-lg p-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-6",children:(0,a.jsx)("div",{className:"p-4 bg-red-900/30 rounded-full",children:(0,a.jsx)(W.A,{className:"w-12 h-12 text-red-400"})})}),(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-gray-400",children:"An unexpected error occurred in the workflow editor. We've been notified and are working to fix this issue."})]}),(0,a.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(K,{className:"w-4 h-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Error ID:"}),(0,a.jsx)("code",{className:"text-gray-300 font-mono",children:this.state.errorId})]})}),this.state.error&&(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-2",children:"Error Message:"}),(0,a.jsx)("p",{className:"text-red-300 text-sm font-mono",children:this.state.error.message})]}),this.props.showDetails&&this.state.error&&(0,a.jsxs)("details",{className:"mb-6",children:[(0,a.jsx)("summary",{className:"text-gray-300 cursor-pointer hover:text-white transition-colors",children:"Technical Details"}),(0,a.jsx)("div",{className:"mt-4 bg-gray-900/50 rounded-lg p-4",children:(0,a.jsx)("div",{className:"text-xs font-mono text-gray-400 whitespace-pre-wrap overflow-auto max-h-40",children:this.state.error.stack})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsxs)("button",{onClick:this.handleRetry,className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:[(0,a.jsx)(N.A,{className:"w-5 h-5"}),"Try Again"]}),(0,a.jsxs)("button",{onClick:this.handleReload,className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(N.A,{className:"w-5 h-5"}),"Reload Page"]}),(0,a.jsxs)("button",{onClick:this.copyErrorDetails,className:"flex items-center justify-center gap-2 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(b.A,{className:"w-5 h-5"}),"Copy Details"]})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"If this problem persists, please contact support with the error ID above."})})]})})}):this.props.children}}var U=r(58089),J=r(81521),Z=r(99127);let Y=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))});function X({errors:e,onRetry:t,onSkip:r,onManualFix:n,isVisible:i,onClose:l}){let[o,c]=(0,s.useState)(new Set),[m,u]=(0,s.useState)({}),x=e=>{let t=new Set(o);t.has(e)?t.delete(e):t.add(e),c(t)},p=e=>{switch(e){case"pending":default:return(0,a.jsx)(W.A,{className:"w-5 h-5 text-yellow-400"});case"retrying":return(0,a.jsx)(d.A,{className:"w-5 h-5 text-blue-400 animate-spin"});case"recovered":return(0,a.jsx)(U.A,{className:"w-5 h-5 text-green-400"});case"failed":return(0,a.jsx)(J.A,{className:"w-5 h-5 text-red-400"});case"skipped":return(0,a.jsx)(Z.A,{className:"w-5 h-5 text-gray-400"})}},g=e=>{switch(e){case"pending":default:return"border-yellow-500 bg-yellow-900/20";case"retrying":return"border-blue-500 bg-blue-900/20";case"recovered":return"border-green-500 bg-green-900/20";case"failed":return"border-red-500 bg-red-900/20";case"skipped":return"border-gray-500 bg-gray-900/20"}},h=(e,a)=>{switch(a){case"retry":t(e);break;case"skip":r(e);break;case"manual":n(e)}};return i&&0!==e.length?(0,a.jsxs)("div",{className:"fixed bottom-4 right-4 w-96 max-h-[70vh] bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden z-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900/50",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(W.A,{className:"w-5 h-5 text-yellow-400"}),(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Error Recovery (",e.length,")"]})]}),(0,a.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(70vh-80px)]",children:e.map(e=>(0,a.jsxs)("div",{className:`border-l-4 ${g(e.status)} m-2 rounded-r-lg`,children:[(0,a.jsx)("div",{className:"p-4 cursor-pointer hover:bg-gray-700/30 transition-colors",onClick:()=>x(e.id),children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3 flex-1",children:[p(e.status),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-white",children:e.nodeLabel}),(0,a.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700 px-2 py-0.5 rounded",children:e.nodeType})]}),(0,a.jsx)("p",{className:"text-sm text-gray-300 truncate",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-400",children:[(0,a.jsxs)("span",{children:["Attempt ",e.attempt,"/",e.maxRetries]}),(0,a.jsx)("span",{children:new Date(e.timestamp).toLocaleTimeString()})]})]})]}),(0,a.jsx)("div",{className:"ml-2",children:o.has(e.id)?(0,a.jsx)(Y,{className:"w-4 h-4 text-gray-400"}):(0,a.jsx)(T.A,{className:"w-4 h-4 text-gray-400"})})]})}),o.has(e.id)&&(0,a.jsxs)("div",{className:"px-4 pb-4 border-t border-gray-700/50",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Error Details:"}),(0,a.jsx)("div",{className:"bg-gray-900/50 rounded p-3 text-sm text-gray-300 font-mono",children:e.message})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Recovery Options:"}),(0,a.jsx)("div",{className:"space-y-2",children:e.recoveryStrategies.map((t,r)=>(0,a.jsx)("div",{className:`p-3 rounded border ${t.available?t.recommended?"border-green-500/50 bg-green-900/20":"border-gray-600 bg-gray-800/50":"border-gray-700 bg-gray-900/50 opacity-50"}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-white capitalize",children:t.type.replace("_"," ")}),t.recommended&&(0,a.jsx)("span",{className:"text-xs bg-green-900/30 text-green-300 px-2 py-0.5 rounded",children:"Recommended"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:t.description})]}),t.available&&"pending"===e.status&&(0,a.jsx)("button",{onClick:()=>h(e.id,t.type),className:`ml-3 px-3 py-1 text-xs rounded transition-colors ${t.recommended?"bg-green-600 text-white hover:bg-green-500":"bg-gray-600 text-white hover:bg-gray-500"}`,children:"Apply"})]})},r))})]}),e.context&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Context:"}),(0,a.jsx)("div",{className:"bg-gray-900/50 rounded p-3 text-xs text-gray-400 font-mono max-h-20 overflow-y-auto",children:JSON.stringify(e.context,null,2)})]})]})]},e.id))}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-700 bg-gray-900/50",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>e.forEach(e=>"pending"===e.status&&t(e.id)),className:"flex-1 px-3 py-2 bg-[#ff6b35] text-white text-sm rounded hover:bg-[#ff6b35]/80 transition-colors",disabled:!e.some(e=>"pending"===e.status),children:[(0,a.jsx)(N.A,{className:"w-4 h-4 inline mr-1"}),"Retry All"]}),(0,a.jsx)("button",{onClick:()=>e.forEach(e=>"pending"===e.status&&r(e.id)),className:"flex-1 px-3 py-2 bg-gray-600 text-white text-sm rounded hover:bg-gray-500 transition-colors",disabled:!e.some(e=>"pending"===e.status),children:"Skip All"})]})})]}):null}var Q=r(50942),ee=r(71178),et=r(2969),er=r(85198),ea=r(44108);function es({workflowId:e,workflowName:t,isOpen:r,onClose:n}){let[i,l]=(0,s.useState)([]),[o,d]=(0,s.useState)(!1),[c,u]=(0,s.useState)(""),[x,p]=(0,s.useState)("view"),[g,h]=(0,s.useState)(!1),[b,f]=(0,s.useState)(""),[j,w]=(0,s.useState)(null),N=async()=>{d(!0);try{let t=await fetch(`/api/manual-build/workflows/${e}/shares`);if(t.ok){let e=await t.json();l(e.shares||[])}}catch(e){}finally{d(!1)}},k=async()=>{if(c||g)try{(await fetch(`/api/manual-build/workflows/${e}/shares`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sharedWith:c?[c]:void 0,permissionLevel:x,isPublic:g,expiresAt:b?new Date(b).toISOString():void 0})})).ok&&(u(""),h(!1),f(""),await N())}catch(e){}},C=async t=>{try{(await fetch(`/api/manual-build/workflows/${e}/shares/${t}`,{method:"DELETE"})).ok&&await N()}catch(e){}},A=async e=>{let t=`${window.location.origin}/manual-build/shared/${e}`;try{await navigator.clipboard.writeText(t),w(e),setTimeout(()=>w(null),2e3)}catch(e){}},T=e=>{switch(e){case"admin":return(0,a.jsx)(Q.A,{className:"w-4 h-4 text-red-400"});case"edit":return(0,a.jsx)(ee.A,{className:"w-4 h-4 text-yellow-400"});default:return(0,a.jsx)(y.A,{className:"w-4 h-4 text-blue-400"})}},E=e=>{switch(e){case"admin":return"text-red-400 bg-red-900/20 border-red-700/30";case"edit":return"text-yellow-400 bg-yellow-900/20 border-yellow-700/30";default:return"text-blue-400 bg-blue-900/20 border-blue-700/30"}};return r?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m.A,{className:"w-6 h-6 text-[#ff6b35]"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"Share Workflow"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:t})]})]}),(0,a.jsx)("button",{onClick:n,className:"text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(_.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Create New Share"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"shareType",checked:!g,onChange:()=>h(!1),className:"text-[#ff6b35] focus:ring-[#ff6b35]"}),(0,a.jsx)(v.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsx)("span",{className:"text-white",children:"Share with specific users"})]}),(0,a.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"shareType",checked:g,onChange:()=>h(!0),className:"text-[#ff6b35] focus:ring-[#ff6b35]"}),(0,a.jsx)(S.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsx)("span",{className:"text-white",children:"Public link"})]})]}),!g&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:c,onChange:e=>u(e.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Permission Level"}),(0,a.jsxs)("select",{value:x,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,a.jsx)("option",{value:"view",children:"View Only"}),(0,a.jsx)("option",{value:"edit",children:"Can Edit"}),(0,a.jsx)("option",{value:"admin",children:"Admin Access"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Expires At (Optional)"}),(0,a.jsx)("input",{type:"datetime-local",value:b,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,a.jsx)("button",{onClick:k,disabled:!c&&!g,className:"w-full px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Create Share Link"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Active Shares"}),o?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("div",{className:"text-gray-400",children:"Loading shares..."})}):0===i.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(m.A,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),(0,a.jsx)("div",{className:"text-gray-400",children:"No active shares"})]}):(0,a.jsx)("div",{className:"space-y-3",children:i.map(e=>(0,a.jsx)("div",{className:"bg-gray-700/50 border border-gray-600 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 flex-1",children:[e.is_public?(0,a.jsx)(S.A,{className:"w-5 h-5 text-green-400"}):(0,a.jsx)(v.A,{className:"w-5 h-5 text-blue-400"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:e.is_public?"Public Link":e.shared_with||"Unknown User"}),(0,a.jsxs)("span",{className:`px-2 py-0.5 text-xs rounded-full border ${E(e.permission_level)}`,children:[T(e.permission_level),(0,a.jsx)("span",{className:"ml-1 capitalize",children:e.permission_level})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-400",children:[(0,a.jsxs)("span",{children:["Created ",new Date(e.created_at).toLocaleDateString()]}),e.expires_at&&(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(et.A,{className:"w-3 h-3"}),"Expires ",new Date(e.expires_at).toLocaleDateString()]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>A(e.share_token),className:"p-2 text-gray-400 hover:text-white transition-colors",title:"Copy share link",children:j===e.share_token?(0,a.jsx)(ea.A,{className:"w-4 h-4 text-green-400"}):(0,a.jsx)(er.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>C(e.id),className:"p-2 text-gray-400 hover:text-red-400 transition-colors",title:"Revoke share",children:(0,a.jsx)(z.A,{className:"w-4 h-4"})})]})]})},e.id))})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-700",children:(0,a.jsx)("button",{onClick:n,className:"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Close"})})]})}):null}function en({params:e}){let t=(0,n.useParams)(),r=(0,n.useRouter)(),o=t?.workflowId,[d,c]=(0,s.useState)(null),[m,u,x]=(0,i.ck)([]),[g,h,b]=(0,i.fM)([]),[f,y]=(0,s.useState)(null),[v,j]=(0,s.useState)(!0),[w,N]=(0,s.useState)(!1),[k,C]=(0,s.useState)(!1),[A,S]=(0,s.useState)(null),[T,E]=function(e,t={}){let{autoConnect:r=!0,maxEvents:a=100,reconnectInterval:n=5e3,onEvent:i,onConnect:l,onDisconnect:o,onError:d}=t,[c,m]=(0,s.useState)({isConnected:!1,isConnecting:!1,error:null,lastEvent:null,events:[],connectionCount:0}),u=(0,s.useRef)(null),x=(0,s.useRef)(null),p=(0,s.useRef)(!1),g=(0,s.useCallback)(()=>{if(e&&!c.isConnected&&!c.isConnecting){m(e=>({...e,isConnecting:!0,error:null})),p.current=!1;try{let t=new EventSource(`/api/workflow/stream/${e}`);u.current=t,t.onopen=()=>{m(e=>({...e,isConnected:!0,isConnecting:!1,error:null,connectionCount:e.connectionCount+1})),l?.()},t.onmessage=e=>{try{let t=JSON.parse(e.data);m(e=>{let r=[...e.events,t];return r.length>a&&r.splice(0,r.length-a),{...e,lastEvent:t,events:r}}),i?.(t)}catch(e){}},t.onerror=e=>{m(e=>({...e,isConnected:!1,isConnecting:!1,error:"Connection error"})),d?.("Connection error"),!p.current&&n>0&&(x.current=setTimeout(()=>{g()},n))}}catch(e){m(e=>({...e,isConnecting:!1,error:"Failed to create connection"})),d?.("Failed to create connection")}}},[e,c.isConnected,c.isConnecting,a,n,l,i,d]),h=(0,s.useCallback)(()=>{p.current=!0,x.current&&(clearTimeout(x.current),x.current=null),u.current&&(u.current.close(),u.current=null),m(e=>({...e,isConnected:!1,isConnecting:!1,error:null})),o?.()},[e,o]),b=(0,s.useCallback)(async(t,r,a)=>{if(!e)throw Error("No workflow ID provided");try{let s=await fetch(`/api/workflow/stream/${e}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({eventType:t,data:r,executionId:a})});if(!s.ok)throw Error(`Failed to send event: ${s.statusText}`)}catch(e){throw e}},[e]),f=(0,s.useCallback)(()=>{m(e=>({...e,events:[],lastEvent:null}))},[]),y=(0,s.useCallback)(()=>{h(),setTimeout(g,100)},[h,g]);return[c,{connect:g,disconnect:h,sendEvent:b,clearEvents:f,reconnect:y}]}("new"!==o?o:null,{autoConnect:!0,onEvent:e=>{"workflow_started"===e.type||"node_started"===e.type||"node_completed"===e.type||"workflow_completed"===e.type||e.type},onConnect:()=>{},onDisconnect:()=>{},onError:e=>{}}),[I,D]=(0,s.useState)([]),[M,P]=(0,s.useState)(!1),[_,L]=(0,s.useState)(!1),F=(0,s.useCallback)(e=>{let t={...e,id:`e${g.length+1}`,type:"smoothstep",animated:!0};h(e=>(0,l.rN)(t,e)),C(!0)},[g.length,h]),O=(0,s.useCallback)((e,t)=>{y(t)},[]),B=(0,s.useCallback)(()=>{y(null),S(null)},[]),z=(0,s.useCallback)((e,t)=>{e.preventDefault(),S({id:t.id,type:"node",nodeType:t.type,x:e.clientX,y:e.clientY})},[]),V=(0,s.useCallback)((e,t)=>{e.preventDefault(),S({id:t.id,type:"edge",x:e.clientX,y:e.clientY})},[]),W=(0,s.useCallback)(e=>{["user-request","classifier","output"].includes(e)||(u(t=>t.filter(t=>t.id!==e)),h(t=>t.filter(t=>t.source!==e&&t.target!==e)),C(!0),f?.id===e&&y(null))},[f,u,h]),K=(0,s.useCallback)(e=>{h(t=>t.filter(t=>t.id!==e)),C(!0)},[h]),U=(0,s.useCallback)(e=>{let t=m.find(t=>t.id===e);if(!t)return;let r={...t,id:`${t.type}-${Date.now()}`,position:{x:t.position.x+50,y:t.position.y+50},data:{...t.data,label:`${t.data.label} Copy`}};u(e=>[...e,r]),C(!0)},[m,u]),J=(0,s.useCallback)(e=>{let t=m.find(t=>t.id===e);t&&y(t)},[m]),Z=async()=>{if(d||"new"!==o){N(!0);try{let e=await fetch("/api/workflows",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:o,name:d?.name,description:d?.description,nodes:m,edges:g,settings:d?.settings||{}})});if(!e.ok){let t=await e.json();throw Error(t.details||"Failed to update workflow")}C(!1),alert("Workflow updated successfully!")}catch(e){alert(`Failed to update workflow: ${e instanceof Error?e.message:"Unknown error"}`)}finally{N(!1)}}else{let e=prompt("Enter workflow name:");if(!e)return;let t=prompt("Enter workflow description (optional):")||"";N(!0);try{let a=await fetch("/api/workflows",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e,description:t,nodes:m,edges:g,settings:{}})});if(!a.ok){let e=await a.json();throw Error(e.details||"Failed to save workflow")}let s=await a.json();alert(`Workflow saved successfully!

Your API Key: ${s.api_key}

Save this key - it will not be shown again!`),r.push(`/manual-build/${s.workflow.id}`)}catch(e){alert(`Failed to save workflow: ${e instanceof Error?e.message:"Unknown error"}`)}finally{N(!1)}}},Y=(e,t)=>{u(r=>r.map(r=>r.id===e?{...r,data:{...r.data,...t}}:r)),C(!0)};return v?(0,a.jsx)("div",{className:"h-screen bg-[#040716] flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white",children:"Loading workflow..."})}):(0,a.jsx)(H,{showDetails:!1,onError:(e,t)=>{let r={id:`error-${Date.now()}`,nodeId:"editor",nodeType:"editor",nodeLabel:"Workflow Editor",message:e.message,timestamp:new Date().toISOString(),attempt:1,maxRetries:3,status:"pending",recoveryStrategies:[{type:"retry",description:"Reload the editor",available:!0,recommended:!0}]};D(e=>[...e,r]),P(!0)},children:(0,a.jsxs)("div",{className:"h-screen bg-[#040716] flex flex-col",children:[(0,a.jsx)(p,{workflow:d,isDirty:k,isSaving:w,onSave:Z,onExecute:()=>{d?.id?window.open("/playground/workflows","_blank"):alert("Please save the workflow first to test it in the playground")},onBack:()=>r.push("/manual-build"),onShare:()=>L(!0)}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[(0,a.jsx)(R,{onAddNode:(e,t)=>{let r={},a=!0;"provider"===e?(r={providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},a=!1):"centralRouter"===e&&(r={routingStrategy:"smart",fallbackProvider:"",maxRetries:3,timeout:3e4,enableCaching:!0,debugMode:!1},a=!0);let s={id:`${e}-${Date.now()}`,type:e,position:t,data:{label:"centralRouter"===e?"Central Router":e.charAt(0).toUpperCase()+e.slice(1),config:r,isConfigured:a,description:`${e} node`}};u(e=>[...e,s]),C(!0)}}),(0,a.jsxs)("div",{className:"flex-1 relative manual-build-canvas",children:[(0,a.jsxs)(i.Gc,{nodes:m,edges:g,onNodesChange:x,onEdgesChange:b,onConnect:F,onNodeClick:O,onNodeContextMenu:z,onEdgeContextMenu:V,onPaneClick:B,nodeTypes:G.c_,fitView:!0,className:"bg-[#040716]",defaultViewport:{x:0,y:0,zoom:.8},connectionLineStyle:{stroke:"#ff6b35",strokeWidth:2},defaultEdgeOptions:{style:{stroke:"#ff6b35",strokeWidth:2},type:"smoothstep",animated:!0},children:[(0,a.jsx)(i.VS,{color:"#1f2937",gap:20,size:1}),(0,a.jsx)(i.H2,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",showInteractive:!1}),(0,a.jsx)(i.of,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",nodeColor:"#ff6b35",maskColor:"rgba(0, 0, 0, 0.2)"})]}),A&&(0,a.jsx)($,{id:A.id,type:A.type,nodeType:A.nodeType,top:A.y,left:A.x,onClose:()=>S(null),onDelete:"node"===A.type?W:K,onDuplicate:"node"===A.type?U:void 0,onConfigure:"node"===A.type?J:void 0,onDisconnect:"edge"===A.type?K:void 0})]}),f&&(0,a.jsx)(q,{node:f,onUpdate:e=>Y(f.id,e),onClose:()=>y(null)})]}),(0,a.jsx)(X,{errors:I,onRetry:e=>{},onSkip:e=>{},onManualFix:e=>{},isVisible:M,onClose:()=>P(!1)}),d&&(0,a.jsx)(es,{workflowId:d.id,workflowName:d.name,isOpen:_,onClose:()=>L(!1)})]})})}},50515:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},52868:(e,t,r)=>{Promise.resolve().then(r.bind(r,50159))},55510:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62525:(e,t,r)=>{"use strict";r.d(t,{Dc:()=>s,p2:()=>a});let a=[{id:"general_chat",name:"General Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],s=e=>a.find(t=>t.id===e)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66368:(e,t,r)=>{"use strict";r.d(t,{MG:()=>a});let a=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},72539:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\[workflowId]\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85198:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"}))})},89114:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95753:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))})},99716:(e,t,r)=>{Promise.resolve().then(r.bind(r,72539))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,1752,1672,4912,1596],()=>r(35133));module.exports=a})();