(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4833],{9266:(e,t,o)=>{"use strict";o.d(t,{A6:()=>a.A,O4:()=>n.A,Vy:()=>r.A,li:()=>s.A,py:()=>i.A,ud:()=>l.A});var a=o(32461),n=o(82771),r=o(37186),i=o(74684),l=o(63603),s=o(63782)},17220:(e,t,o)=>{"use strict";o.d(t,{Vy:()=>n.A,fK:()=>r.A,go:()=>a.A});var a=o(89959),n=o(37186),r=o(74500)},25039:(e,t,o)=>{"use strict";o.d(t,{EF:()=>a.A,C1:()=>n.A,D3:()=>r.A,Mt:()=>l,O4:()=>s.A,Pi:()=>d.A,KS:()=>c.A,qh:()=>u.A});var a=o(5279),n=o(6865),r=o(63418),i=o(12115);let l=i.forwardRef(function(e,t){let{title:o,titleId:a,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),o?i.createElement("title",{id:a},o):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))});var s=o(82771),d=o(55628),c=o(67695),u=o(52589)},31663:(e,t,o)=>{"use strict";o.d(t,{Vy:()=>a.A,gF:()=>n.A,vk:()=>i,uc:()=>l.A});var a=o(37186),n=o(99695),r=o(12115);let i=r.forwardRef(function(e,t){let{title:o,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),o?r.createElement("title",{id:a},o):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.181 8.68a4.503 4.503 0 0 1 1.903 6.405m-9.768-2.782L3.56 14.06a4.5 4.5 0 0 0 6.364 6.365l3.129-3.129m5.614-5.615 1.757-1.757a4.5 4.5 0 0 0-6.364-6.365l-4.5 4.5c-.258.26-.479.541-.661.84m1.903 6.405a4.495 4.495 0 0 1-1.242-.88 4.483 4.483 0 0 1-1.062-1.683m6.587 2.345 5.907 5.907m-5.907-5.907L8.898 8.898M2.991 2.99 8.898 8.9"}))});var l=o(31151)},40575:(e,t,o)=>{"use strict";o.d(t,{AQ:()=>p.A,D3:()=>r.A,DQ:()=>n.A,EF:()=>a.A,K6:()=>w.A,OL:()=>l.A,Pp:()=>s.A,YE:()=>u.A,bM:()=>f.A,hp:()=>d.A,jO:()=>g.A,mS:()=>y.A,ny:()=>m.A,r$:()=>c.A,vK:()=>i.A});var a=o(5279),n=o(64274),r=o(63418),i=o(1442),l=o(64353),s=o(94830),d=o(18276),c=o(78030),u=o(58397),p=o(92975),f=o(10184),y=o(65529),w=o(64219),m=o(14170),g=o(58688)},46813:(e,t,o)=>{"use strict";o.d(t,{CT:()=>a.A,K6:()=>u.A,R2:()=>l.A,Xx:()=>n.A,Zu:()=>d.A,bM:()=>r.A,fK:()=>p.A,li:()=>s.A,mS:()=>i.A,uc:()=>c.A});var a=o(72227),n=o(45754),r=o(10184),i=o(65529),l=o(61316),s=o(63782),d=o(8246),c=o(31151),u=o(64219),p=o(74500)},72396:(e,t,o)=>{Promise.resolve().then(o.bind(o,86239))},76351:(e,t,o)=>{"use strict";o.d(t,{S:()=>a.A});var a=o(46172)},86239:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>m});var a=o(95155),n=o(12115),r=o(35695),i=o(61772);o(66419);var l=o(63768),s=o(75521),d=o(41324),c=o(82416),u=o(82087),p=o(10009),f=o(69200),y=o(27223),w=o(35020);function m(e){let{params:t}=e,o=(0,r.useParams)(),m=(0,r.useRouter)(),g=null==o?void 0:o.workflowId,[k,A]=(0,n.useState)(null),[h,v,b]=(0,i.ck)([]),[x,C,E]=(0,i.fM)([]),[S,j]=(0,n.useState)(null),[M,N]=(0,n.useState)(!0),[O,D]=(0,n.useState)(!1),[P,R]=(0,n.useState)(!1),[_,L]=(0,n.useState)(null),[T,F]=(0,p.S)("new"!==g?g:null,{autoConnect:!0,onEvent:e=>{"workflow_started"===e.type||"node_started"===e.type||"node_completed"===e.type||"workflow_completed"===e.type||e.type},onConnect:()=>{},onDisconnect:()=>{},onError:e=>{}}),[q,I]=(0,n.useState)([]),[K,W]=(0,n.useState)(!1),[V,U]=(0,n.useState)(!1);(0,n.useEffect)(()=>{"new"===g?z():B(g)},[g]);let z=async()=>{try{v([{id:"user-request",type:"userRequest",position:{x:50,y:200},data:{label:"User Request",config:{},isConfigured:!0,description:"Starting point for user input"}},{id:"classifier",type:"classifier",position:{x:350,y:200},data:{label:"Classifier",config:{},isConfigured:!0,description:"Analyzes and categorizes the request"}},{id:"output",type:"output",position:{x:950,y:200},data:{label:"Output",config:{},isConfigured:!0,description:"Final response to the user"}}]),C([{id:"e1",source:"user-request",target:"classifier",type:"smoothstep",animated:!0}]),N(!1)}catch(e){N(!1)}},B=async e=>{try{N(!1)}catch(e){N(!1)}},Y=(0,n.useCallback)(e=>{let t={...e,id:"e".concat(x.length+1),type:"smoothstep",animated:!0};C(e=>(0,i.rN)(t,e)),R(!0)},[x.length,C]),Q=(0,n.useCallback)((e,t)=>{j(t)},[]),X=(0,n.useCallback)(()=>{j(null),L(null)},[]),J=(0,n.useCallback)((e,t)=>{e.preventDefault(),L({id:t.id,type:"node",nodeType:t.type,x:e.clientX,y:e.clientY})},[]),Z=(0,n.useCallback)((e,t)=>{e.preventDefault(),L({id:t.id,type:"edge",x:e.clientX,y:e.clientY})},[]),G=(0,n.useCallback)(e=>{["user-request","classifier","output"].includes(e)||(v(t=>t.filter(t=>t.id!==e)),C(t=>t.filter(t=>t.source!==e&&t.target!==e)),R(!0),(null==S?void 0:S.id)===e&&j(null))},[S,v,C]),H=(0,n.useCallback)(e=>{C(t=>t.filter(t=>t.id!==e)),R(!0)},[C]),$=(0,n.useCallback)(e=>{let t=h.find(t=>t.id===e);if(!t)return;let o={...t,id:"".concat(t.type,"-").concat(Date.now()),position:{x:t.position.x+50,y:t.position.y+50},data:{...t.data,label:"".concat(t.data.label," Copy")}};v(e=>[...e,o]),R(!0)},[h,v]),ee=(0,n.useCallback)(e=>{let t=h.find(t=>t.id===e);t&&j(t)},[h]),et=async()=>{if(k||"new"!==g){D(!0);try{let e=await fetch("/api/workflows",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:g,name:null==k?void 0:k.name,description:null==k?void 0:k.description,nodes:h,edges:x,settings:(null==k?void 0:k.settings)||{}})});if(!e.ok){let t=await e.json();throw Error(t.details||"Failed to update workflow")}R(!1),alert("Workflow updated successfully!")}catch(e){alert("Failed to update workflow: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{D(!1)}}else{let e=prompt("Enter workflow name:");if(!e)return;let t=prompt("Enter workflow description (optional):")||"";D(!0);try{let o=await fetch("/api/workflows",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e,description:t,nodes:h,edges:x,settings:{}})});if(!o.ok){let e=await o.json();throw Error(e.details||"Failed to save workflow")}let a=await o.json();alert("Workflow saved successfully!\n\nYour API Key: ".concat(a.api_key,"\n\nSave this key - it will not be shown again!")),m.push("/manual-build/".concat(a.workflow.id))}catch(e){alert("Failed to save workflow: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{D(!1)}}},eo=(e,t)=>{v(o=>o.map(o=>o.id===e?{...o,data:{...o.data,...t}}:o)),R(!0)};return M?(0,a.jsx)("div",{className:"h-screen bg-[#040716] flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white",children:"Loading workflow..."})}):(0,a.jsx)(f.Ay,{showDetails:!1,onError:(e,t)=>{let o={id:"error-".concat(Date.now()),nodeId:"editor",nodeType:"editor",nodeLabel:"Workflow Editor",message:e.message,timestamp:new Date().toISOString(),attempt:1,maxRetries:3,status:"pending",recoveryStrategies:[{type:"retry",description:"Reload the editor",available:!0,recommended:!0}]};I(e=>[...e,o]),W(!0)},children:(0,a.jsxs)("div",{className:"h-screen bg-[#040716] flex flex-col",children:[(0,a.jsx)(l.A,{workflow:k,isDirty:P,isSaving:O,onSave:et,onExecute:()=>{(null==k?void 0:k.id)?window.open("/playground/workflows","_blank"):alert("Please save the workflow first to test it in the playground")},onBack:()=>m.push("/manual-build"),onShare:()=>U(!0)}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[(0,a.jsx)(s.A,{onAddNode:(e,t)=>{let o={},a=!0;"provider"===e?(o={providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},a=!1):"centralRouter"===e&&(o={routingStrategy:"smart",fallbackProvider:"",maxRetries:3,timeout:3e4,enableCaching:!0,debugMode:!1},a=!0);let n={id:"".concat(e,"-").concat(Date.now()),type:e,position:t,data:{label:"centralRouter"===e?"Central Router":e.charAt(0).toUpperCase()+e.slice(1),config:o,isConfigured:a,description:"".concat(e," node")}};v(e=>[...e,n]),R(!0)}}),(0,a.jsxs)("div",{className:"flex-1 relative manual-build-canvas",children:[(0,a.jsxs)(i.Gc,{nodes:h,edges:x,onNodesChange:b,onEdgesChange:E,onConnect:Y,onNodeClick:Q,onNodeContextMenu:J,onEdgeContextMenu:Z,onPaneClick:X,nodeTypes:u.c_,fitView:!0,className:"bg-[#040716]",defaultViewport:{x:0,y:0,zoom:.8},connectionLineStyle:{stroke:"#ff6b35",strokeWidth:2},defaultEdgeOptions:{style:{stroke:"#ff6b35",strokeWidth:2},type:"smoothstep",animated:!0},children:[(0,a.jsx)(i.VS,{color:"#1f2937",gap:20,size:1}),(0,a.jsx)(i.H2,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",showInteractive:!1}),(0,a.jsx)(i.of,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",nodeColor:"#ff6b35",maskColor:"rgba(0, 0, 0, 0.2)"})]}),_&&(0,a.jsx)(c.A,{id:_.id,type:_.type,nodeType:_.nodeType,top:_.y,left:_.x,onClose:()=>L(null),onDelete:"node"===_.type?G:H,onDuplicate:"node"===_.type?$:void 0,onConfigure:"node"===_.type?ee:void 0,onDisconnect:"edge"===_.type?H:void 0})]}),S&&(0,a.jsx)(d.A,{node:S,onUpdate:e=>eo(S.id,e),onClose:()=>j(null)})]}),(0,a.jsx)(y.A,{errors:q,onRetry:e=>{},onSkip:e=>{},onManualFix:e=>{},isVisible:K,onClose:()=>W(!1)}),k&&(0,a.jsx)(w.A,{workflowId:k.id,workflowName:k.name,isOpen:V,onClose:()=>U(!1)})]})})}},99415:(e,t,o)=>{"use strict";o.d(t,{EF:()=>a.A,N7:()=>r,AQ:()=>i.A,Pi:()=>l.A});var a=o(5279),n=o(12115);let r=n.forwardRef(function(e,t){let{title:o,titleId:a,...r}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),o?n.createElement("title",{id:a},o):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 12.75c1.148 0 2.278.08 3.383.237 1.037.146 1.866.966 1.866 2.013 0 3.728-2.35 6.75-5.25 6.75S6.75 18.728 6.75 15c0-1.046.83-1.867 1.866-2.013A24.204 24.204 0 0 1 12 12.75Zm0 0c2.883 0 5.647.508 8.207 1.44a23.91 23.91 0 0 1-1.152 6.06M12 12.75c-2.883 0-5.647.508-8.208 1.44.125 2.104.52 4.136 1.153 6.06M12 12.75a2.25 2.25 0 0 0 2.248-2.354M12 12.75a2.25 2.25 0 0 1-2.248-2.354M12 8.25c.995 0 1.971-.08 2.922-.236.403-.066.74-.358.795-.762a3.778 3.778 0 0 0-.399-2.25M12 8.25c-.995 0-1.97-.08-2.922-.236-.402-.066-.74-.358-.795-.762a3.734 3.734 0 0 1 .4-2.253M12 8.25a2.25 2.25 0 0 0-2.248 2.146M12 8.25a2.25 2.25 0 0 1 2.248 2.146M8.683 5a6.032 6.032 0 0 1-1.155-1.002c.07-.63.27-1.222.574-1.747m.581 2.749A3.75 3.75 0 0 1 15.318 5m0 0c.427-.283.815-.62 1.155-.999a4.471 4.471 0 0 0-.575-1.752M4.921 6a24.048 24.048 0 0 0-.392 3.314c1.668.546 3.416.914 5.223 1.082M19.08 6c.205 1.08.337 2.187.392 3.314a23.882 23.882 0 0 1-5.223 1.082"}))});var i=o(92975),l=o(55628)}},e=>{var t=t=>e(e.s=t);e.O(0,[8946,5738,9968,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(72396)),_N_E=e.O()}]);