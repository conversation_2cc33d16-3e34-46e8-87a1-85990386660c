(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9280],{12368:(e,t,s)=>{Promise.resolve().then(s.bind(s,19853))},19853:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(95155),r=s(12115),l=s(35695),i=s(37186),o=s(99695);let n=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"}))});var c=s(78046),d=s(63603),x=s(57765),m=s(55020);function u(e){let{workflow:t,onEdit:s,onDuplicate:r,onDelete:l}=e;return(0,a.jsxs)(m.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-2},className:"bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 hover:border-[#ff6b35]/30 transition-all duration-300 group",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2 group-hover:text-[#ff6b35] transition-colors",children:t.name}),(0,a.jsx)("p",{className:"text-gray-400 text-sm line-clamp-2",children:t.description||"No description provided"})]}),(0,a.jsx)("div",{className:"flex items-center gap-2 ml-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(t.is_active?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-gray-500/20 text-gray-400 border border-gray-500/30"),children:t.is_active?"Active":"Inactive"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,a.jsxs)("span",{children:[t.nodes.length," nodes"]}),(0,a.jsxs)("span",{children:["Updated ",new Date(t.updated_at).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("button",{onClick:()=>s(t.id),className:"flex-1 bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2",children:[(0,a.jsx)(i.A,{className:"w-4 h-4"}),"Edit"]}),(0,a.jsx)("button",{onClick:()=>r(t.id),className:"bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors duration-200",title:"Duplicate",children:(0,a.jsx)(o.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"bg-green-600 hover:bg-green-500 text-white px-3 py-2 rounded-lg transition-colors duration-200",title:"Test in Playground",children:(0,a.jsx)(d.A,{className:"w-4 h-4"})})]})]})}function h(e){let{template:t,onUse:s}=e;return(0,a.jsxs)(m.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-2},className:"bg-gradient-to-br from-blue-900/40 to-blue-800/20 backdrop-blur-sm border border-blue-700/50 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 group",children:[(0,a.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white group-hover:text-blue-400 transition-colors",children:t.name}),t.is_official&&(0,a.jsx)("span",{className:"bg-blue-500/20 text-blue-400 border border-blue-500/30 px-2 py-0.5 rounded-full text-xs font-medium",children:"Official"})]}),(0,a.jsx)("p",{className:"text-gray-400 text-sm line-clamp-2",children:t.description})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,a.jsx)("span",{className:"bg-gray-700/50 px-2 py-1 rounded text-xs",children:t.category}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("span",{children:["⭐ ",t.rating.toFixed(1)]}),(0,a.jsxs)("span",{children:["↓ ",t.download_count]})]})]}),(0,a.jsx)("button",{onClick:()=>s(t.id),className:"w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200",children:"Use Template"})]})}function f(){let e=(0,l.useRouter)(),[t,s]=(0,r.useState)([]),[i,d]=(0,r.useState)([]),[m,f]=(0,r.useState)(!0),[b,g]=(0,r.useState)(""),[w,p]=(0,r.useState)("workflows");(0,r.useEffect)(()=>{j(),y()},[]);let j=async()=>{try{let e=await fetch("/api/workflows");if(e.ok){let t=await e.json();s(t.workflows||[])}}catch(e){}},y=async()=>{try{d([]),f(!1)}catch(e){f(!1)}},N=()=>{e.push("/manual-build/new")},v=t=>{e.push("/manual-build/".concat(t))},k=async e=>{},C=async e=>{if(confirm("Are you sure you want to delete this workflow? This action cannot be undone."))try{let t=await fetch("/api/workflows?id=".concat(e),{method:"DELETE"});if(t.ok)j(),alert("Workflow deleted successfully");else{let e=await t.json();throw Error(e.details||"Failed to delete workflow")}}catch(e){alert("Failed to delete workflow: ".concat(e instanceof Error?e.message:"Unknown error"))}},A=t=>{e.push("/manual-build/new?template=".concat(t))},L=t.filter(e=>{var t;return e.name.toLowerCase().includes(b.toLowerCase())||(null==(t=e.description)?void 0:t.toLowerCase().includes(b.toLowerCase()))}),E=i.filter(e=>e.name.toLowerCase().includes(b.toLowerCase())||e.description.toLowerCase().includes(b.toLowerCase())||e.category.toLowerCase().includes(b.toLowerCase()));return m?(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white",children:"Loading..."})}):(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Manual Build"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Create custom AI workflows with visual node-based editor"})]}),(0,a.jsxs)("button",{onClick:N,className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-5 h-5"}),"Create New Workflow"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search workflows and templates...",value:b,onChange:e=>g(e.target.value),className:"bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-[#ff6b35]/50 transition-colors w-80"})]})}),(0,a.jsxs)("div",{className:"flex bg-gray-800/50 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>p("workflows"),className:"px-4 py-2 rounded-md font-medium transition-colors ".concat("workflows"===w?"bg-[#ff6b35] text-white":"text-gray-400 hover:text-white"),children:[(0,a.jsx)(n,{className:"w-4 h-4 inline mr-2"}),"My Workflows"]}),(0,a.jsxs)("button",{onClick:()=>p("templates"),className:"px-4 py-2 rounded-md font-medium transition-colors ".concat("templates"===w?"bg-[#ff6b35] text-white":"text-gray-400 hover:text-white"),children:[(0,a.jsx)(o.A,{className:"w-4 h-4 inline mr-2"}),"Templates"]})]})]}),"workflows"===w?(0,a.jsx)("div",{children:0===L.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(n,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No workflows yet"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"Create your first visual workflow to get started"}),(0,a.jsxs)("button",{onClick:N,className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 inline-flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-5 h-5"}),"Create New Workflow"]})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:L.map(e=>(0,a.jsx)(u,{workflow:e,onEdit:v,onDuplicate:k,onDelete:C},e.id))})}):(0,a.jsx)("div",{children:0===E.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No templates found"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search or check back later"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:E.map(e=>(0,a.jsx)(h,{template:e,onUse:A},e.id))})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9299,9420,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(12368)),_N_E=e.O()}]);