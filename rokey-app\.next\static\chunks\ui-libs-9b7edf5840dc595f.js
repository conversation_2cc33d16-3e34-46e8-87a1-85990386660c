"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7874],{55424:(e,t,l)=>{l.d(t,{m_:()=>k});var o=l(12115),r=l(22475),n=l(29300),s=l(87358);let c={core:!1,base:!1};function i({css:e,id:t="react-tooltip-base-styles",type:l="base",ref:o}){var r,n;if(!e||"undefined"==typeof document||c[l]||"core"===l&&void 0!==s&&(null==(r=null==s?void 0:s.env)?void 0:r.REACT_TOOLTIP_DISABLE_CORE_STYLES)||"base"!==l&&void 0!==s&&(null==(n=null==s?void 0:s.env)?void 0:n.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;"core"===l&&(t="react-tooltip-core-styles"),o||(o={});let{insertAt:i}=o;if(document.getElementById(t))return;let a=document.head||document.getElementsByTagName("head")[0],u=document.createElement("style");u.id=t,u.type="text/css","top"===i&&a.firstChild?a.insertBefore(u,a.firstChild):a.appendChild(u),u.styleSheet?u.styleSheet.cssText=e:u.appendChild(document.createTextNode(e)),c[l]=!0}let a=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:l=null,place:o="top",offset:n=10,strategy:s="absolute",middlewares:c=[(0,r.cY)(Number(n)),(0,r.UU)({fallbackAxisSideDirection:"start"}),(0,r.BN)({padding:5})],border:i,arrowSize:a=8})=>e&&null!==t?l?(c.push((0,r.UE)({element:l,padding:5})),(0,r.rD)(e,t,{placement:o,strategy:s,middleware:c}).then(({x:e,y:t,placement:l,middlewareData:o})=>{var r,n;let s={left:`${e}px`,top:`${t}px`,border:i},{x:c,y:u}=null!=(r=o.arrow)?r:{x:0,y:0},d=null!=(n=({top:"bottom",right:"left",bottom:"top",left:"right"})[l.split("-")[0]])?n:"bottom",p=0;if(i){let e=`${i}`.match(/(\d+)px/);p=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:s,tooltipArrowStyles:{left:null!=c?`${c}px`:"",top:null!=u?`${u}px`:"",right:"",bottom:"",...i&&{borderBottom:i,borderRight:i},[d]:`-${a/2+p}px`},place:l}})):(0,r.rD)(e,t,{placement:"bottom",strategy:s,middleware:c}).then(({x:e,y:t,placement:l})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:l})):{tooltipStyles:{},tooltipArrowStyles:{},place:o},u=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),d=(e,t,l)=>{let o=null,r=function(...r){let n=()=>{o=null,l||e.apply(this,r)};l&&!o&&(e.apply(this,r),o=setTimeout(n,t)),l||(o&&clearTimeout(o),o=setTimeout(n,t))};return r.cancel=()=>{o&&(clearTimeout(o),o=null)},r},p=e=>null!==e&&!Array.isArray(e)&&"object"==typeof e,f=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,l)=>f(e,t[l]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!p(e)||!p(t))return e===t;let l=Object.keys(e),o=Object.keys(t);return l.length===o.length&&l.every(l=>f(e[l],t[l]))},m=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;let t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(e=>{let l=t.getPropertyValue(e);return"auto"===l||"scroll"===l})},v=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(m(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},y="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,w=e=>{e.current&&(clearTimeout(e.current),e.current=null)},h={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},_=(0,o.createContext)({getTooltipData:()=>h});function b(e="DEFAULT_TOOLTIP_ID"){return(0,o.useContext)(_).getTooltipData(e)}var E={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},S={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};let g=({forwardRef:e,id:t,className:l,classNameArrow:s,variant:c="dark",anchorId:i,anchorSelect:u,place:p="top",offset:m=10,events:h=["hover"],openOnClick:_=!1,positionStrategy:g="absolute",middlewares:A,wrapper:k,delayShow:O=0,delayHide:T=0,float:R=!1,hidden:x=!1,noArrow:L=!1,clickable:C=!1,closeOnEsc:N=!1,closeOnScroll:z=!1,closeOnResize:$=!1,openEvents:I,closeEvents:j,globalCloseEvents:B,imperativeModeOnly:D,style:q,position:H,afterShow:M,afterHide:K,disableTooltip:W,content:U,contentWrapperRef:P,isOpen:V,defaultIsOpen:X=!1,setIsOpen:F,activeAnchor:Y,setActiveAnchor:Z,border:G,opacity:J,arrowColor:Q,arrowSize:ee=8,role:et="tooltip"})=>{var el;let eo=(0,o.useRef)(null),er=(0,o.useRef)(null),en=(0,o.useRef)(null),es=(0,o.useRef)(null),ec=(0,o.useRef)(null),[ei,ea]=(0,o.useState)({tooltipStyles:{},tooltipArrowStyles:{},place:p}),[eu,ed]=(0,o.useState)(!1),[ep,ef]=(0,o.useState)(!1),[em,ev]=(0,o.useState)(null),ey=(0,o.useRef)(!1),ew=(0,o.useRef)(null),{anchorRefs:eh,setActiveAnchor:e_}=b(t),eb=(0,o.useRef)(!1),[eE,eS]=(0,o.useState)([]),eg=(0,o.useRef)(!1),eA=_||h.includes("click"),ek=eA||(null==I?void 0:I.click)||(null==I?void 0:I.dblclick)||(null==I?void 0:I.mousedown),eO=I?{...I}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!I&&eA&&Object.assign(eO,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});let eT=j?{...j}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!j&&eA&&Object.assign(eT,{mouseleave:!1,blur:!1,mouseout:!1});let eR=B?{...B}:{escape:N||!1,scroll:z||!1,resize:$||!1,clickOutsideAnchor:ek||!1};D&&(Object.assign(eO,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(eT,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(eR,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),y(()=>(eg.current=!0,()=>{eg.current=!1}),[]);let ex=e=>{eg.current&&(e&&ef(!0),setTimeout(()=>{eg.current&&(null==F||F(e),void 0===V&&ed(e))},10))};(0,o.useEffect)(()=>{if(void 0===V)return()=>null;V&&ef(!0);let e=setTimeout(()=>{ed(V)},10);return()=>{clearTimeout(e)}},[V]),(0,o.useEffect)(()=>{eu!==ey.current&&((w(ec),ey.current=eu,eu)?null==M||M():ec.current=setTimeout(()=>{ef(!1),ev(null),null==K||K()},(e=>{let t=e.match(/^([\d.]+)(ms|s)$/);if(!t)return 0;let[,l,o]=t;return Number(l)*("ms"===o?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"))+25))},[eu]);let eL=e=>{ea(t=>f(t,e)?t:e)},eC=(e=O)=>{w(en),ep?ex(!0):en.current=setTimeout(()=>{ex(!0)},e)},eN=(e=T)=>{w(es),es.current=setTimeout(()=>{eb.current||ex(!1)},e)},ez=e=>{var t;if(!e)return;let l=null!=(t=e.currentTarget)?t:e.target;if(!(null==l?void 0:l.isConnected))return Z(null),void e_({current:null});O?eC():ex(!0),Z(l),e_({current:l}),w(es)},e$=()=>{C?eN(T||100):T?eN():ex(!1),w(en)},eI=({x:e,y:t})=>{var l;a({place:null!=(l=null==em?void 0:em.place)?l:p,offset:m,elementReference:{getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})},tooltipReference:eo.current,tooltipArrowReference:er.current,strategy:g,middlewares:A,border:G,arrowSize:ee}).then(e=>{eL(e)})},ej=e=>{if(!e)return;let t={x:e.clientX,y:e.clientY};eI(t),ew.current=t},eB=e=>{var t;if(!eu)return;let l=e.target;l.isConnected&&(null==(t=eo.current)||!t.contains(l))&&([document.querySelector(`[id='${i}']`),...eE].some(e=>null==e?void 0:e.contains(l))||(ex(!1),w(en)))},eD=d(ez,50,!0),eq=d(e$,50,!0),eH=e=>{eq.cancel(),eD(e)},eM=()=>{eD.cancel(),eq()},eK=(0,o.useCallback)(()=>{var e,t;let l=null!=(e=null==em?void 0:em.position)?e:H;l?eI(l):R?ew.current&&eI(ew.current):(null==Y?void 0:Y.isConnected)&&a({place:null!=(t=null==em?void 0:em.place)?t:p,offset:m,elementReference:Y,tooltipReference:eo.current,tooltipArrowReference:er.current,strategy:g,middlewares:A,border:G,arrowSize:ee}).then(e=>{eg.current&&eL(e)})},[eu,Y,U,q,p,null==em?void 0:em.place,m,g,H,null==em?void 0:em.position,R,ee]);(0,o.useEffect)(()=>{var e,t;let l=new Set(eh);eE.forEach(e=>{(null==W?void 0:W(e))||l.add({current:e})});let o=document.querySelector(`[id='${i}']`);!o||(null==W?void 0:W(o))||l.add({current:o});let n=()=>{ex(!1)},s=v(Y),c=v(eo.current);eR.scroll&&(window.addEventListener("scroll",n),null==s||s.addEventListener("scroll",n),null==c||c.addEventListener("scroll",n));let a=null;eR.resize?window.addEventListener("resize",n):Y&&eo.current&&(a=(0,r.ll)(Y,eo.current,eK,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));let u=e=>{"Escape"===e.key&&ex(!1)};eR.escape&&window.addEventListener("keydown",u),eR.clickOutsideAnchor&&window.addEventListener("click",eB);let d=[],p=e=>!!((null==e?void 0:e.target)&&(null==Y?void 0:Y.contains(e.target))),f=e=>{eu&&p(e)||ez(e)},m=e=>{eu&&p(e)&&e$()},y=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],w=["click","dblclick","mousedown","mouseup"];Object.entries(eO).forEach(([e,t])=>{t&&(y.includes(e)?d.push({event:e,listener:eH}):w.includes(e)&&d.push({event:e,listener:f}))}),Object.entries(eT).forEach(([e,t])=>{t&&(y.includes(e)?d.push({event:e,listener:eM}):w.includes(e)&&d.push({event:e,listener:m}))}),R&&d.push({event:"pointermove",listener:ej});let h=()=>{eb.current=!0},_=()=>{eb.current=!1,e$()},b=C&&(eT.mouseout||eT.mouseleave);return b&&(null==(e=eo.current)||e.addEventListener("mouseover",h),null==(t=eo.current)||t.addEventListener("mouseout",_)),d.forEach(({event:e,listener:t})=>{l.forEach(l=>{var o;null==(o=l.current)||o.addEventListener(e,t)})}),()=>{var e,t;eR.scroll&&(window.removeEventListener("scroll",n),null==s||s.removeEventListener("scroll",n),null==c||c.removeEventListener("scroll",n)),eR.resize?window.removeEventListener("resize",n):null==a||a(),eR.clickOutsideAnchor&&window.removeEventListener("click",eB),eR.escape&&window.removeEventListener("keydown",u),b&&(null==(e=eo.current)||e.removeEventListener("mouseover",h),null==(t=eo.current)||t.removeEventListener("mouseout",_)),d.forEach(({event:e,listener:t})=>{l.forEach(l=>{var o;null==(o=l.current)||o.removeEventListener(e,t)})})}},[Y,eK,ep,eh,eE,I,j,B,eA,O,T]),(0,o.useEffect)(()=>{var e,l;let o=null!=(l=null!=(e=null==em?void 0:em.anchorSelect)?e:u)?l:"";!o&&t&&(o=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);let r=new MutationObserver(e=>{let l=[],r=[];e.forEach(e=>{if("attributes"===e.type&&"data-tooltip-id"===e.attributeName&&(e.target.getAttribute("data-tooltip-id")===t?l.push(e.target):e.oldValue===t&&r.push(e.target)),"childList"===e.type){if(Y){let t=[...e.removedNodes].filter(e=>1===e.nodeType);if(o)try{r.push(...t.filter(e=>e.matches(o))),r.push(...t.flatMap(e=>[...e.querySelectorAll(o)]))}catch(e){}t.some(e=>{var t;return!!(null==(t=null==e?void 0:e.contains)?void 0:t.call(e,Y))&&(ef(!1),ex(!1),Z(null),w(en),w(es),!0)})}if(o)try{let t=[...e.addedNodes].filter(e=>1===e.nodeType);l.push(...t.filter(e=>e.matches(o))),l.push(...t.flatMap(e=>[...e.querySelectorAll(o)]))}catch(e){}}}),(l.length||r.length)&&eS(e=>[...e.filter(e=>!r.includes(e)),...l])});return r.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{r.disconnect()}},[t,u,null==em?void 0:em.anchorSelect,Y]),(0,o.useEffect)(()=>{eK()},[eK]),(0,o.useEffect)(()=>{if(!(null==P?void 0:P.current))return()=>null;let e=new ResizeObserver(()=>{setTimeout(()=>eK())});return e.observe(P.current),()=>{e.disconnect()}},[U,null==P?void 0:P.current]),(0,o.useEffect)(()=>{var e;let t=document.querySelector(`[id='${i}']`),l=[...eE,t];Y&&l.includes(Y)||Z(null!=(e=eE[0])?e:t)},[i,eE,Y]),(0,o.useEffect)(()=>(X&&ex(!0),()=>{w(en),w(es)}),[]),(0,o.useEffect)(()=>{var e;let l=null!=(e=null==em?void 0:em.anchorSelect)?e:u;if(!l&&t&&(l=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),l)try{let e=Array.from(document.querySelectorAll(l));eS(e)}catch(e){eS([])}},[t,u,null==em?void 0:em.anchorSelect]),(0,o.useEffect)(()=>{en.current&&(w(en),eC(O))},[O]);let eW=null!=(el=null==em?void 0:em.content)?el:U,eU=eu&&Object.keys(ei.tooltipStyles).length>0;return(0,o.useImperativeHandle)(e,()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}ev(null!=e?e:null),(null==e?void 0:e.delay)?eC(e.delay):ex(!0)},close:e=>{(null==e?void 0:e.delay)?eN(e.delay):ex(!1)},activeAnchor:Y,place:ei.place,isOpen:!!(ep&&!x&&eW&&eU)})),ep&&!x&&eW?o.createElement(k,{id:t,role:et,className:n("react-tooltip",E.tooltip,S.tooltip,S[c],l,`react-tooltip__place-${ei.place}`,E[eU?"show":"closing"],eU?"react-tooltip__show":"react-tooltip__closing","fixed"===g&&E.fixed,C&&E.clickable),onTransitionEnd:e=>{w(ec),eu||"opacity"!==e.propertyName||(ef(!1),ev(null),null==K||K())},style:{...q,...ei.tooltipStyles,opacity:void 0!==J&&eU?J:void 0},ref:eo},eW,o.createElement(k,{className:n("react-tooltip-arrow",E.arrow,S.arrow,s,L&&E.noArrow),style:{...ei.tooltipArrowStyles,background:Q?`linear-gradient(to right bottom, transparent 50%, ${Q} 50%)`:void 0,"--rt-arrow-size":`${ee}px`},ref:er})):null},A=({content:e})=>o.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),k=o.forwardRef(({id:e,anchorId:t,anchorSelect:l,content:r,html:s,render:c,className:i,classNameArrow:a,variant:d="dark",place:p="top",offset:f=10,wrapper:m="div",children:v=null,events:y=["hover"],openOnClick:w=!1,positionStrategy:h="absolute",middlewares:_,delayShow:E=0,delayHide:S=0,float:k=!1,hidden:O=!1,noArrow:T=!1,clickable:R=!1,closeOnEsc:x=!1,closeOnScroll:L=!1,closeOnResize:C=!1,openEvents:N,closeEvents:z,globalCloseEvents:$,imperativeModeOnly:I=!1,style:j,position:B,isOpen:D,defaultIsOpen:q=!1,disableStyleInjection:H=!1,border:M,opacity:K,arrowColor:W,arrowSize:U,setIsOpen:P,afterShow:V,afterHide:X,disableTooltip:F,role:Y="tooltip"},Z)=>{let[G,J]=(0,o.useState)(r),[Q,ee]=(0,o.useState)(s),[et,el]=(0,o.useState)(p),[eo,er]=(0,o.useState)(d),[en,es]=(0,o.useState)(f),[ec,ei]=(0,o.useState)(E),[ea,eu]=(0,o.useState)(S),[ed,ep]=(0,o.useState)(k),[ef,em]=(0,o.useState)(O),[ev,ey]=(0,o.useState)(m),[ew,eh]=(0,o.useState)(y),[e_,eb]=(0,o.useState)(h),[eE,eS]=(0,o.useState)(null),[eg,eA]=(0,o.useState)(null),ek=(0,o.useRef)(H),{anchorRefs:eO,activeAnchor:eT}=b(e),eR=e=>null==e?void 0:e.getAttributeNames().reduce((t,l)=>{var o;return l.startsWith("data-tooltip-")&&(t[l.replace(/^data-tooltip-/,"")]=null!=(o=null==e?void 0:e.getAttribute(l))?o:null),t},{}),ex=e=>{let t={place:e=>{el(null!=e?e:p)},content:e=>{J(null!=e?e:r)},html:e=>{ee(null!=e?e:s)},variant:e=>{er(null!=e?e:d)},offset:e=>{es(null===e?f:Number(e))},wrapper:e=>{ey(null!=e?e:m)},events:e=>{let t=null==e?void 0:e.split(" ");eh(null!=t?t:y)},"position-strategy":e=>{eb(null!=e?e:h)},"delay-show":e=>{ei(null===e?E:Number(e))},"delay-hide":e=>{eu(null===e?S:Number(e))},float:e=>{ep(null===e?k:"true"===e)},hidden:e=>{em(null===e?O:"true"===e)},"class-name":e=>{eS(e)}};Object.values(t).forEach(e=>e(null)),Object.entries(e).forEach(([e,l])=>{var o;null==(o=t[e])||o.call(t,l)})};(0,o.useEffect)(()=>{J(r)},[r]),(0,o.useEffect)(()=>{ee(s)},[s]),(0,o.useEffect)(()=>{el(p)},[p]),(0,o.useEffect)(()=>{er(d)},[d]),(0,o.useEffect)(()=>{es(f)},[f]),(0,o.useEffect)(()=>{ei(E)},[E]),(0,o.useEffect)(()=>{eu(S)},[S]),(0,o.useEffect)(()=>{ep(k)},[k]),(0,o.useEffect)(()=>{em(O)},[O]),(0,o.useEffect)(()=>{eb(h)},[h]),(0,o.useEffect)(()=>{ek.current!==H&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[H]),(0,o.useEffect)(()=>{"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:"core"===H,disableBase:H}}))},[]),(0,o.useEffect)(()=>{var o;let r=new Set(eO),n=l;if(!n&&e&&(n=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),n)try{document.querySelectorAll(n).forEach(e=>{r.add({current:e})})}catch(e){console.warn(`[react-tooltip] "${n}" is not a valid CSS selector`)}let s=document.querySelector(`[id='${t}']`);if(s&&r.add({current:s}),!r.size)return()=>null;let c=null!=(o=null!=eg?eg:s)?o:eT.current,i=new MutationObserver(e=>{e.forEach(e=>{var t;c&&"attributes"===e.type&&(null==(t=e.attributeName)?void 0:t.startsWith("data-tooltip-"))&&ex(eR(c))})});return c&&(ex(eR(c)),i.observe(c,{attributes:!0,childList:!1,subtree:!1})),()=>{i.disconnect()}},[eO,eT,eg,t,l]),(0,o.useEffect)(()=>{(null==j?void 0:j.border)&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),M&&!u("border",`${M}`)&&console.warn(`[react-tooltip] "${M}" is not a valid \`border\`.`),(null==j?void 0:j.opacity)&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),K&&!u("opacity",`${K}`)&&console.warn(`[react-tooltip] "${K}" is not a valid \`opacity\`.`)},[]);let eL=v,eC=(0,o.useRef)(null);if(c){let e=c({content:(null==eg?void 0:eg.getAttribute("data-tooltip-content"))||G||null,activeAnchor:eg});eL=e?o.createElement("div",{ref:eC,className:"react-tooltip-content-wrapper"},e):null}else G&&(eL=G);Q&&(eL=o.createElement(A,{content:Q}));let eN={forwardRef:Z,id:e,anchorId:t,anchorSelect:l,className:n(i,eE),classNameArrow:a,content:eL,contentWrapperRef:eC,place:et,variant:eo,offset:en,wrapper:ev,events:ew,openOnClick:w,positionStrategy:e_,middlewares:_,delayShow:ec,delayHide:ea,float:ed,hidden:ef,noArrow:T,clickable:R,closeOnEsc:x,closeOnScroll:L,closeOnResize:C,openEvents:N,closeEvents:z,globalCloseEvents:$,imperativeModeOnly:I,style:j,position:B,isOpen:D,defaultIsOpen:q,border:M,opacity:K,arrowColor:W,arrowSize:U,setIsOpen:P,afterShow:V,afterHide:X,disableTooltip:F,activeAnchor:eg,setActiveAnchor:e=>eA(e),role:Y};return o.createElement(g,{...eN})});"undefined"!=typeof window&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||i({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s;--rt-arrow-size:8px}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit;z-index:-1}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||i({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:var(--rt-arrow-size);height:var(--rt-arrow-size)}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})})}}]);