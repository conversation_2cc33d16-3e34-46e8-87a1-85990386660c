"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7069],{41324:(e,t,a)=>{a.d(t,{A:()=>d});var s=a(95155),l=a(12115),r=a(17220),n=a(75922),o=a(47225);let i=n.MG.map(e=>({value:e.id,label:e.name}));function d(e){let{node:t,onUpdate:a,onClose:d}=e,[c,u]=(0,l.useState)(t.data.config),[m,x]=(0,l.useState)(null),[p,g]=(0,l.useState)(!1),[b,h]=(0,l.useState)(null),[f,y]=(0,l.useState)([]),[v,j]=(0,l.useState)(!1),[N,w]=(0,l.useState)(null),k=(0,l.useCallback)(async()=>{g(!0),h(null),x(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?x(t.models):x([])}catch(e){h(e.message),x([])}finally{g(!1)}},[]),C=(0,l.useCallback)(async()=>{j(!0),w(null);try{let e=await fetch("/api/user/custom-roles");if(!e.ok)throw Error("Failed to fetch custom roles");let t=await e.json();y(t)}catch(e){w(e.message),y([])}finally{j(!1)}},[]);(0,l.useEffect)(()=>{("provider"===t.type||"vision"===t.type||"planner"===t.type)&&k(),"roleAgent"===t.type&&C()},[t.type,k,C]),(0,l.useEffect)(()=>{if(("provider"===t.type||"vision"===t.type||"planner"===t.type)&&m&&m.length>0){let e=n.MG.find(e=>e.id===c.providerId);if(e&&c.providerId&&!c.modelId){let s=[];if("openrouter"===e.id?s=m.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||"")):"deepseek"===e.id?(m.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&s.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),m.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&s.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"})):s=m.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||"")),s.length>0){let e=s[0].value,l=m.find(t=>t.id===e),r=(null==l?void 0:l.output_token_limit)||(null==l?void 0:l.context_window)||4096,n=Math.min(r,Math.max(1024,Math.floor(.75*r))),o=c.parameters||{},i={...c,modelId:e,parameters:{...o,maxTokens:o.maxTokens||n}};u(i),a({config:i,isConfigured:D(t.type,i)})}}}},[m,t.type,null==c?void 0:c.providerId]);let T=(e,s)=>{let l={...c,[e]:s};u(l),a({config:l,isConfigured:D(t.type,l)})},I=(e,s)=>{let l={...c,[e]:s};"parameters"!==e&&c.parameters||(l.parameters={maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0,temperature:1,...c.parameters,..."parameters"===e?s:{}}),u(l),a({config:l,isConfigured:D(t.type,l)})},A=(0,l.useMemo)(()=>{if(m&&("provider"===t.type||"vision"===t.type||"planner"===t.type)){let s=n.MG.find(e=>e.id===c.providerId);if(!s)return[];let l=e=>"vision"===t.type?e.filter(e=>e.modality&&(e.modality.includes("multimodal")||e.modality.includes("vision")||e.modality.includes("image"))):e;if("openrouter"===s.id)return l(m).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===s.id){var e,a;let s=[],l=m.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id);l&&("provider"===t.type||"planner"===t.type||"vision"===t.type&&(null==(e=l.modality)?void 0:e.includes("multimodal")))&&s.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"});let r=m.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id);return r&&("provider"===t.type||"planner"===t.type||"vision"===t.type&&(null==(a=r.modality)?void 0:a.includes("multimodal")))&&s.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),s.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return l(m.filter(e=>e.provider_id===s.id)).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[m,c,t.type]),S=(0,l.useMemo)(()=>{if(!m||"provider"!==t.type&&"vision"!==t.type&&"planner"!==t.type||!(null==c?void 0:c.modelId))return{maxTokens:4096,minTokens:1};let e=m.find(e=>e.id===c.modelId);return e?{maxTokens:e.output_token_limit||e.context_window||4096,minTokens:1}:{maxTokens:4096,minTokens:1}},[m,c,t.type]),D=(e,t)=>{switch(e){case"provider":case"vision":case"planner":return!!(t.providerId&&t.modelId&&t.apiKey);case"roleAgent":if("new"===t.roleType)return!!(t.newRoleName&&t.customPrompt);return!!(t.roleId&&t.roleName);case"centralRouter":return!!t.routingStrategy;case"conditional":return!!(t.condition&&t.conditionType);case"tool":return!!t.toolType;case"browsing":default:return!0;case"memory":return!!t.memoryName;case"switch":var a;return!!(t.switchType&&(null==(a=t.cases)?void 0:a.length)>0);case"loop":return!!t.loopType}},M=()=>{var e,l,n,o;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,s.jsxs)("select",{value:(null==c?void 0:c.providerId)||"",onChange:e=>{let s={...c,providerId:e.target.value,modelId:"",parameters:c.parameters||{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,s.jsx)("option",{value:"",children:"Select Provider"}),i.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,s.jsx)("input",{type:"password",value:(null==c?void 0:c.apiKey)||"",onChange:e=>I("apiKey",e.target.value),placeholder:"Enter your API key for this provider",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"}),p&&null===m&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,s.jsx)(r.go,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),b&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",b]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,s.jsx)("select",{value:(null==c?void 0:c.modelId)||"",onChange:e=>{let s=e.target.value,l={...c,modelId:s};if(s&&m){let e=m.find(e=>e.id===s);if(e){let t=e.output_token_limit||e.context_window||4096,a=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=(null==c?void 0:c.parameters)||{};l={...l,parameters:{...s,maxTokens:a}}}}u(l),a({config:l,isConfigured:D(t.type,l)})},disabled:!(null==c?void 0:c.providerId)||!A.length,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30",children:(null==c?void 0:c.providerId)?A.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("option",{value:"",children:"Select Model"}),A.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,s.jsx)("option",{value:"",disabled:!0,children:p?"Loading models...":"No models available"}):(0,s.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,s.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:(null==c||null==(e=c.parameters)?void 0:e.temperature)||1,onChange:e=>{let t=parseFloat(e.target.value);I("parameters",{...(null==c?void 0:c.parameters)||{},temperature:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:(null==c||null==(l=c.parameters)?void 0:l.temperature)||1,onChange:e=>{let t=Math.min(2,Math.max(0,parseFloat(e.target.value)||1));I("parameters",{...(null==c?void 0:c.parameters)||{},temperature:t})},className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"maxTokens",className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens",(0,s.jsxs)("span",{className:"text-xs text-gray-400 ml-1",children:["(",S.minTokens," - ",S.maxTokens.toLocaleString(),")"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"maxTokens",min:S.minTokens,max:S.maxTokens,step:"1",value:(null==c||null==(n=c.parameters)?void 0:n.maxTokens)||S.maxTokens,onChange:e=>{let t=parseInt(e.target.value);I("parameters",{...(null==c?void 0:c.parameters)||{},maxTokens:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Minimal"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"number",min:S.minTokens,max:S.maxTokens,step:"1",value:(null==c||null==(o=c.parameters)?void 0:o.maxTokens)||S.maxTokens,onChange:e=>{let t=Math.min(S.maxTokens,Math.max(S.minTokens,parseInt(e.target.value)||S.maxTokens));I("parameters",{...(null==c?void 0:c.parameters)||{},maxTokens:t})},className:"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"}),(0,s.jsx)("button",{type:"button",onClick:()=>{I("parameters",{...(null==c?void 0:c.parameters)||{},maxTokens:S.maxTokens})},className:"text-xs text-orange-400 hover:text-orange-300 underline",children:"Max"})]}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Maximum"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more."})]})]}),(null==c?void 0:c.providerId)==="openrouter"&&(0,s.jsxs)("div",{className:"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-blue-300 font-medium mb-1",children:"\uD83C\uDF10 OpenRouter"}),(0,s.jsx)("div",{className:"text-xs text-blue-200",children:"Access to 300+ models from multiple providers with a single API key."})]})]})},P=()=>{var e,l,n,o;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,s.jsxs)("select",{value:(null==c?void 0:c.providerId)||"",onChange:e=>{let s={...c,providerId:e.target.value,modelId:"",parameters:c.parameters||{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,s.jsx)("option",{value:"",children:"Select Provider"}),i.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,s.jsx)("input",{type:"password",value:(null==c?void 0:c.apiKey)||"",onChange:e=>I("apiKey",e.target.value),placeholder:"Enter your API key for this provider",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"}),p&&null===m&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,s.jsx)(r.go,{className:"w-4 h-4 mr-2"}),"Fetching models..."]}),b&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",b]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Vision Model",(0,s.jsx)("span",{className:"text-xs text-purple-400 ml-1",children:"(Multimodal Only)"})]}),(0,s.jsx)("select",{value:(null==c?void 0:c.modelId)||"",onChange:e=>{let s=e.target.value,l={...c,modelId:s};if(s&&m){let e=m.find(e=>e.id===s);if(e){let t=e.output_token_limit||e.context_window||4096,a=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=(null==c?void 0:c.parameters)||{};l={...l,parameters:{...s,maxTokens:a}}}}u(l),a({config:l,isConfigured:D(t.type,l)})},disabled:!(null==c?void 0:c.providerId)||!A.length,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30",children:(null==c?void 0:c.providerId)?A.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("option",{value:"",children:"Select Vision Model"}),A.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,s.jsx)("option",{value:"",disabled:!0,children:p?"Loading models...":"No vision models available"}):(0,s.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})}),0===A.length&&(null==c?void 0:c.providerId)&&!p&&(0,s.jsx)("p",{className:"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg",children:"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:"Temperature (0.0 - 2.0)"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:(null==c||null==(e=c.parameters)?void 0:e.temperature)||1,onChange:e=>{let t=parseFloat(e.target.value);I("parameters",{...(null==c?void 0:c.parameters)||{},temperature:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:(null==c||null==(l=c.parameters)?void 0:l.temperature)||1,onChange:e=>{let t=Math.min(2,Math.max(0,parseFloat(e.target.value)||1));I("parameters",{...(null==c?void 0:c.parameters)||{},temperature:t})},className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"maxTokens",className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens",(0,s.jsxs)("span",{className:"text-xs text-gray-400 ml-1",children:["(",S.minTokens," - ",S.maxTokens.toLocaleString(),")"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"maxTokens",min:S.minTokens,max:S.maxTokens,step:"1",value:(null==c||null==(n=c.parameters)?void 0:n.maxTokens)||S.maxTokens,onChange:e=>{let t=parseInt(e.target.value);I("parameters",{...(null==c?void 0:c.parameters)||{},maxTokens:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Minimal"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"number",min:S.minTokens,max:S.maxTokens,step:"1",value:(null==c||null==(o=c.parameters)?void 0:o.maxTokens)||S.maxTokens,onChange:e=>{let t=Math.min(S.maxTokens,Math.max(S.minTokens,parseInt(e.target.value)||S.maxTokens));I("parameters",{...(null==c?void 0:c.parameters)||{},maxTokens:t})},className:"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"}),(0,s.jsx)("button",{type:"button",onClick:()=>{I("parameters",{...(null==c?void 0:c.parameters)||{},maxTokens:S.maxTokens})},className:"text-xs text-orange-400 hover:text-orange-300 underline",children:"Max"})]}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Maximum"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Controls the maximum number of tokens the model can generate for vision analysis."})]})]}),(null==c?void 0:c.providerId)==="openrouter"&&(0,s.jsxs)("div",{className:"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-purple-300 font-medium mb-1",children:"\uD83D\uDC41️ Vision Models"}),(0,s.jsx)("div",{className:"text-xs text-purple-200",children:"Access to multimodal models from multiple providers for image analysis and vision tasks."})]})]})},_=()=>{let e=[...o.p2.map(e=>({id:e.id,name:e.name,description:e.description,type:"predefined"})),...f.map(e=>({id:e.role_id,name:e.name,description:e.description,type:"custom"}))],l=s=>{if("create_new"===s){let e={...c,roleType:"new",roleId:"",roleName:"",newRoleName:"",newRoleDescription:"",customPrompt:""};u(e),a({config:e,isConfigured:D(t.type,e)})}else{let l=e.find(e=>e.id===s);if(l){let e={...c,roleType:l.type,roleId:l.id,roleName:l.name,customPrompt:l.description||""};u(e),a({config:e,isConfigured:D(t.type,e)})}}},r=(e,s)=>{let l={...c,[e]:s};u(l),a({config:l,isConfigured:D(t.type,l)})};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Role"}),v?(0,s.jsx)("div",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400",children:"Loading roles..."}):(0,s.jsxs)("select",{value:(null==c?void 0:c.roleType)==="new"?"create_new":(null==c?void 0:c.roleId)||"",onChange:e=>l(e.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,s.jsx)("option",{value:"",children:"Select a role..."}),(0,s.jsx)("optgroup",{label:"System Roles",children:o.p2.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))}),f.length>0&&(0,s.jsx)("optgroup",{label:"Your Custom Roles",children:f.map(e=>(0,s.jsx)("option",{value:e.role_id,children:e.name},e.role_id))}),(0,s.jsx)("optgroup",{label:"Create New",children:(0,s.jsx)("option",{value:"create_new",children:"+ Create New Role"})})]}),N&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error loading roles: ",N]})]}),(null==c?void 0:c.roleType)!=="new"&&(null==c?void 0:c.roleId)&&(0,s.jsxs)("div",{className:"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-white mb-1",children:c.roleName}),c.customPrompt&&(0,s.jsx)("div",{className:"text-xs text-gray-300",children:c.customPrompt})]}),(null==c?void 0:c.roleType)==="new"&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Role Name"}),(0,s.jsx)("input",{type:"text",value:c.newRoleName||"",onChange:e=>r("newRoleName",e.target.value),placeholder:"e.g., Data Analyst, Creative Writer",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Role Description"}),(0,s.jsx)("input",{type:"text",value:c.newRoleDescription||"",onChange:e=>r("newRoleDescription",e.target.value),placeholder:"Brief description of this role's purpose",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),(0,s.jsx)("textarea",{value:c.customPrompt||"",onChange:e=>r("customPrompt",e.target.value),placeholder:"Enter detailed instructions for this role...",rows:4,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:(null==c?void 0:c.memoryEnabled)||!1,onChange:e=>T("memoryEnabled",e.target.checked),className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable memory"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1 ml-6",children:"Allow this role to remember context from previous interactions"})]})]})},E=()=>(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Condition Type"}),(0,s.jsxs)("select",{value:c.conditionType||"",onChange:e=>T("conditionType",e.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,s.jsx)("option",{value:"",children:"Select Type"}),(0,s.jsx)("option",{value:"contains",children:"Contains"}),(0,s.jsx)("option",{value:"equals",children:"Equals"}),(0,s.jsx)("option",{value:"regex",children:"Regex"}),(0,s.jsx)("option",{value:"length",children:"Length"}),(0,s.jsx)("option",{value:"custom",children:"Custom"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Condition"}),(0,s.jsx)("input",{type:"text",value:c.condition||"",onChange:e=>T("condition",e.target.value),placeholder:"Enter condition...",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"True Label"}),(0,s.jsx)("input",{type:"text",value:c.trueLabel||"",onChange:e=>T("trueLabel",e.target.value),placeholder:"Continue",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"False Label"}),(0,s.jsx)("input",{type:"text",value:c.falseLabel||"",onChange:e=>T("falseLabel",e.target.value),placeholder:"Skip",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]})]}),R=()=>(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,s.jsx)("input",{type:"text",value:t.data.label,onChange:e=>a({label:e.target.value}),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description"}),(0,s.jsx)("textarea",{value:t.data.description||"",onChange:e=>a({description:e.target.value}),rows:3,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]}),F=()=>{var e,l;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Routing Strategy"}),(0,s.jsxs)("select",{value:(null==c?void 0:c.routingStrategy)||"smart",onChange:e=>{let s={...c,routingStrategy:e.target.value};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,s.jsx)("option",{value:"smart",children:"Smart Routing"}),(0,s.jsx)("option",{value:"round_robin",children:"Round Robin"}),(0,s.jsx)("option",{value:"load_balanced",children:"Load Balanced"}),(0,s.jsx)("option",{value:"priority",children:"Priority Based"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"How the router selects between available AI providers"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Retries"}),(0,s.jsx)("input",{type:"number",min:"0",max:"10",value:(null==c?void 0:c.maxRetries)||3,onChange:e=>{let s={...c,maxRetries:parseInt(e.target.value)||3};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Number of retry attempts on failure"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (ms)"}),(0,s.jsx)("input",{type:"number",min:"1000",max:"300000",step:"1000",value:(null==c?void 0:c.timeout)||3e4,onChange:e=>{let s={...c,timeout:parseInt(e.target.value)||3e4};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Request timeout in milliseconds"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Enable Caching"}),(0,s.jsx)("input",{type:"checkbox",checked:null==(e=null==c?void 0:c.enableCaching)||e,onChange:e=>{let s={...c,enableCaching:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Cache responses to improve performance"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Debug Mode"}),(0,s.jsx)("input",{type:"checkbox",checked:null!=(l=null==c?void 0:c.debugMode)&&l,onChange:e=>{let s={...c,debugMode:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Enable detailed logging for debugging"})]})]})},q=()=>{var e,l;let r=[{value:"",label:"Select a tool..."},{value:"google_drive",label:"\uD83D\uDCC1 Google Drive",description:"Access and manage Google Drive files"},{value:"google_docs",label:"\uD83D\uDCC4 Google Docs",description:"Create and edit Google Documents"},{value:"google_sheets",label:"\uD83D\uDCCA Google Sheets",description:"Work with Google Spreadsheets"},{value:"zapier",label:"⚡ Zapier",description:"Connect with 5000+ apps via Zapier"},{value:"notion",label:"\uD83D\uDCDD Notion",description:"Access Notion databases and pages"},{value:"calendar",label:"\uD83D\uDCC5 Calendar",description:"Manage calendar events and schedules"},{value:"gmail",label:"\uD83D\uDCE7 Gmail",description:"Send and manage emails"},{value:"youtube",label:"\uD83D\uDCFA YouTube",description:"Access YouTube data and analytics"},{value:"supabase",label:"\uD83D\uDDC4️ Supabase",description:"Direct database operations"}];return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Tool Type"}),(0,s.jsx)("select",{value:(null==c?void 0:c.toolType)||"",onChange:e=>{let s={...c,toolType:e.target.value,toolConfig:{},connectionStatus:"disconnected",isAuthenticated:!1};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:r.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))}),(null==c?void 0:c.toolType)&&(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:null==(e=r.find(e=>e.value===c.toolType))?void 0:e.description})]}),(null==c?void 0:c.toolType)&&(0,s.jsxs)("div",{className:"space-y-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("span",{className:"text-yellow-400",children:"●"}),(0,s.jsx)("span",{className:"text-sm font-medium text-yellow-400",children:"Authentication Required"})]}),(0,s.jsxs)("div",{className:"text-center py-4",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-400 mb-2",children:[null==(l=r.find(e=>e.value===c.toolType))?void 0:l.label," integration coming soon!"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"This tool will require account linking and authentication."})]})]}),(null==c?void 0:c.toolType)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),(0,s.jsx)("input",{type:"number",min:"5",max:"300",value:(null==c?void 0:c.timeout)||30,onChange:e=>{let s={...c,timeout:parseInt(e.target.value)||30};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum time to wait for the tool operation to complete"})]})]})},L=()=>{var e,l,r,n;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,s.jsxs)("select",{value:(null==c?void 0:c.providerId)||"",onChange:e=>{let s={...c,providerId:e.target.value,modelId:"",parameters:c.parameters||{temperature:.7,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,s.jsx)("option",{value:"",children:"Select Provider"}),(0,s.jsx)("option",{value:"openai",children:"OpenAI"}),(0,s.jsx)("option",{value:"anthropic",children:"Anthropic"}),(0,s.jsx)("option",{value:"google",children:"Google"}),(0,s.jsx)("option",{value:"deepseek",children:"DeepSeek"}),(0,s.jsx)("option",{value:"xai",children:"xAI"}),(0,s.jsx)("option",{value:"openrouter",children:"OpenRouter"})]})]}),(null==c?void 0:c.providerId)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,s.jsx)("select",{value:(null==c?void 0:c.modelId)||"",onChange:e=>{let s=e.target.value,l={...c,modelId:s};if(s&&m){let e=m.find(e=>e.id===s);if(e){let t=e.output_token_limit||e.context_window||4096,a=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=(null==c?void 0:c.parameters)||{};l={...l,parameters:{...s,maxTokens:a}}}}u(l),a({config:l,isConfigured:D(t.type,l)})},disabled:!(null==c?void 0:c.providerId)||!A.length,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent disabled:opacity-50 disabled:bg-gray-800/30",children:(null==c?void 0:c.providerId)?A.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("option",{value:"",children:"Select Model"}),A.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,s.jsx)("option",{value:"",disabled:!0,children:p?"Loading models...":"No models available"}):(0,s.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})}),p&&(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Loading models..."}),b&&(0,s.jsx)("p",{className:"text-xs text-red-400 mt-1",children:b})]}),(null==c?void 0:c.modelId)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,s.jsx)("input",{type:"password",value:(null==c?void 0:c.apiKey)||"",onChange:e=>{let s={...c,apiKey:e.target.value};u(s),a({config:s,isConfigured:D(t.type,s)})},placeholder:"Enter your API key for this provider",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"})]}),(null==c?void 0:c.modelId)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens: ",(null==c||null==(e=c.parameters)?void 0:e.maxTokens)||"Auto"]}),(0,s.jsx)("input",{type:"range",min:S.minTokens,max:S.maxTokens,value:(null==c||null==(l=c.parameters)?void 0:l.maxTokens)||S.maxTokens,onChange:e=>{let s={...c,parameters:{...c.parameters,maxTokens:parseInt(e.target.value)}};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,s.jsx)("span",{children:S.minTokens}),(0,s.jsx)("span",{children:S.maxTokens})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",(null==c||null==(r=c.parameters)?void 0:r.temperature)||.7]}),(0,s.jsx)("input",{type:"range",min:"0",max:"2",step:"0.1",value:(null==c||null==(n=c.parameters)?void 0:n.temperature)||.7,onChange:e=>{let s={...c,parameters:{...c.parameters,temperature:parseFloat(e.target.value)}};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,s.jsx)("span",{children:"0 (Focused)"}),(0,s.jsx)("span",{children:"2 (Creative)"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Subtasks"}),(0,s.jsx)("input",{type:"number",min:"1",max:"50",value:(null==c?void 0:c.maxSubtasks)||10,onChange:e=>{let s={...c,maxSubtasks:parseInt(e.target.value)||10};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum number of subtasks the planner can create"})]})]})},K=()=>{var e,l,r,n,o,i,d,m,x,p,g,b;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-green-900/20 border border-green-700 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("span",{className:"text-green-400",children:"●"}),(0,s.jsx)("span",{className:"text-sm font-medium text-green-400",children:"Intelligent Browsing Agent"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-300",children:"This node automatically plans and executes complex web browsing tasks using AI."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Sites to Visit"}),(0,s.jsx)("input",{type:"number",min:"1",max:"20",value:(null==c?void 0:c.maxSites)||5,onChange:e=>{let s={...c,maxSites:parseInt(e.target.value)||5};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout per Operation (seconds)"}),(0,s.jsx)("input",{type:"number",min:"10",max:"300",value:(null==c?void 0:c.timeout)||30,onChange:e=>{let s={...c,timeout:parseInt(e.target.value)||30};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Basic Settings"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Sites"}),(0,s.jsx)("input",{type:"number",min:"1",max:"20",value:(null==c?void 0:c.maxSites)||5,onChange:e=>{let s={...c,maxSites:parseInt(e.target.value)};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),(0,s.jsx)("input",{type:"number",min:"10",max:"300",value:(null==c?void 0:c.timeout)||30,onChange:e=>{let s={...c,timeout:parseInt(e.target.value)};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Engines"}),(0,s.jsx)("div",{className:"flex gap-2",children:["google","bing"].map(e=>{var l,r;return(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null!=(r=null==c||null==(l=c.searchEngines)?void 0:l.includes(e))?r:"google"===e,onChange:s=>{let l=(null==c?void 0:c.searchEngines)||["google"],r=s.target.checked?[...l.filter(t=>t!==e),e]:l.filter(t=>t!==e),n={...c,searchEngines:r.length>0?r:["google"]};u(n),a({config:n,isConfigured:D(t.type,n)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300 capitalize",children:e})]},e)})})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Capabilities"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(n=null==c?void 0:c.enableScreenshots)||n,onChange:e=>{let s={...c,enableScreenshots:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCF8 Take Screenshots"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(o=null==c?void 0:c.enableFormFilling)||o,onChange:e=>{let s={...c,enableFormFilling:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCDD Fill Forms Automatically"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null!=(i=null==c?void 0:c.enableCaptchaSolving)&&i,onChange:e=>{let s={...c,enableCaptchaSolving:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDD10 Solve CAPTCHAs"}),(0,s.jsx)("span",{className:"text-xs text-yellow-400",children:"(Beta)"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(d=null==c?void 0:c.enableJavaScript)||d,onChange:e=>{let s={...c,enableJavaScript:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"⚡ Enable JavaScript"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(m=null==c?void 0:c.respectRobots)||m,onChange:e=>{let s={...c,respectRobots:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83E\uDD16 Respect robots.txt"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Advanced Settings"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Browsing Depth"}),(0,s.jsx)("input",{type:"number",min:"1",max:"5",value:(null==c?void 0:c.maxDepth)||2,onChange:e=>{let s={...c,maxDepth:parseInt(e.target.value)};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"How many levels deep to follow links (1-5)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom User Agent"}),(0,s.jsx)("input",{type:"text",value:(null==c?void 0:c.userAgent)||"",onChange:e=>{let s={...c,userAgent:e.target.value};u(s),a({config:s,isConfigured:D(t.type,s)})},placeholder:"Leave empty for default",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Custom user agent string (optional)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Extraction Goals"}),(0,s.jsx)("textarea",{value:(null==c||null==(e=c.extractionGoals)?void 0:e.join(", "))||"",onChange:e=>{let s=e.target.value.split(",").map(e=>e.trim()).filter(e=>e),l={...c,extractionGoals:s};u(l),a({config:l,isConfigured:D(t.type,l)})},placeholder:"prices, contact info, products, links",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Comma-separated list of what to extract (e.g., prices, contact, products)"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(x=null==c?void 0:c.enableFormFilling)||x,onChange:e=>{let s={...c,enableFormFilling:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCDD Fill Forms Automatically"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null!=(p=null==c?void 0:c.enableCaptchaSolving)&&p,onChange:e=>{let s={...c,enableCaptchaSolving:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDD10 Attempt CAPTCHA Solving"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Engines"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(g=null==c||null==(l=c.searchEngines)?void 0:l.includes("google"))||g,onChange:e=>{let s=(null==c?void 0:c.searchEngines)||["google"],l=e.target.checked?[...s.filter(e=>"google"!==e),"google"]:s.filter(e=>"google"!==e),r={...c,searchEngines:l.length>0?l:["google"]};u(r),a({config:r,isConfigured:D(t.type,r)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"Google"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null!=(b=null==c||null==(r=c.searchEngines)?void 0:r.includes("bing"))&&b,onChange:e=>{let s=(null==c?void 0:c.searchEngines)||["google"],l=e.target.checked?[...s.filter(e=>"bing"!==e),"bing"]:s.filter(e=>"bing"!==e),r={...c,searchEngines:l.length>0?l:["google"]};u(r),a({config:r,isConfigured:D(t.type,r)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"Bing"})]})]})]})]})},O=()=>(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-300",children:"Plug & Play Memory"})]}),(0,s.jsx)("p",{className:"text-xs text-blue-200/80",children:"This memory node automatically acts as a brain for any connected node. It handles storing, retrieving, session data, and persistent memory intelligently without manual configuration."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Memory Name *"}),(0,s.jsx)("input",{type:"text",value:(null==c?void 0:c.memoryName)||"",onChange:e=>{let s={...c,memoryName:e.target.value};u(s),a({config:s,isConfigured:D(t.type,s)})},placeholder:"Enter a name for this memory (e.g., Browsing Memory, Router Memory)",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Give this memory a descriptive name for easy identification"})]}),(null==c?void 0:c.memoryName)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Storage Size (MB)"}),(0,s.jsx)("input",{type:"number",min:"1",max:"100",value:Math.round(((null==c?void 0:c.maxSize)||10240)/1024),onChange:e=>{let s=parseInt(e.target.value)||10,l={...c,maxSize:1024*s};u(l),a({config:l,isConfigured:D(t.type,l)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum storage size limit (default: 10MB)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:(null==c?void 0:c.encryption)!==!1,onChange:e=>{let s={...c,encryption:e.target.checked};u(s),a({config:s,isConfigured:D(t.type,s)})},className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable encryption (recommended)"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1 ml-6",children:"Encrypt stored data for security (enabled by default)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),(0,s.jsx)("textarea",{value:(null==c?void 0:c.description)||"",onChange:e=>{let s={...c,description:e.target.value};u(s),a({config:s,isConfigured:D(t.type,s)})},placeholder:"Describe what this memory will be used for...",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Optional description of this memory's purpose"})]})]})]});return(0,s.jsxs)("div",{className:"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-[#ff6b35]/20 rounded-lg",children:(0,s.jsx)(r.Vy,{className:"w-5 h-5 text-[#ff6b35]"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Configure Node"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:t.data.label})]})]}),(0,s.jsx)("button",{onClick:d,className:"text-gray-400 hover:text-white transition-colors p-1 rounded",children:(0,s.jsx)(r.fK,{className:"w-5 h-5"})})]}),(0,s.jsx)("div",{className:"space-y-6",children:(()=>{switch(t.type){case"provider":return M();case"vision":return P();case"roleAgent":return _();case"centralRouter":return F();case"conditional":return E();case"tool":return q();case"planner":return L();case"browsing":return K();case"memory":return O();default:return R()}})()}),(0,s.jsxs)("div",{className:"mt-6 p-3 rounded-lg border border-gray-700/50",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t.data.isConfigured?"bg-green-500":"bg-yellow-500")}),(0,s.jsx)("span",{className:"text-sm font-medium text-white",children:t.data.isConfigured?"Configured":"Needs Configuration"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:t.data.isConfigured?"This node is properly configured and ready to use.":"Complete the configuration to use this node in your workflow."})]})]})}},75521:(e,t,a)=>{a.d(t,{A:()=>d});var s=a(95155),l=a(12115),r=a(40575);let n={core:{label:"Core Nodes",description:"Essential workflow components",nodes:[{type:"userRequest",label:"User Request",description:"Starting point for user input",icon:r.ny,isAvailable:!0,defaultData:{label:"User Request",config:{},isConfigured:!0}},{type:"classifier",label:"Classifier",description:"Analyzes and categorizes requests",icon:r.YE,isAvailable:!0,defaultData:{label:"Classifier",config:{},isConfigured:!0}},{type:"output",label:"Output",description:"Final response to user",icon:r.AQ,isAvailable:!0,defaultData:{label:"Output",config:{},isConfigured:!0}}]},ai:{label:"AI Providers",description:"AI model integrations",nodes:[{type:"provider",label:"AI Provider",description:"Connect to AI models (OpenAI, Claude, etc.)",icon:r.hp,isAvailable:!0,defaultData:{label:"AI Provider",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},isConfigured:!1}},{type:"vision",label:"Vision AI",description:"Multimodal AI for image analysis and vision tasks",icon:r.bM,isAvailable:!0,defaultData:{label:"Vision AI",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},isConfigured:!1}},{type:"roleAgent",label:"Role Agent",description:"Role plugin for AI providers (connect to role input)",icon:r.K6,isAvailable:!0,defaultData:{label:"Role Agent",config:{roleId:"",roleName:"",roleType:"predefined",customPrompt:"",memoryEnabled:!1},isConfigured:!1}},{type:"centralRouter",label:"Central Router",description:"Smart routing hub for multiple AI providers and vision models",icon:r.YE,isAvailable:!0,defaultData:{label:"Central Router",config:{routingStrategy:"smart",fallbackProvider:"",maxRetries:3,timeout:3e4,enableCaching:!0,debugMode:!1},isConfigured:!0}},{type:"planner",label:"Planner",description:"AI model that creates browsing strategies and todo lists",icon:r.Pp,isAvailable:!0,defaultData:{label:"Planner",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:.7,maxTokens:1e3},maxSubtasks:10},isConfigured:!1}}]},logic:{label:"Logic & Control",description:"Flow control and decision making",nodes:[{type:"conditional",label:"Conditional",description:"Branch workflow based on conditions",icon:r.r$,isAvailable:!0,defaultData:{label:"Conditional",config:{},isConfigured:!1}},{type:"merge",label:"Merge",description:"Combine multiple inputs",icon:r.EF,isAvailable:!0,defaultData:{label:"Merge",config:{},isConfigured:!0}},{type:"switch",label:"Switch",description:"Route to different paths",icon:r.DQ,isAvailable:!0,defaultData:{label:"Switch",config:{},isConfigured:!1}},{type:"loop",label:"Loop",description:"Repeat operations",icon:r.EF,isAvailable:!0,defaultData:{label:"Loop",config:{},isConfigured:!1}}]},tools:{label:"Tools & Integrations",description:"External service integrations",nodes:[{type:"tool",label:"Tools",description:"External tool integrations (Google Drive, Zapier, etc.)",icon:r.jO,isAvailable:!0,defaultData:{label:"Tools",config:{toolType:"",toolConfig:{},timeout:30,connectionStatus:"disconnected",isAuthenticated:!1},isConfigured:!1}},{type:"memory",label:"Memory",description:"Store and retrieve data across workflow executions",icon:r.OL,isAvailable:!0,defaultData:{label:"Memory",config:{memoryName:"",maxSize:10240,encryption:!0,description:""},isConfigured:!1}}]},browsing:{label:"Web Browsing",description:"Intelligent web browsing and automation",nodes:[{type:"browsing",label:"Browsing Agent",description:"Intelligent web browsing agent with multi-step automation",icon:r.mS,isAvailable:!0,defaultData:{label:"Browsing Agent",config:{maxSites:5,timeout:30,enableScreenshots:!0,enableFormFilling:!0,enableCaptchaSolving:!1,searchEngines:["google"],maxDepth:2,respectRobots:!0,enableJavaScript:!0},isConfigured:!0}}]}};function o(e){let{node:t,onAddNode:a}=e,l=t.icon;return(0,s.jsx)("div",{draggable:!0,onDragStart:e=>{e.dataTransfer.setData("application/reactflow",t.type),e.dataTransfer.effectAllowed="move"},onClick:()=>{a(t.type)},className:"p-3 rounded-lg border cursor-pointer transition-all duration-200 ".concat(t.isAvailable?"bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50":"bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed"),title:t.description,children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat(t.isAvailable?"bg-[#ff6b35]/20 text-[#ff6b35]":"bg-gray-700/50 text-gray-500"),children:(0,s.jsx)(l,{className:"w-4 h-4"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"font-medium text-sm ".concat(t.isAvailable?"text-white":"text-gray-500"),children:t.label}),(0,s.jsx)("div",{className:"text-xs text-gray-400 truncate",children:t.description})]})]})})}function i(e){let{category:t,data:a,isExpanded:l,onToggle:n,onAddNode:i}=e;return(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("button",{onClick:n,className:"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[l?(0,s.jsx)(r.D3,{className:"w-4 h-4 text-gray-400"}):(0,s.jsx)(r.vK,{className:"w-4 h-4 text-gray-400"}),(0,s.jsx)("span",{className:"font-medium text-white",children:a.label})]}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:a.nodes.length})]}),l&&(0,s.jsx)("div",{className:"mt-2 space-y-2",children:a.nodes.map(e=>(0,s.jsx)(o,{node:e,onAddNode:i},e.type))})]})}function d(e){let{onAddNode:t}=e,[a,r]=(0,l.useState)(new Set(["core","ai"])),o=e=>{let t=new Set(a);t.has(e)?t.delete(e):t.add(e),r(t)},d=e=>{t(e,{x:400,y:200})};return(0,s.jsxs)("div",{className:"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-white mb-2",children:"Node Palette"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Drag nodes to the canvas or click to add at center"})]}),(0,s.jsx)("div",{className:"space-y-1",children:Object.entries(n).map(e=>{let[t,l]=e;return(0,s.jsx)(i,{category:t,data:l,isExpanded:a.has(t),onToggle:()=>o(t),onAddNode:d},t)})}),(0,s.jsxs)("div",{className:"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-blue-300 font-medium mb-1",children:"\uD83D\uDCA1 Pro Tip"}),(0,s.jsx)("div",{className:"text-xs text-blue-200",children:"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node."})]})]})}}}]);