(()=>{var e={};e.id=4150,e.ids=[4150],e.modules={2643:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(60687),a=r(43210),s=r(9776);let i=(0,a.forwardRef)(({className:e="",variant:t="default",size:r="default",loading:a=!1,icon:i,iconPosition:o="left",children:l,disabled:d,...c},u)=>{let m={default:"h-5 w-5",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",icon:"h-5 w-5"},f=d||a;return(0,n.jsxs)("button",{ref:u,className:`inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed ${{default:"bg-orange-600 text-white hover:bg-orange-700",primary:"bg-orange-600 text-white hover:bg-orange-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500",danger:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500"}[t]} ${{default:"px-4 py-2.5 text-sm",sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base",icon:"h-10 w-10"}[r]} ${e}`,disabled:f,...c,children:[a&&(0,n.jsx)(s.Ay,{size:"lg"===r?"md":"sm",className:"mr-2"}),!a&&i&&"left"===o&&(0,n.jsx)("span",{className:`${m[r]} mr-2`,children:i}),l,!a&&i&&"right"===o&&(0,n.jsx)("span",{className:`${m[r]} ml-2`,children:i})]})});i.displayName="Button"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8730:(e,t,r)=>{"use strict";r.d(t,{TL:()=>i});var n=r(43210),a=r(98599),s=r(60687);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var i;let e,o,l=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let n in t){let a=e[n],s=t[n];/^on[A-Z]/.test(n)?a&&s?r[n]=(...e)=>{let t=s(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...s}:"className"===n&&(r[n]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,a.t)(t,l):l),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...i}=e,o=n.Children.toArray(a),d=o.find(l);if(d){let e=d.props.children,a=o.map(t=>t!==d?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var o=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},11997:e=>{"use strict";e.exports=require("punycode")},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>o});var n=r(43210),a=r(51215),s=r(8730),i=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:a,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?r:t,{...s,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx","default")},23051:(e,t,r)=>{Promise.resolve().then(r.bind(r,41922))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41922:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n_});var n,a,s,i=r(60687),o=r(43210),l=r.t(o,2),d=r(16189),c=r(66368),u=r(62525),m=r(81521),f=r(71031),p=r(51426),h=r(26403),g=r(44725),x=r(95753),y=r(58089),v=r(97450),b=r(70143),w=r(57891),j=r(99127),N=r(50942),k=r(71178);let _=Math.min,E=Math.max,C=Math.round,A=Math.floor,S=e=>({x:e,y:e}),R={left:"right",right:"left",bottom:"top",top:"bottom"},P={start:"end",end:"start"};function T(e,t){return"function"==typeof e?e(t):e}function M(e){return e.split("-")[0]}function D(e){return e.split("-")[1]}function I(e){return"x"===e?"y":"x"}function O(e){return"y"===e?"height":"width"}function L(e){return["top","bottom"].includes(M(e))?"y":"x"}function $(e){return e.replace(/start|end/g,e=>P[e])}function F(e){return e.replace(/left|right|bottom|top/g,e=>R[e])}function W(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function z(e){let{x:t,y:r,width:n,height:a}=e;return{width:n,height:a,top:r,left:t,right:t+n,bottom:r+a,x:t,y:r}}function K(e,t,r){let n,{reference:a,floating:s}=e,i=L(t),o=I(L(t)),l=O(o),d=M(t),c="y"===i,u=a.x+a.width/2-s.width/2,m=a.y+a.height/2-s.height/2,f=a[l]/2-s[l]/2;switch(d){case"top":n={x:u,y:a.y-s.height};break;case"bottom":n={x:u,y:a.y+a.height};break;case"right":n={x:a.x+a.width,y:m};break;case"left":n={x:a.x-s.width,y:m};break;default:n={x:a.x,y:a.y}}switch(D(t)){case"start":n[o]-=f*(r&&c?-1:1);break;case"end":n[o]+=f*(r&&c?-1:1)}return n}let B=async(e,t,r)=>{let{placement:n="bottom",strategy:a="absolute",middleware:s=[],platform:i}=r,o=s.filter(Boolean),l=await (null==i.isRTL?void 0:i.isRTL(t)),d=await i.getElementRects({reference:e,floating:t,strategy:a}),{x:c,y:u}=K(d,n,l),m=n,f={},p=0;for(let r=0;r<o.length;r++){let{name:s,fn:h}=o[r],{x:g,y:x,data:y,reset:v}=await h({x:c,y:u,initialPlacement:n,placement:m,strategy:a,middlewareData:f,rects:d,platform:i,elements:{reference:e,floating:t}});c=null!=g?g:c,u=null!=x?x:u,f={...f,[s]:{...f[s],...y}},v&&p<=50&&(p++,"object"==typeof v&&(v.placement&&(m=v.placement),v.rects&&(d=!0===v.rects?await i.getElementRects({reference:e,floating:t,strategy:a}):v.rects),{x:c,y:u}=K(d,m,l)),r=-1)}return{x:c,y:u,placement:m,strategy:a,middlewareData:f}};async function q(e,t){var r;void 0===t&&(t={});let{x:n,y:a,platform:s,rects:i,elements:o,strategy:l}=e,{boundary:d="clippingAncestors",rootBoundary:c="viewport",elementContext:u="floating",altBoundary:m=!1,padding:f=0}=T(t,e),p=W(f),h=o[m?"floating"===u?"reference":"floating":u],g=z(await s.getClippingRect({element:null==(r=await (null==s.isElement?void 0:s.isElement(h)))||r?h:h.contextElement||await (null==s.getDocumentElement?void 0:s.getDocumentElement(o.floating)),boundary:d,rootBoundary:c,strategy:l})),x="floating"===u?{x:n,y:a,width:i.floating.width,height:i.floating.height}:i.reference,y=await (null==s.getOffsetParent?void 0:s.getOffsetParent(o.floating)),v=await (null==s.isElement?void 0:s.isElement(y))&&await (null==s.getScale?void 0:s.getScale(y))||{x:1,y:1},b=z(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:o,rect:x,offsetParent:y,strategy:l}):x);return{top:(g.top-b.top+p.top)/v.y,bottom:(b.bottom-g.bottom+p.bottom)/v.y,left:(g.left-b.left+p.left)/v.x,right:(b.right-g.right+p.right)/v.x}}async function H(e,t){let{placement:r,platform:n,elements:a}=e,s=await (null==n.isRTL?void 0:n.isRTL(a.floating)),i=M(r),o=D(r),l="y"===L(r),d=["left","top"].includes(i)?-1:1,c=s&&l?-1:1,u=T(t,e),{mainAxis:m,crossAxis:f,alignmentAxis:p}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return o&&"number"==typeof p&&(f="end"===o?-1*p:p),l?{x:f*c,y:m*d}:{x:m*d,y:f*c}}function U(){return"undefined"!=typeof window}function V(e){return Y(e)?(e.nodeName||"").toLowerCase():"#document"}function G(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function X(e){var t;return null==(t=(Y(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Y(e){return!!U()&&(e instanceof Node||e instanceof G(e).Node)}function J(e){return!!U()&&(e instanceof Element||e instanceof G(e).Element)}function Z(e){return!!U()&&(e instanceof HTMLElement||e instanceof G(e).HTMLElement)}function Q(e){return!!U()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof G(e).ShadowRoot)}function ee(e){let{overflow:t,overflowX:r,overflowY:n,display:a}=es(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(a)}function et(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function er(e){let t=en(),r=J(e)?es(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function en(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function ea(e){return["html","body","#document"].includes(V(e))}function es(e){return G(e).getComputedStyle(e)}function ei(e){return J(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eo(e){if("html"===V(e))return e;let t=e.assignedSlot||e.parentNode||Q(e)&&e.host||X(e);return Q(t)?t.host:t}function el(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let a=function e(t){let r=eo(t);return ea(r)?t.ownerDocument?t.ownerDocument.body:t.body:Z(r)&&ee(r)?r:e(r)}(e),s=a===(null==(n=e.ownerDocument)?void 0:n.body),i=G(a);if(s){let e=ed(i);return t.concat(i,i.visualViewport||[],ee(a)?a:[],e&&r?el(e):[])}return t.concat(a,el(a,[],r))}function ed(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ec(e){let t=es(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,a=Z(e),s=a?e.offsetWidth:r,i=a?e.offsetHeight:n,o=C(r)!==s||C(n)!==i;return o&&(r=s,n=i),{width:r,height:n,$:o}}function eu(e){return J(e)?e:e.contextElement}function em(e){let t=eu(e);if(!Z(t))return S(1);let r=t.getBoundingClientRect(),{width:n,height:a,$:s}=ec(t),i=(s?C(r.width):r.width)/n,o=(s?C(r.height):r.height)/a;return i&&Number.isFinite(i)||(i=1),o&&Number.isFinite(o)||(o=1),{x:i,y:o}}let ef=S(0);function ep(e){let t=G(e);return en()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ef}function eh(e,t,r,n){var a;void 0===t&&(t=!1),void 0===r&&(r=!1);let s=e.getBoundingClientRect(),i=eu(e),o=S(1);t&&(n?J(n)&&(o=em(n)):o=em(e));let l=(void 0===(a=r)&&(a=!1),n&&(!a||n===G(i))&&a)?ep(i):S(0),d=(s.left+l.x)/o.x,c=(s.top+l.y)/o.y,u=s.width/o.x,m=s.height/o.y;if(i){let e=G(i),t=n&&J(n)?G(n):n,r=e,a=ed(r);for(;a&&n&&t!==r;){let e=em(a),t=a.getBoundingClientRect(),n=es(a),s=t.left+(a.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(a.clientTop+parseFloat(n.paddingTop))*e.y;d*=e.x,c*=e.y,u*=e.x,m*=e.y,d+=s,c+=i,a=ed(r=G(a))}}return z({width:u,height:m,x:d,y:c})}function eg(e,t){let r=ei(e).scrollLeft;return t?t.left+r:eh(X(e)).left+r}function ex(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eg(e,n)),y:n.top+t.scrollTop}}function ey(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=G(e),n=X(e),a=r.visualViewport,s=n.clientWidth,i=n.clientHeight,o=0,l=0;if(a){s=a.width,i=a.height;let e=en();(!e||e&&"fixed"===t)&&(o=a.offsetLeft,l=a.offsetTop)}return{width:s,height:i,x:o,y:l}}(e,r);else if("document"===t)n=function(e){let t=X(e),r=ei(e),n=e.ownerDocument.body,a=E(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),s=E(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+eg(e),o=-r.scrollTop;return"rtl"===es(n).direction&&(i+=E(t.clientWidth,n.clientWidth)-a),{width:a,height:s,x:i,y:o}}(X(e));else if(J(t))n=function(e,t){let r=eh(e,!0,"fixed"===t),n=r.top+e.clientTop,a=r.left+e.clientLeft,s=Z(e)?em(e):S(1),i=e.clientWidth*s.x,o=e.clientHeight*s.y;return{width:i,height:o,x:a*s.x,y:n*s.y}}(t,r);else{let r=ep(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return z(n)}function ev(e){return"static"===es(e).position}function eb(e,t){if(!Z(e)||"fixed"===es(e).position)return null;if(t)return t(e);let r=e.offsetParent;return X(e)===r&&(r=r.ownerDocument.body),r}function ew(e,t){let r=G(e);if(et(e))return r;if(!Z(e)){let t=eo(e);for(;t&&!ea(t);){if(J(t)&&!ev(t))return t;t=eo(t)}return r}let n=eb(e,t);for(;n&&["table","td","th"].includes(V(n))&&ev(n);)n=eb(n,t);return n&&ea(n)&&ev(n)&&!er(n)?r:n||function(e){let t=eo(e);for(;Z(t)&&!ea(t);){if(er(t))return t;if(et(t))break;t=eo(t)}return null}(e)||r}let ej=async function(e){let t=this.getOffsetParent||ew,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=Z(t),a=X(t),s="fixed"===r,i=eh(e,!0,s,t),o={scrollLeft:0,scrollTop:0},l=S(0);if(n||!n&&!s)if(("body"!==V(t)||ee(a))&&(o=ei(t)),n){let e=eh(t,!0,s,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else a&&(l.x=eg(a));s&&!n&&a&&(l.x=eg(a));let d=!a||n||s?S(0):ex(a,o);return{x:i.left+o.scrollLeft-l.x-d.x,y:i.top+o.scrollTop-l.y-d.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eN={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:a}=e,s="fixed"===a,i=X(n),o=!!t&&et(t.floating);if(n===i||o&&s)return r;let l={scrollLeft:0,scrollTop:0},d=S(1),c=S(0),u=Z(n);if((u||!u&&!s)&&(("body"!==V(n)||ee(i))&&(l=ei(n)),Z(n))){let e=eh(n);d=em(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let m=!i||u||s?S(0):ex(i,l,!0);return{width:r.width*d.x,height:r.height*d.y,x:r.x*d.x-l.scrollLeft*d.x+c.x+m.x,y:r.y*d.y-l.scrollTop*d.y+c.y+m.y}},getDocumentElement:X,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:a}=e,s=[..."clippingAncestors"===r?et(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=el(e,[],!1).filter(e=>J(e)&&"body"!==V(e)),a=null,s="fixed"===es(e).position,i=s?eo(e):e;for(;J(i)&&!ea(i);){let t=es(i),r=er(i);r||"fixed"!==t.position||(a=null),(s?!r&&!a:!r&&"static"===t.position&&!!a&&["absolute","fixed"].includes(a.position)||ee(i)&&!r&&function e(t,r){let n=eo(t);return!(n===r||!J(n)||ea(n))&&("fixed"===es(n).position||e(n,r))}(e,i))?n=n.filter(e=>e!==i):a=t,i=eo(i)}return t.set(e,n),n}(t,this._c):[].concat(r),n],i=s[0],o=s.reduce((e,r)=>{let n=ey(t,r,a);return e.top=E(n.top,e.top),e.right=_(n.right,e.right),e.bottom=_(n.bottom,e.bottom),e.left=E(n.left,e.left),e},ey(t,i,a));return{width:o.right-o.left,height:o.bottom-o.top,x:o.left,y:o.top}},getOffsetParent:ew,getElementRects:ej,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ec(e);return{width:t,height:r}},getScale:em,isElement:J,isRTL:function(e){return"rtl"===es(e).direction}};function ek(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e_=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:a,y:s,placement:i,middlewareData:o}=t,l=await H(t,e);return i===(null==(r=o.offset)?void 0:r.placement)&&null!=(n=o.arrow)&&n.alignmentOffset?{}:{x:a+l.x,y:s+l.y,data:{...l,placement:i}}}}},eE=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:a}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:o={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...l}=T(e,t),d={x:r,y:n},c=await q(t,l),u=L(M(a)),m=I(u),f=d[m],p=d[u];if(s){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",r=f+c[e],n=f-c[t];f=E(r,_(f,n))}if(i){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=p+c[e],n=p-c[t];p=E(r,_(p,n))}let h=o.fn({...t,[m]:f,[u]:p});return{...h,data:{x:h.x-r,y:h.y-n,enabled:{[m]:s,[u]:i}}}}}},eC=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,a,s,i;let{placement:o,middlewareData:l,rects:d,initialPlacement:c,platform:u,elements:m}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:h,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:y=!0,...v}=T(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let b=M(o),w=L(c),j=M(c)===c,N=await (null==u.isRTL?void 0:u.isRTL(m.floating)),k=h||(j||!y?[F(c)]:function(e){let t=F(e);return[$(e),t,$(t)]}(c)),_="none"!==x;!h&&_&&k.push(...function(e,t,r,n){let a=D(e),s=function(e,t,r){let n=["left","right"],a=["right","left"];switch(e){case"top":case"bottom":if(r)return t?a:n;return t?n:a;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(M(e),"start"===r,n);return a&&(s=s.map(e=>e+"-"+a),t&&(s=s.concat(s.map($)))),s}(c,y,x,N));let E=[c,...k],C=await q(t,v),A=[],S=(null==(n=l.flip)?void 0:n.overflows)||[];if(f&&A.push(C[b]),p){let e=function(e,t,r){void 0===r&&(r=!1);let n=D(e),a=I(L(e)),s=O(a),i="x"===a?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=F(i)),[i,F(i)]}(o,d,N);A.push(C[e[0]],C[e[1]])}if(S=[...S,{placement:o,overflows:A}],!A.every(e=>e<=0)){let e=((null==(a=l.flip)?void 0:a.index)||0)+1,t=E[e];if(t&&("alignment"!==p||w===L(t)||S.every(e=>e.overflows[0]>0&&L(e.placement)===w)))return{data:{index:e,overflows:S},reset:{placement:t}};let r=null==(s=S.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:s.placement;if(!r)switch(g){case"bestFit":{let e=null==(i=S.filter(e=>{if(_){let t=L(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(r=e);break}case"initialPlacement":r=c}if(o!==r)return{reset:{placement:r}}}return{}}}},eA=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:a,rects:s,platform:i,elements:o,middlewareData:l}=t,{element:d,padding:c=0}=T(e,t)||{};if(null==d)return{};let u=W(c),m={x:r,y:n},f=I(L(a)),p=O(f),h=await i.getDimensions(d),g="y"===f,x=g?"clientHeight":"clientWidth",y=s.reference[p]+s.reference[f]-m[f]-s.floating[p],v=m[f]-s.reference[f],b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(d)),w=b?b[x]:0;w&&await (null==i.isElement?void 0:i.isElement(b))||(w=o.floating[x]||s.floating[p]);let j=w/2-h[p]/2-1,N=_(u[g?"top":"left"],j),k=_(u[g?"bottom":"right"],j),C=w-h[p]-k,A=w/2-h[p]/2+(y/2-v/2),S=E(N,_(A,C)),R=!l.arrow&&null!=D(a)&&A!==S&&s.reference[p]/2-(A<N?N:k)-h[p]/2<0,P=R?A<N?A-N:A-C:0;return{[f]:m[f]+P,data:{[f]:S,centerOffset:A-S-P,...R&&{alignmentOffset:P}},reset:R}}}),eS=(e,t,r)=>{let n=new Map,a={platform:eN,...r},s={...a.platform,_c:n};return B(e,t,{...a,platform:s})};var eR=r(47281);let eP={core:!1,base:!1};function eT({css:e,id:t="react-tooltip-base-styles",type:r="base",ref:n}){var a,s;if(!e||"undefined"==typeof document||eP[r]||"core"===r&&"undefined"!=typeof process&&(null==(a=null==process?void 0:process.env)?void 0:a.REACT_TOOLTIP_DISABLE_CORE_STYLES)||"base"!==r&&"undefined"!=typeof process&&(null==(s=null==process?void 0:process.env)?void 0:s.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;"core"===r&&(t="react-tooltip-core-styles"),n||(n={});let{insertAt:i}=n;if(document.getElementById(t))return;let o=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.id=t,l.type="text/css","top"===i&&o.firstChild?o.insertBefore(l,o.firstChild):o.appendChild(l),l.styleSheet?l.styleSheet.cssText=e:l.appendChild(document.createTextNode(e)),eP[r]=!0}let eM=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:r=null,place:n="top",offset:a=10,strategy:s="absolute",middlewares:i=[e_(Number(a)),eC({fallbackAxisSideDirection:"start"}),eE({padding:5})],border:o,arrowSize:l=8})=>e&&null!==t?r?(i.push(eA({element:r,padding:5})),eS(e,t,{placement:n,strategy:s,middleware:i}).then(({x:e,y:t,placement:r,middlewareData:n})=>{var a,s;let i={left:`${e}px`,top:`${t}px`,border:o},{x:d,y:c}=null!=(a=n.arrow)?a:{x:0,y:0},u=null!=(s=({top:"bottom",right:"left",bottom:"top",left:"right"})[r.split("-")[0]])?s:"bottom",m=0;if(o){let e=`${o}`.match(/(\d+)px/);m=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:i,tooltipArrowStyles:{left:null!=d?`${d}px`:"",top:null!=c?`${c}px`:"",right:"",bottom:"",...o&&{borderBottom:o,borderRight:o},[u]:`-${l/2+m}px`},place:r}})):eS(e,t,{placement:"bottom",strategy:s,middleware:i}).then(({x:e,y:t,placement:r})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:r})):{tooltipStyles:{},tooltipArrowStyles:{},place:n},eD=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),eI=(e,t,r)=>{let n=null,a=function(...a){let s=()=>{n=null,r||e.apply(this,a)};r&&!n&&(e.apply(this,a),n=setTimeout(s,t)),r||(n&&clearTimeout(n),n=setTimeout(s,t))};return a.cancel=()=>{n&&(clearTimeout(n),n=null)},a},eO=e=>null!==e&&!Array.isArray(e)&&"object"==typeof e,eL=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,r)=>eL(e,t[r]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!eO(e)||!eO(t))return e===t;let r=Object.keys(e),n=Object.keys(t);return r.length===n.length&&r.every(r=>eL(e[r],t[r]))},e$=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;let t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(e=>{let r=t.getPropertyValue(e);return"auto"===r||"scroll"===r})},eF=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(e$(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},eW="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,ez=e=>{e.current&&(clearTimeout(e.current),e.current=null)},eK={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},eB=(0,o.createContext)({getTooltipData:()=>eK});function eq(e="DEFAULT_TOOLTIP_ID"){return(0,o.useContext)(eB).getTooltipData(e)}var eH={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},eU={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};let eV=({forwardRef:e,id:t,className:r,classNameArrow:n,variant:a="dark",anchorId:s,anchorSelect:i,place:l="top",offset:d=10,events:c=["hover"],openOnClick:u=!1,positionStrategy:m="absolute",middlewares:f,wrapper:p,delayShow:h=0,delayHide:g=0,float:x=!1,hidden:y=!1,noArrow:v=!1,clickable:b=!1,closeOnEsc:w=!1,closeOnScroll:j=!1,closeOnResize:N=!1,openEvents:k,closeEvents:C,globalCloseEvents:S,imperativeModeOnly:R,style:P,position:T,afterShow:M,afterHide:D,disableTooltip:I,content:O,contentWrapperRef:L,isOpen:$,defaultIsOpen:F=!1,setIsOpen:W,activeAnchor:z,setActiveAnchor:K,border:B,opacity:q,arrowColor:H,arrowSize:U=8,role:V="tooltip"})=>{var G;let Y=(0,o.useRef)(null),J=(0,o.useRef)(null),Z=(0,o.useRef)(null),Q=(0,o.useRef)(null),ee=(0,o.useRef)(null),[et,er]=(0,o.useState)({tooltipStyles:{},tooltipArrowStyles:{},place:l}),[en,ea]=(0,o.useState)(!1),[es,ei]=(0,o.useState)(!1),[eo,ed]=(0,o.useState)(null),ec=(0,o.useRef)(!1),em=(0,o.useRef)(null),{anchorRefs:ef,setActiveAnchor:ep}=eq(t),eg=(0,o.useRef)(!1),[ex,ey]=(0,o.useState)([]),ev=(0,o.useRef)(!1),eb=u||c.includes("click"),ew=eb||(null==k?void 0:k.click)||(null==k?void 0:k.dblclick)||(null==k?void 0:k.mousedown),ej=k?{...k}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!k&&eb&&Object.assign(ej,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});let eN=C?{...C}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!C&&eb&&Object.assign(eN,{mouseleave:!1,blur:!1,mouseout:!1});let e_=S?{...S}:{escape:w||!1,scroll:j||!1,resize:N||!1,clickOutsideAnchor:ew||!1};R&&(Object.assign(ej,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(eN,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(e_,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),eW(()=>(ev.current=!0,()=>{ev.current=!1}),[]);let eE=e=>{ev.current&&(e&&ei(!0),setTimeout(()=>{ev.current&&(null==W||W(e),void 0===$&&ea(e))},10))};(0,o.useEffect)(()=>{if(void 0===$)return()=>null;$&&ei(!0);let e=setTimeout(()=>{ea($)},10);return()=>{clearTimeout(e)}},[$]),(0,o.useEffect)(()=>{en!==ec.current&&((ez(ee),ec.current=en,en)?null==M||M():ee.current=setTimeout(()=>{ei(!1),ed(null),null==D||D()},(e=>{let t=e.match(/^([\d.]+)(ms|s)$/);if(!t)return 0;let[,r,n]=t;return Number(r)*("ms"===n?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"))+25))},[en]);let eC=e=>{er(t=>eL(t,e)?t:e)},eA=(e=h)=>{ez(Z),es?eE(!0):Z.current=setTimeout(()=>{eE(!0)},e)},eS=(e=g)=>{ez(Q),Q.current=setTimeout(()=>{eg.current||eE(!1)},e)},eP=e=>{var t;if(!e)return;let r=null!=(t=e.currentTarget)?t:e.target;if(!(null==r?void 0:r.isConnected))return K(null),void ep({current:null});h?eA():eE(!0),K(r),ep({current:r}),ez(Q)},eT=()=>{b?eS(g||100):g?eS():eE(!1),ez(Z)},eD=({x:e,y:t})=>{var r;eM({place:null!=(r=null==eo?void 0:eo.place)?r:l,offset:d,elementReference:{getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})},tooltipReference:Y.current,tooltipArrowReference:J.current,strategy:m,middlewares:f,border:B,arrowSize:U}).then(e=>{eC(e)})},eO=e=>{if(!e)return;let t={x:e.clientX,y:e.clientY};eD(t),em.current=t},e$=e=>{var t;if(!en)return;let r=e.target;r.isConnected&&(null==(t=Y.current)||!t.contains(r))&&([document.querySelector(`[id='${s}']`),...ex].some(e=>null==e?void 0:e.contains(r))||(eE(!1),ez(Z)))},eK=eI(eP,50,!0),eB=eI(eT,50,!0),eV=e=>{eB.cancel(),eK(e)},eG=()=>{eK.cancel(),eB()},eX=(0,o.useCallback)(()=>{var e,t;let r=null!=(e=null==eo?void 0:eo.position)?e:T;r?eD(r):x?em.current&&eD(em.current):(null==z?void 0:z.isConnected)&&eM({place:null!=(t=null==eo?void 0:eo.place)?t:l,offset:d,elementReference:z,tooltipReference:Y.current,tooltipArrowReference:J.current,strategy:m,middlewares:f,border:B,arrowSize:U}).then(e=>{ev.current&&eC(e)})},[en,z,O,P,l,null==eo?void 0:eo.place,d,m,T,null==eo?void 0:eo.position,x,U]);(0,o.useEffect)(()=>{var e,t;let r=new Set(ef);ex.forEach(e=>{(null==I?void 0:I(e))||r.add({current:e})});let n=document.querySelector(`[id='${s}']`);!n||(null==I?void 0:I(n))||r.add({current:n});let a=()=>{eE(!1)},i=eF(z),o=eF(Y.current);e_.scroll&&(window.addEventListener("scroll",a),null==i||i.addEventListener("scroll",a),null==o||o.addEventListener("scroll",a));let l=null;e_.resize?window.addEventListener("resize",a):z&&Y.current&&(l=function(e,t,r,n){let a;void 0===n&&(n={});let{ancestorScroll:s=!0,ancestorResize:i=!0,elementResize:o="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:d=!1}=n,c=eu(e),u=s||i?[...c?el(c):[],...el(t)]:[];u.forEach(e=>{s&&e.addEventListener("scroll",r,{passive:!0}),i&&e.addEventListener("resize",r)});let m=c&&l?function(e,t){let r,n=null,a=X(e);function s(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function i(o,l){void 0===o&&(o=!1),void 0===l&&(l=1),s();let d=e.getBoundingClientRect(),{left:c,top:u,width:m,height:f}=d;if(o||t(),!m||!f)return;let p=A(u),h=A(a.clientWidth-(c+m)),g={rootMargin:-p+"px "+-h+"px "+-A(a.clientHeight-(u+f))+"px "+-A(c)+"px",threshold:E(0,_(1,l))||1},x=!0;function y(t){let n=t[0].intersectionRatio;if(n!==l){if(!x)return i();n?i(!1,n):r=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==n||ek(d,e.getBoundingClientRect())||i(),x=!1}try{n=new IntersectionObserver(y,{...g,root:a.ownerDocument})}catch(e){n=new IntersectionObserver(y,g)}n.observe(e)}(!0),s}(c,r):null,f=-1,p=null;o&&(p=new ResizeObserver(e=>{let[n]=e;n&&n.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var e;null==(e=p)||e.observe(t)})),r()}),c&&!d&&p.observe(c),p.observe(t));let h=d?eh(e):null;return d&&function t(){let n=eh(e);h&&!ek(h,n)&&r(),h=n,a=requestAnimationFrame(t)}(),r(),()=>{var e;u.forEach(e=>{s&&e.removeEventListener("scroll",r),i&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=p)||e.disconnect(),p=null,d&&cancelAnimationFrame(a)}}(z,Y.current,eX,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));let d=e=>{"Escape"===e.key&&eE(!1)};e_.escape&&window.addEventListener("keydown",d),e_.clickOutsideAnchor&&window.addEventListener("click",e$);let c=[],u=e=>!!((null==e?void 0:e.target)&&(null==z?void 0:z.contains(e.target))),m=e=>{en&&u(e)||eP(e)},f=e=>{en&&u(e)&&eT()},p=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],h=["click","dblclick","mousedown","mouseup"];Object.entries(ej).forEach(([e,t])=>{t&&(p.includes(e)?c.push({event:e,listener:eV}):h.includes(e)&&c.push({event:e,listener:m}))}),Object.entries(eN).forEach(([e,t])=>{t&&(p.includes(e)?c.push({event:e,listener:eG}):h.includes(e)&&c.push({event:e,listener:f}))}),x&&c.push({event:"pointermove",listener:eO});let g=()=>{eg.current=!0},y=()=>{eg.current=!1,eT()},v=b&&(eN.mouseout||eN.mouseleave);return v&&(null==(e=Y.current)||e.addEventListener("mouseover",g),null==(t=Y.current)||t.addEventListener("mouseout",y)),c.forEach(({event:e,listener:t})=>{r.forEach(r=>{var n;null==(n=r.current)||n.addEventListener(e,t)})}),()=>{var e,t;e_.scroll&&(window.removeEventListener("scroll",a),null==i||i.removeEventListener("scroll",a),null==o||o.removeEventListener("scroll",a)),e_.resize?window.removeEventListener("resize",a):null==l||l(),e_.clickOutsideAnchor&&window.removeEventListener("click",e$),e_.escape&&window.removeEventListener("keydown",d),v&&(null==(e=Y.current)||e.removeEventListener("mouseover",g),null==(t=Y.current)||t.removeEventListener("mouseout",y)),c.forEach(({event:e,listener:t})=>{r.forEach(r=>{var n;null==(n=r.current)||n.removeEventListener(e,t)})})}},[z,eX,es,ef,ex,k,C,S,eb,h,g]),(0,o.useEffect)(()=>{var e,r;let n=null!=(r=null!=(e=null==eo?void 0:eo.anchorSelect)?e:i)?r:"";!n&&t&&(n=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);let a=new MutationObserver(e=>{let r=[],a=[];e.forEach(e=>{if("attributes"===e.type&&"data-tooltip-id"===e.attributeName&&(e.target.getAttribute("data-tooltip-id")===t?r.push(e.target):e.oldValue===t&&a.push(e.target)),"childList"===e.type){if(z){let t=[...e.removedNodes].filter(e=>1===e.nodeType);if(n)try{a.push(...t.filter(e=>e.matches(n))),a.push(...t.flatMap(e=>[...e.querySelectorAll(n)]))}catch(e){}t.some(e=>{var t;return!!(null==(t=null==e?void 0:e.contains)?void 0:t.call(e,z))&&(ei(!1),eE(!1),K(null),ez(Z),ez(Q),!0)})}if(n)try{let t=[...e.addedNodes].filter(e=>1===e.nodeType);r.push(...t.filter(e=>e.matches(n))),r.push(...t.flatMap(e=>[...e.querySelectorAll(n)]))}catch(e){}}}),(r.length||a.length)&&ey(e=>[...e.filter(e=>!a.includes(e)),...r])});return a.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{a.disconnect()}},[t,i,null==eo?void 0:eo.anchorSelect,z]),(0,o.useEffect)(()=>{eX()},[eX]),(0,o.useEffect)(()=>{if(!(null==L?void 0:L.current))return()=>null;let e=new ResizeObserver(()=>{setTimeout(()=>eX())});return e.observe(L.current),()=>{e.disconnect()}},[O,null==L?void 0:L.current]),(0,o.useEffect)(()=>{var e;let t=document.querySelector(`[id='${s}']`),r=[...ex,t];z&&r.includes(z)||K(null!=(e=ex[0])?e:t)},[s,ex,z]),(0,o.useEffect)(()=>(F&&eE(!0),()=>{ez(Z),ez(Q)}),[]),(0,o.useEffect)(()=>{var e;let r=null!=(e=null==eo?void 0:eo.anchorSelect)?e:i;if(!r&&t&&(r=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),r)try{let e=Array.from(document.querySelectorAll(r));ey(e)}catch(e){ey([])}},[t,i,null==eo?void 0:eo.anchorSelect]),(0,o.useEffect)(()=>{Z.current&&(ez(Z),eA(h))},[h]);let eY=null!=(G=null==eo?void 0:eo.content)?G:O,eJ=en&&Object.keys(et.tooltipStyles).length>0;return(0,o.useImperativeHandle)(e,()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}ed(null!=e?e:null),(null==e?void 0:e.delay)?eA(e.delay):eE(!0)},close:e=>{(null==e?void 0:e.delay)?eS(e.delay):eE(!1)},activeAnchor:z,place:et.place,isOpen:!!(es&&!y&&eY&&eJ)})),es&&!y&&eY?o.createElement(p,{id:t,role:V,className:eR("react-tooltip",eH.tooltip,eU.tooltip,eU[a],r,`react-tooltip__place-${et.place}`,eH[eJ?"show":"closing"],eJ?"react-tooltip__show":"react-tooltip__closing","fixed"===m&&eH.fixed,b&&eH.clickable),onTransitionEnd:e=>{ez(ee),en||"opacity"!==e.propertyName||(ei(!1),ed(null),null==D||D())},style:{...P,...et.tooltipStyles,opacity:void 0!==q&&eJ?q:void 0},ref:Y},eY,o.createElement(p,{className:eR("react-tooltip-arrow",eH.arrow,eU.arrow,n,v&&eH.noArrow),style:{...et.tooltipArrowStyles,background:H?`linear-gradient(to right bottom, transparent 50%, ${H} 50%)`:void 0,"--rt-arrow-size":`${U}px`},ref:J})):null},eG=({content:e})=>o.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),eX=o.forwardRef(({id:e,anchorId:t,anchorSelect:r,content:n,html:a,render:s,className:i,classNameArrow:l,variant:d="dark",place:c="top",offset:u=10,wrapper:m="div",children:f=null,events:p=["hover"],openOnClick:h=!1,positionStrategy:g="absolute",middlewares:x,delayShow:y=0,delayHide:v=0,float:b=!1,hidden:w=!1,noArrow:j=!1,clickable:N=!1,closeOnEsc:k=!1,closeOnScroll:_=!1,closeOnResize:E=!1,openEvents:C,closeEvents:A,globalCloseEvents:S,imperativeModeOnly:R=!1,style:P,position:T,isOpen:M,defaultIsOpen:D=!1,disableStyleInjection:I=!1,border:O,opacity:L,arrowColor:$,arrowSize:F,setIsOpen:W,afterShow:z,afterHide:K,disableTooltip:B,role:q="tooltip"},H)=>{let[U,V]=(0,o.useState)(n),[G,X]=(0,o.useState)(a),[Y,J]=(0,o.useState)(c),[Z,Q]=(0,o.useState)(d),[ee,et]=(0,o.useState)(u),[er,en]=(0,o.useState)(y),[ea,es]=(0,o.useState)(v),[ei,eo]=(0,o.useState)(b),[el,ed]=(0,o.useState)(w),[ec,eu]=(0,o.useState)(m),[em,ef]=(0,o.useState)(p),[ep,eh]=(0,o.useState)(g),[eg,ex]=(0,o.useState)(null),[ey,ev]=(0,o.useState)(null),eb=(0,o.useRef)(I),{anchorRefs:ew,activeAnchor:ej}=eq(e),eN=e=>null==e?void 0:e.getAttributeNames().reduce((t,r)=>{var n;return r.startsWith("data-tooltip-")&&(t[r.replace(/^data-tooltip-/,"")]=null!=(n=null==e?void 0:e.getAttribute(r))?n:null),t},{}),ek=e=>{let t={place:e=>{J(null!=e?e:c)},content:e=>{V(null!=e?e:n)},html:e=>{X(null!=e?e:a)},variant:e=>{Q(null!=e?e:d)},offset:e=>{et(null===e?u:Number(e))},wrapper:e=>{eu(null!=e?e:m)},events:e=>{let t=null==e?void 0:e.split(" ");ef(null!=t?t:p)},"position-strategy":e=>{eh(null!=e?e:g)},"delay-show":e=>{en(null===e?y:Number(e))},"delay-hide":e=>{es(null===e?v:Number(e))},float:e=>{eo(null===e?b:"true"===e)},hidden:e=>{ed(null===e?w:"true"===e)},"class-name":e=>{ex(e)}};Object.values(t).forEach(e=>e(null)),Object.entries(e).forEach(([e,r])=>{var n;null==(n=t[e])||n.call(t,r)})};(0,o.useEffect)(()=>{V(n)},[n]),(0,o.useEffect)(()=>{X(a)},[a]),(0,o.useEffect)(()=>{J(c)},[c]),(0,o.useEffect)(()=>{Q(d)},[d]),(0,o.useEffect)(()=>{et(u)},[u]),(0,o.useEffect)(()=>{en(y)},[y]),(0,o.useEffect)(()=>{es(v)},[v]),(0,o.useEffect)(()=>{eo(b)},[b]),(0,o.useEffect)(()=>{ed(w)},[w]),(0,o.useEffect)(()=>{eh(g)},[g]),(0,o.useEffect)(()=>{eb.current!==I&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[I]),(0,o.useEffect)(()=>{"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:"core"===I,disableBase:I}}))},[]),(0,o.useEffect)(()=>{var n;let a=new Set(ew),s=r;if(!s&&e&&(s=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),s)try{document.querySelectorAll(s).forEach(e=>{a.add({current:e})})}catch(e){console.warn(`[react-tooltip] "${s}" is not a valid CSS selector`)}let i=document.querySelector(`[id='${t}']`);if(i&&a.add({current:i}),!a.size)return()=>null;let o=null!=(n=null!=ey?ey:i)?n:ej.current,l=new MutationObserver(e=>{e.forEach(e=>{var t;o&&"attributes"===e.type&&(null==(t=e.attributeName)?void 0:t.startsWith("data-tooltip-"))&&ek(eN(o))})});return o&&(ek(eN(o)),l.observe(o,{attributes:!0,childList:!1,subtree:!1})),()=>{l.disconnect()}},[ew,ej,ey,t,r]),(0,o.useEffect)(()=>{(null==P?void 0:P.border)&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),O&&!eD("border",`${O}`)&&console.warn(`[react-tooltip] "${O}" is not a valid \`border\`.`),(null==P?void 0:P.opacity)&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),L&&!eD("opacity",`${L}`)&&console.warn(`[react-tooltip] "${L}" is not a valid \`opacity\`.`)},[]);let e_=f,eE=(0,o.useRef)(null);if(s){let e=s({content:(null==ey?void 0:ey.getAttribute("data-tooltip-content"))||U||null,activeAnchor:ey});e_=e?o.createElement("div",{ref:eE,className:"react-tooltip-content-wrapper"},e):null}else U&&(e_=U);G&&(e_=o.createElement(eG,{content:G}));let eC={forwardRef:H,id:e,anchorId:t,anchorSelect:r,className:eR(i,eg),classNameArrow:l,content:e_,contentWrapperRef:eE,place:Y,variant:Z,offset:ee,wrapper:ec,events:em,openOnClick:h,positionStrategy:ep,middlewares:x,delayShow:er,delayHide:ea,float:ei,hidden:el,noArrow:j,clickable:N,closeOnEsc:k,closeOnScroll:_,closeOnResize:E,openEvents:C,closeEvents:A,globalCloseEvents:S,imperativeModeOnly:R,style:P,position:T,isOpen:M,defaultIsOpen:D,border:O,opacity:L,arrowColor:$,arrowSize:F,setIsOpen:W,afterShow:z,afterHide:K,disableTooltip:B,activeAnchor:ey,setActiveAnchor:e=>ev(e),role:q};return o.createElement(eV,{...eC})});"undefined"!=typeof window&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||eT({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s;--rt-arrow-size:8px}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit;z-index:-1}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||eT({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:var(--rt-arrow-size);height:var(--rt-arrow-size)}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});var eY=r(50181),eJ=r(20404),eZ=r(5097);function eQ(){return(0,i.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-48 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-64 animate-pulse"})]})]})}),(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded w-32 mb-6 animate-pulse"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-16 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-12 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded animate-pulse"})]})]}),(0,i.jsx)("div",{className:"mt-6",children:(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded w-32 animate-pulse"})})]}),(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded w-40 animate-pulse"}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-24 animate-pulse"})]}),(0,i.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,i.jsxs)("div",{className:"border border-gray-700/50 rounded-lg p-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gray-700 rounded-lg animate-pulse"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-5 bg-gray-700 rounded w-32 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-48 animate-pulse"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-20 animate-pulse"}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"})]})]}),(0,i.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-16 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-12 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-16 animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-20 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-14 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-18 animate-pulse"})]})]}),(0,i.jsxs)("div",{className:"mt-4",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-28 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded w-36 animate-pulse"}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-28 animate-pulse"})]}),(0,i.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,i.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-700/50 rounded-lg",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-32 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-48 animate-pulse"})]}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"})]},e))})]})]})}function e0(){return(0,i.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded w-40 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-56 animate-pulse"})]})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-32 animate-pulse"})]})]})},e))})]})}var e1=r(60925),e2=r(36721),e5=r(2643),e4=r(62688);let e3=(0,e4.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),e6=(0,e4.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),e9=(0,e4.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var e8=r(52581),e7=r(43985);let te=(0,e7.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-orange-600 text-white hover:bg-orange-700",secondary:"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700",outline:"text-gray-700 border-gray-300"}},defaultVariants:{variant:"default"}});function tt({className:e,variant:t,...r}){return(0,i.jsx)("div",{className:`${te({variant:t})} ${e||""}`,...r})}let tr=(0,e4.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),tn=(0,e4.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),ta=(0,e4.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),ts=(0,e4.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),ti=(0,e4.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),to=(0,e4.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),tl=Symbol.for("constructDateFrom");function td(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&tl in e?e[tl](t):e instanceof Date?new e.constructor(t):new Date(t)}let tc={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function tu(e){return (t={})=>{let r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let tm={date:tu({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:tu({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:tu({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},tf={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function tp(e){return(t,r)=>{let n;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,a=r?.width?String(r.width):t;n=e.formattingValues[a]||e.formattingValues[t]}else{let t=e.defaultWidth,a=r?.width?String(r.width):e.defaultWidth;n=e.values[a]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}function th(e){return(t,r={})=>{let n,a=r.width,s=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],i=t.match(s);if(!i)return null;let o=i[0],l=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(l,e=>e.test(o)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(l,e=>e.test(o));return n=e.valueCallback?e.valueCallback(d):d,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(o.length)}}}let tg={code:"en-US",formatDistance:(e,t,r)=>{let n,a=tc[e];if(n="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",t.toString()),r?.addSuffix)if(r.comparison&&r.comparison>0)return"in "+n;else return n+" ago";return n},formatLong:tm,formatRelative:(e,t,r,n)=>tf[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:tp({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:tp({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:tp({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:tp({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:tp({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,r={})=>{let n=t.match(e.matchPattern);if(!n)return null;let a=n[0],s=t.match(e.parsePattern);if(!s)return null;let i=e.valueCallback?e.valueCallback(s[0]):s[0];return{value:i=r.valueCallback?r.valueCallback(i):i,rest:t.slice(a.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:th({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:th({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:th({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:th({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:th({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},tx={};function ty(e,t){return td(t||e,e)}function tv(e){let t=ty(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),e-r}function tb(e,...t){let r=td.bind(null,e||t.find(e=>"object"==typeof e));return t.map(r)}function tw(e,t){let r=ty(e)-ty(t);return r<0?-1:r>0?1:r}function tj(e,t){return function(e,t,r){let n,a=r?.locale??tx.locale??tg,s=tw(e,t);if(isNaN(s))throw RangeError("Invalid time value");let i=Object.assign({},r,{addSuffix:r?.addSuffix,comparison:s}),[o,l]=tb(r?.in,...s>0?[t,e]:[e,t]),d=function(e,t,r){var n;return(n=void 0,e=>{let t=(n?Math[n]:Math.trunc)(e);return 0===t?0:t})((ty(e)-ty(t))/1e3)}(l,o),c=Math.round((d-(tv(l)-tv(o))/1e3)/60);if(c<2)if(r?.includeSeconds)if(d<5)return a.formatDistance("lessThanXSeconds",5,i);else if(d<10)return a.formatDistance("lessThanXSeconds",10,i);else if(d<20)return a.formatDistance("lessThanXSeconds",20,i);else if(d<40)return a.formatDistance("halfAMinute",0,i);else if(d<60)return a.formatDistance("lessThanXMinutes",1,i);else return a.formatDistance("xMinutes",1,i);else if(0===c)return a.formatDistance("lessThanXMinutes",1,i);else return a.formatDistance("xMinutes",c,i);if(c<45)return a.formatDistance("xMinutes",c,i);if(c<90)return a.formatDistance("aboutXHours",1,i);if(c<1440){let e=Math.round(c/60);return a.formatDistance("aboutXHours",e,i)}if(c<2520)return a.formatDistance("xDays",1,i);else if(c<43200){let e=Math.round(c/1440);return a.formatDistance("xDays",e,i)}else if(c<86400)return n=Math.round(c/43200),a.formatDistance("aboutXMonths",n,i);if((n=function(e,t,r){let[n,a,s]=tb(void 0,e,e,t),i=tw(a,s),o=Math.abs(function(e,t,r){let[n,a]=tb(void 0,e,t);return 12*(n.getFullYear()-a.getFullYear())+(n.getMonth()-a.getMonth())}(a,s));if(o<1)return 0;1===a.getMonth()&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-i*o);let l=tw(a,s)===-i;(function(e,t){let r=ty(e,void 0);return+function(e,t){let r=ty(e,t?.in);return r.setHours(23,59,59,999),r}(r,void 0)==+function(e,t){let r=ty(e,t?.in),n=r.getMonth();return r.setFullYear(r.getFullYear(),n+1,0),r.setHours(23,59,59,999),r}(r,t)})(n)&&1===o&&1===tw(n,s)&&(l=!1);let d=i*(o-l);return 0===d?0:d}(l,o))<12){let e=Math.round(c/43200);return a.formatDistance("xMonths",e,i)}{let e=n%12,t=Math.trunc(n/12);return e<3?a.formatDistance("aboutXYears",t,i):e<9?a.formatDistance("overXYears",t,i):a.formatDistance("almostXYears",t+1,i)}}(e,td(e,Date.now()),t)}function tN({apiKey:e,onRevoke:t}){let r=async e=>{try{await navigator.clipboard.writeText(e),e8.oR.success("API key copied to clipboard")}catch(e){e8.oR.error("Failed to copy API key")}},n=e.expires_at&&new Date(e.expires_at)<new Date,a="active"===e.status&&!n;return(0,i.jsxs)("div",{className:`bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 transition-all duration-200 hover:border-gray-700/50 ${!a?"opacity-75":""}`,children:[(0,i.jsx)("div",{className:"pb-3",children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white",children:e.key_name}),(0,i.jsxs)("p",{className:"text-sm text-gray-400",children:["Configuration: ",e.custom_api_configs.name]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(tt,{className:(e=>{switch(e){case"active":return"bg-green-900/30 text-green-300 border-green-500/50";case"inactive":return"bg-yellow-900/30 text-yellow-300 border-yellow-500/50";case"revoked":return"bg-red-900/30 text-red-300 border-red-500/50";default:return"bg-gray-800/50 text-gray-300 border-gray-600/50"}})(e.status),children:e.status}),n&&(0,i.jsx)(tt,{className:"bg-red-900/30 text-red-300 border-red-500/50",children:"Expired"})]})]})}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"API Key (Masked)"}),(0,i.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,i.jsxs)("code",{className:"flex-1 text-sm font-mono text-gray-300",children:[e.key_prefix,"_","*".repeat(28),"string"==typeof e.masked_key?e.masked_key.slice(-4):"xxxx"]}),(0,i.jsx)(e5.$,{variant:"ghost",size:"sm",onClick:()=>r(`${e.key_prefix}_${"*".repeat(28)}${"string"==typeof e.masked_key?e.masked_key.slice(-4):"xxxx"}`),className:"h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700/50",title:"Copy masked key (for reference only)",children:(0,i.jsx)(tr,{className:"h-4 w-4"})})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-400 bg-amber-900/20 p-2 rounded",children:[(0,i.jsx)("span",{children:"⚠️"}),(0,i.jsx)("span",{children:"Full API key was only shown once during creation for security. Save it securely when creating new keys."})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Permissions"}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.permissions.chat&&(0,i.jsx)(tt,{variant:"secondary",className:"text-xs bg-blue-900/30 text-blue-300 border-blue-500/50",children:"Chat Completions"}),e.permissions.streaming&&(0,i.jsx)(tt,{variant:"secondary",className:"text-xs bg-green-900/30 text-green-300 border-green-500/50",children:"Streaming"}),e.permissions.all_models&&(0,i.jsx)(tt,{variant:"secondary",className:"text-xs bg-purple-900/30 text-purple-300 border-purple-500/50",children:"All Models"})]})]}),(e.allowed_ips.length>0||e.allowed_domains.length>0)&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,i.jsx)(tn,{className:"h-4 w-4"}),"Security Restrictions"]}),(0,i.jsxs)("div",{className:"space-y-1 text-xs",children:[e.allowed_ips.length>0&&(0,i.jsxs)("div",{className:"flex items-center gap-1 text-gray-400",children:[(0,i.jsx)("span",{className:"font-medium",children:"IPs:"}),(0,i.jsx)("span",{children:e.allowed_ips.join(", ")})]}),e.allowed_domains.length>0&&(0,i.jsxs)("div",{className:"flex items-center gap-1 text-gray-400",children:[(0,i.jsx)(ta,{className:"h-3 w-3"}),(0,i.jsx)("span",{className:"font-medium",children:"Domains:"}),(0,i.jsx)("span",{children:e.allowed_domains.join(", ")})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,i.jsx)(ts,{className:"h-4 w-4"}),"Usage Statistics"]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-400",children:"Total Requests:"}),(0,i.jsx)("span",{className:"ml-2 font-semibold text-white",children:e.total_requests.toLocaleString()})]}),e.last_used_at&&(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-400",children:"Last Used:"}),(0,i.jsx)("span",{className:"ml-2 font-semibold text-white",children:tj(new Date(e.last_used_at),{addSuffix:!0})})]})]})]}),e.expires_at&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,i.jsx)(ti,{className:"h-4 w-4"}),"Expiration"]}),(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsxs)("span",{className:`font-semibold ${n?"text-red-400":"text-white"}`,children:[new Date(e.expires_at).toLocaleDateString()," at"," ",new Date(e.expires_at).toLocaleTimeString()]}),!n&&(0,i.jsxs)("span",{className:"ml-2 text-gray-400",children:["(",tj(new Date(e.expires_at),{addSuffix:!0}),")"]})]})]}),(0,i.jsx)("div",{className:"flex items-center justify-end pt-2 border-t border-gray-700",children:"revoked"!==e.status&&(0,i.jsxs)(e5.$,{variant:"destructive",size:"sm",onClick:()=>t(e.id),className:"text-xs",children:[(0,i.jsx)(to,{className:"h-3 w-3 mr-1"}),"Revoke"]})})]})]})}function tk(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var t_=r(98599),tE=globalThis?.document?o.useLayoutEffect:()=>{},tC=l[" useId ".trim().toString()]||(()=>void 0),tA=0;function tS(e){let[t,r]=o.useState(tC());return tE(()=>{e||r(e=>e??String(tA++))},[e]),e||(t?`radix-${t}`:"")}var tR=l[" useInsertionEffect ".trim().toString()]||tE,tP=(Symbol("RADIX:SYNC_STATE"),r(14163));function tT(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var tM="dismissableLayer.update",tD=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),tI=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:s,onFocusOutside:l,onInteractOutside:d,onDismiss:c,...u}=e,m=o.useContext(tD),[f,p]=o.useState(null),h=f?.ownerDocument??globalThis?.document,[,g]=o.useState({}),x=(0,t_.s)(t,e=>p(e)),y=Array.from(m.layers),[v]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),b=y.indexOf(v),w=f?y.indexOf(f):-1,j=m.layersWithOutsidePointerEventsDisabled.size>0,N=w>=b,k=function(e,t=globalThis?.document){let r=tT(e),n=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){tL("dismissableLayer.pointerDownOutside",r,s,{discrete:!0})},s={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=n,t.addEventListener("click",a.current,{once:!0})):n()}else t.removeEventListener("click",a.current);n.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...m.branches].some(e=>e.contains(t));N&&!r&&(s?.(e),d?.(e),e.defaultPrevented||c?.())},h),_=function(e,t=globalThis?.document){let r=tT(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&tL("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...m.branches].some(e=>e.contains(t))&&(l?.(e),d?.(e),e.defaultPrevented||c?.())},h);return!function(e,t=globalThis?.document){let r=tT(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{w===m.layers.size-1&&(n?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},h),o.useEffect(()=>{if(f)return r&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(a=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(f)),m.layers.add(f),tO(),()=>{r&&1===m.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=a)}},[f,h,r,m]),o.useEffect(()=>()=>{f&&(m.layers.delete(f),m.layersWithOutsidePointerEventsDisabled.delete(f),tO())},[f,m]),o.useEffect(()=>{let e=()=>g({});return document.addEventListener(tM,e),()=>document.removeEventListener(tM,e)},[]),(0,i.jsx)(tP.sG.div,{...u,ref:x,style:{pointerEvents:j?N?"auto":"none":void 0,...e.style},onFocusCapture:tk(e.onFocusCapture,_.onFocusCapture),onBlurCapture:tk(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:tk(e.onPointerDownCapture,k.onPointerDownCapture)})});function tO(){let e=new CustomEvent(tM);document.dispatchEvent(e)}function tL(e,t,r,{discrete:n}){let a=r.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),n?(0,tP.hO)(a,s):a.dispatchEvent(s)}tI.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(tD),n=o.useRef(null),a=(0,t_.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,i.jsx)(tP.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch";var t$="focusScope.autoFocusOnMount",tF="focusScope.autoFocusOnUnmount",tW={bubbles:!1,cancelable:!0},tz=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:a,onUnmountAutoFocus:s,...l}=e,[d,c]=o.useState(null),u=tT(a),m=tT(s),f=o.useRef(null),p=(0,t_.s)(t,e=>c(e)),h=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(n){let e=function(e){if(h.paused||!d)return;let t=e.target;d.contains(t)?f.current=t:tq(f.current,{select:!0})},t=function(e){if(h.paused||!d)return;let t=e.relatedTarget;null!==t&&(d.contains(t)||tq(f.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&tq(d)});return d&&r.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,d,h.paused]),o.useEffect(()=>{if(d){tH.add(h);let e=document.activeElement;if(!d.contains(e)){let t=new CustomEvent(t$,tW);d.addEventListener(t$,u),d.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(tq(n,{select:t}),document.activeElement!==r)return}(tK(d).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&tq(d))}return()=>{d.removeEventListener(t$,u),setTimeout(()=>{let t=new CustomEvent(tF,tW);d.addEventListener(tF,m),d.dispatchEvent(t),t.defaultPrevented||tq(e??document.body,{select:!0}),d.removeEventListener(tF,m),tH.remove(h)},0)}}},[d,u,m,h]);let g=o.useCallback(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){let t=e.currentTarget,[n,s]=function(e){let t=tK(e);return[tB(t,e),tB(t.reverse(),e)]}(t);n&&s?e.shiftKey||a!==s?e.shiftKey&&a===n&&(e.preventDefault(),r&&tq(s,{select:!0})):(e.preventDefault(),r&&tq(n,{select:!0})):a===t&&e.preventDefault()}},[r,n,h.paused]);return(0,i.jsx)(tP.sG.div,{tabIndex:-1,...l,ref:p,onKeyDown:g})});function tK(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function tB(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function tq(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}tz.displayName="FocusScope";var tH=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=tU(e,t)).unshift(t)},remove(t){e=tU(e,t),e[0]?.resume()}}}();function tU(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var tV=r(51215),tG=o.forwardRef((e,t)=>{let{container:r,...n}=e,[a,s]=o.useState(!1);tE(()=>s(!0),[]);let l=r||a&&globalThis?.document?.body;return l?tV.createPortal((0,i.jsx)(tP.sG.div,{...n,ref:t}),l):null});tG.displayName="Portal";var tX=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,a]=o.useState(),s=o.useRef(null),i=o.useRef(e),l=o.useRef("none"),[d,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>r[e][t]??e,t));return o.useEffect(()=>{let e=tY(s.current);l.current="mounted"===d?e:"none"},[d]),tE(()=>{let t=s.current,r=i.current;if(r!==e){let n=l.current,a=tY(t);e?c("MOUNT"):"none"===a||t?.display==="none"?c("UNMOUNT"):r&&n!==a?c("ANIMATION_OUT"):c("UNMOUNT"),i.current=e}},[e,c]),tE(()=>{if(n){let e,t=n.ownerDocument.defaultView??window,r=r=>{let a=tY(s.current).includes(r.animationName);if(r.target===n&&a&&(c("ANIMATION_END"),!i.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},a=e=>{e.target===n&&(l.current=tY(s.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:o.useCallback(e=>{s.current=e?getComputedStyle(e):null,a(e)},[])}}(t),a="function"==typeof r?r({present:n.isPresent}):o.Children.only(r),s=(0,t_.s)(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||n.isPresent?o.cloneElement(a,{ref:s}):null};function tY(e){return e?.animationName||"none"}tX.displayName="Presence";var tJ=0;function tZ(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var tQ=function(){return(tQ=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var a in t=arguments[r])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function t0(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r}Object.create;Object.create;var t1=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),t2="width-before-scroll-bar";function t5(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var t4="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,t3=new WeakMap;function t6(e){return e}var t9=function(e){void 0===e&&(e={});var t,r,n,a,s=(t=null,void 0===r&&(r=t6),n=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,a);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){a=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var s=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(s)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return s.options=tQ({async:!0,ssr:!1},e),s}(),t8=function(){},t7=o.forwardRef(function(e,t){var r,n,a,s,i=o.useRef(null),l=o.useState({onScrollCapture:t8,onWheelCapture:t8,onTouchMoveCapture:t8}),d=l[0],c=l[1],u=e.forwardProps,m=e.children,f=e.className,p=e.removeScrollBar,h=e.enabled,g=e.shards,x=e.sideCar,y=e.noRelative,v=e.noIsolation,b=e.inert,w=e.allowPinchZoom,j=e.as,N=e.gapMode,k=t0(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),_=(r=[i,t],n=function(e){return r.forEach(function(t){return t5(t,e)})},(a=(0,o.useState)(function(){return{value:null,callback:n,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=n,s=a.facade,t4(function(){var e=t3.get(s);if(e){var t=new Set(e),n=new Set(r),a=s.current;t.forEach(function(e){n.has(e)||t5(e,null)}),n.forEach(function(e){t.has(e)||t5(e,a)})}t3.set(s,r)},[r]),s),E=tQ(tQ({},k),d);return o.createElement(o.Fragment,null,h&&o.createElement(x,{sideCar:t9,removeScrollBar:p,shards:g,noRelative:y,noIsolation:v,inert:b,setCallbacks:c,allowPinchZoom:!!w,lockRef:i,gapMode:N}),u?o.cloneElement(o.Children.only(m),tQ(tQ({},E),{ref:_})):o.createElement(void 0===j?"div":j,tQ({},E,{className:f,ref:_}),m))});t7.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t7.classNames={fullWidth:t2,zeroRight:t1};var re=function(e){var t=e.sideCar,r=t0(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return o.createElement(n,tQ({},r))};re.isSideCarExport=!0;var rt=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=s||r.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},rr=function(){var e=rt();return function(t,r){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},rn=function(){var e=rr();return function(t){return e(t.styles,t.dynamic),null}},ra={left:0,top:0,right:0,gap:0},rs=function(e){return parseInt(e||"",10)||0},ri=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[rs(r),rs(n),rs(a)]},ro=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return ra;var t=ri(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},rl=rn(),rd="data-scroll-locked",rc=function(e,t,r,n){var a=e.left,s=e.top,i=e.right,o=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(o,"px ").concat(n,";\n  }\n  body[").concat(rd,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(s,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(o,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(o,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(t1," {\n    right: ").concat(o,"px ").concat(n,";\n  }\n  \n  .").concat(t2," {\n    margin-right: ").concat(o,"px ").concat(n,";\n  }\n  \n  .").concat(t1," .").concat(t1," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(t2," .").concat(t2," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(rd,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(o,"px;\n  }\n")},ru=function(){var e=parseInt(document.body.getAttribute(rd)||"0",10);return isFinite(e)?e:0},rm=function(){o.useEffect(function(){return document.body.setAttribute(rd,(ru()+1).toString()),function(){var e=ru()-1;e<=0?document.body.removeAttribute(rd):document.body.setAttribute(rd,e.toString())}},[])},rf=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,a=void 0===n?"margin":n;rm();var s=o.useMemo(function(){return ro(a)},[a]);return o.createElement(rl,{styles:rc(s,!t,a,r?"":"!important")})},rp=!1;if("undefined"!=typeof window)try{var rh=Object.defineProperty({},"passive",{get:function(){return rp=!0,!0}});window.addEventListener("test",rh,rh),window.removeEventListener("test",rh,rh)}catch(e){rp=!1}var rg=!!rp&&{passive:!1},rx=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},ry=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),rv(e,n)){var a=rb(e,n);if(a[1]>a[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},rv=function(e,t){return"v"===e?rx(t,"overflowY"):rx(t,"overflowX")},rb=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rw=function(e,t,r,n,a){var s,i=(s=window.getComputedStyle(t).direction,"h"===e&&"rtl"===s?-1:1),o=i*n,l=r.target,d=t.contains(l),c=!1,u=o>0,m=0,f=0;do{if(!l)break;var p=rb(e,l),h=p[0],g=p[1]-p[2]-i*h;(h||g)&&rv(e,l)&&(m+=g,f+=h);var x=l.parentNode;l=x&&x.nodeType===Node.DOCUMENT_FRAGMENT_NODE?x.host:x}while(!d&&l!==document.body||d&&(t.contains(l)||t===l));return u&&(a&&1>Math.abs(m)||!a&&o>m)?c=!0:!u&&(a&&1>Math.abs(f)||!a&&-o>f)&&(c=!0),c},rj=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},rN=function(e){return[e.deltaX,e.deltaY]},rk=function(e){return e&&"current"in e?e.current:e},r_=0,rE=[];let rC=(n=function(e){var t=o.useRef([]),r=o.useRef([0,0]),n=o.useRef(),a=o.useState(r_++)[0],s=o.useState(rn)[0],i=o.useRef(e);o.useEffect(function(){i.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,a=0,s=t.length;a<s;a++)!n&&a in t||(n||(n=Array.prototype.slice.call(t,0,a)),n[a]=t[a]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(rk),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var a,s=rj(e),o=r.current,l="deltaX"in e?e.deltaX:o[0]-s[0],d="deltaY"in e?e.deltaY:o[1]-s[1],c=e.target,u=Math.abs(l)>Math.abs(d)?"h":"v";if("touches"in e&&"h"===u&&"range"===c.type)return!1;var m=ry(u,c);if(!m)return!0;if(m?a=u:(a="v"===u?"h":"v",m=ry(u,c)),!m)return!1;if(!n.current&&"changedTouches"in e&&(l||d)&&(n.current=a),!a)return!0;var f=n.current||a;return rw(f,t,e,"h"===f?l:d,!0)},[]),d=o.useCallback(function(e){if(rE.length&&rE[rE.length-1]===s){var r="deltaY"in e?rN(e):rj(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var a=(i.current.shards||[]).map(rk).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?l(e,a[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=o.useCallback(function(e,r,n,a){var s={name:e,delta:r,target:n,should:a,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(s),setTimeout(function(){t.current=t.current.filter(function(e){return e!==s})},1)},[]),u=o.useCallback(function(e){r.current=rj(e),n.current=void 0},[]),m=o.useCallback(function(t){c(t.type,rN(t),t.target,l(t,e.lockRef.current))},[]),f=o.useCallback(function(t){c(t.type,rj(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return rE.push(s),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:f}),document.addEventListener("wheel",d,rg),document.addEventListener("touchmove",d,rg),document.addEventListener("touchstart",u,rg),function(){rE=rE.filter(function(e){return e!==s}),document.removeEventListener("wheel",d,rg),document.removeEventListener("touchmove",d,rg),document.removeEventListener("touchstart",u,rg)}},[]);var p=e.removeScrollBar,h=e.inert;return o.createElement(o.Fragment,null,h?o.createElement(s,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,p?o.createElement(rf,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},t9.useMedium(n),re);var rA=o.forwardRef(function(e,t){return o.createElement(t7,tQ({},e,{ref:t,sideCar:rC}))});rA.classNames=t7.classNames;var rS=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},rR=new WeakMap,rP=new WeakMap,rT={},rM=0,rD=function(e){return e&&(e.host||rD(e.parentNode))},rI=function(e,t,r,n){var a=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=rD(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});rT[r]||(rT[r]=new WeakMap);var s=rT[r],i=[],o=new Set,l=new Set(a),d=function(e){!e||o.has(e)||(o.add(e),d(e.parentNode))};a.forEach(d);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(o.has(e))c(e);else try{var t=e.getAttribute(n),a=null!==t&&"false"!==t,l=(rR.get(e)||0)+1,d=(s.get(e)||0)+1;rR.set(e,l),s.set(e,d),i.push(e),1===l&&a&&rP.set(e,!0),1===d&&e.setAttribute(r,"true"),a||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),o.clear(),rM++,function(){i.forEach(function(e){var t=rR.get(e)-1,a=s.get(e)-1;rR.set(e,t),s.set(e,a),t||(rP.has(e)||e.removeAttribute(n),rP.delete(e)),a||e.removeAttribute(r)}),--rM||(rR=new WeakMap,rR=new WeakMap,rP=new WeakMap,rT={})}},rO=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),a=t||rS(e);return a?(n.push.apply(n,Array.from(a.querySelectorAll("[aria-live], script"))),rI(n,a,r,"aria-hidden")):function(){return null}},rL=r(8730),r$="Dialog",[rF,rW]=function(e,t=[]){let r=[],n=()=>{let t=r.map(e=>o.createContext(e));return function(r){let n=r?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let a=o.createContext(n),s=r.length;r=[...r,n];let l=t=>{let{scope:r,children:n,...l}=t,d=r?.[e]?.[s]||a,c=o.useMemo(()=>l,Object.values(l));return(0,i.jsx)(d.Provider,{value:c,children:n})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[s]||a,d=o.useContext(l);if(d)return d;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let a=r(e)[`__scope${n}`];return{...t,...a}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}(r$),[rz,rK]=rF(r$),rB=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:s,modal:l=!0}=e,d=o.useRef(null),c=o.useRef(null),[u,m]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,s,i]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),a=o.useRef(r),s=o.useRef(t);return tR(()=>{s.current=t},[t]),o.useEffect(()=>{a.current!==r&&(s.current?.(r),a.current=r)},[r,a]),[r,n,s]}({defaultProp:t,onChange:r}),l=void 0!==e,d=l?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,n])}return[d,o.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&i.current?.(r)}else s(t)},[l,e,s,i])]}({prop:n,defaultProp:a??!1,onChange:s,caller:r$});return(0,i.jsx)(rz,{scope:t,triggerRef:d,contentRef:c,contentId:tS(),titleId:tS(),descriptionId:tS(),open:u,onOpenChange:m,onOpenToggle:o.useCallback(()=>m(e=>!e),[m]),modal:l,children:r})};rB.displayName=r$;var rq="DialogTrigger";o.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=rK(rq,r),s=(0,t_.s)(t,a.triggerRef);return(0,i.jsx)(tP.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":ne(a.open),...n,ref:s,onClick:tk(e.onClick,a.onOpenToggle)})}).displayName=rq;var rH="DialogPortal",[rU,rV]=rF(rH,{forceMount:void 0}),rG=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,s=rK(rH,t);return(0,i.jsx)(rU,{scope:t,forceMount:r,children:o.Children.map(n,e=>(0,i.jsx)(tX,{present:r||s.open,children:(0,i.jsx)(tG,{asChild:!0,container:a,children:e})}))})};rG.displayName=rH;var rX="DialogOverlay",rY=o.forwardRef((e,t)=>{let r=rV(rX,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,s=rK(rX,e.__scopeDialog);return s.modal?(0,i.jsx)(tX,{present:n||s.open,children:(0,i.jsx)(rZ,{...a,ref:t})}):null});rY.displayName=rX;var rJ=(0,rL.TL)("DialogOverlay.RemoveScroll"),rZ=o.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=rK(rX,r);return(0,i.jsx)(rA,{as:rJ,allowPinchZoom:!0,shards:[a.contentRef],children:(0,i.jsx)(tP.sG.div,{"data-state":ne(a.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),rQ="DialogContent",r0=o.forwardRef((e,t)=>{let r=rV(rQ,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,s=rK(rQ,e.__scopeDialog);return(0,i.jsx)(tX,{present:n||s.open,children:s.modal?(0,i.jsx)(r1,{...a,ref:t}):(0,i.jsx)(r2,{...a,ref:t})})});r0.displayName=rQ;var r1=o.forwardRef((e,t)=>{let r=rK(rQ,e.__scopeDialog),n=o.useRef(null),a=(0,t_.s)(t,r.contentRef,n);return o.useEffect(()=>{let e=n.current;if(e)return rO(e)},[]),(0,i.jsx)(r5,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:tk(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:tk(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:tk(e.onFocusOutside,e=>e.preventDefault())})}),r2=o.forwardRef((e,t)=>{let r=rK(rQ,e.__scopeDialog),n=o.useRef(!1),a=o.useRef(!1);return(0,i.jsx)(r5,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),r5=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,d=rK(rQ,r),c=o.useRef(null),u=(0,t_.s)(t,c);return o.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??tZ()),document.body.insertAdjacentElement("beforeend",e[1]??tZ()),tJ++,()=>{1===tJ&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),tJ--}},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(tz,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,i.jsx)(tI,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":ne(d.open),...l,ref:u,onDismiss:()=>d.onOpenChange(!1)})}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(na,{titleId:d.titleId}),(0,i.jsx)(ns,{contentRef:c,descriptionId:d.descriptionId})]})]})}),r4="DialogTitle",r3=o.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=rK(r4,r);return(0,i.jsx)(tP.sG.h2,{id:a.titleId,...n,ref:t})});r3.displayName=r4;var r6="DialogDescription",r9=o.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=rK(r6,r);return(0,i.jsx)(tP.sG.p,{id:a.descriptionId,...n,ref:t})});r9.displayName=r6;var r8="DialogClose",r7=o.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=rK(r8,r);return(0,i.jsx)(tP.sG.button,{type:"button",...n,ref:t,onClick:tk(e.onClick,()=>a.onOpenChange(!1))})});function ne(e){return e?"open":"closed"}r7.displayName=r8;var nt="DialogTitleWarning",[nr,nn]=function(e,t){let r=o.createContext(t),n=e=>{let{children:t,...n}=e,a=o.useMemo(()=>n,Object.values(n));return(0,i.jsx)(r.Provider,{value:a,children:t})};return n.displayName=e+"Provider",[n,function(n){let a=o.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}(nt,{contentName:rQ,titleName:r4,docsSlug:"dialog"}),na=({titleId:e})=>{let t=nn(nt),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&document.getElementById(e)},[r,e]),null},ns=({contentRef:e,descriptionId:t})=>{let r=nn("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return o.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&document.getElementById(t)},[n,e,t]),null},ni=r(11860);let no=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(rY,{ref:r,className:`fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ${e||""}`,...t}));no.displayName=rY.displayName;let nl=o.forwardRef(({className:e,children:t,...r},n)=>(0,i.jsxs)(rG,{children:[(0,i.jsx)(no,{}),(0,i.jsxs)(r0,{ref:n,className:`fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg ${e||""}`,...r,children:[t,(0,i.jsxs)(r7,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,i.jsx)(ni.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));nl.displayName=r0.displayName;let nd=({className:e,...t})=>(0,i.jsx)("div",{className:`flex flex-col space-y-1.5 text-center sm:text-left ${e||""}`,...t});nd.displayName="DialogHeader";let nc=({className:e,...t})=>(0,i.jsx)("div",{className:`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 ${e||""}`,...t});nc.displayName="DialogFooter";let nu=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(r3,{ref:r,className:`text-lg font-semibold leading-none tracking-tight ${e||""}`,...t}));nu.displayName=r3.displayName;let nm=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(r9,{ref:r,className:`text-sm text-gray-600 ${e||""}`,...t}));nm.displayName=r9.displayName;let nf=(0,o.forwardRef)(({className:e="",label:t,error:r,helperText:n,icon:a,iconPosition:s="left",id:o,...l},d)=>{let c=o||`input-${Math.random().toString(36).substr(2,9)}`;return(0,i.jsxs)("div",{className:"space-y-2",children:[t&&(0,i.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-300",children:t}),(0,i.jsxs)("div",{className:"relative",children:[a&&"left"===s&&(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)("div",{className:"h-5 w-5 text-gray-400",children:a})}),(0,i.jsx)("input",{ref:d,id:c,className:`
              w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 
              focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
              disabled:opacity-50 disabled:cursor-not-allowed
              transition-all duration-200
              ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
              ${a&&"left"===s?"pl-10":""}
              ${a&&"right"===s?"pr-10":""}
              ${e}
            `,...l}),a&&"right"===s&&(0,i.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,i.jsx)("div",{className:"h-5 w-5 text-gray-400",children:a})})]}),r&&(0,i.jsx)("p",{className:"text-sm text-red-400",children:r}),n&&!r&&(0,i.jsx)("p",{className:"text-sm text-gray-500",children:n})]})});nf.displayName="Input",(0,o.forwardRef)(({className:e="",label:t,error:r,helperText:n,id:a,...s},o)=>{let l=a||`textarea-${Math.random().toString(36).substr(2,9)}`;return(0,i.jsxs)("div",{className:"space-y-2",children:[t&&(0,i.jsx)("label",{htmlFor:l,className:"block text-sm font-medium text-gray-300",children:t}),(0,i.jsx)("textarea",{ref:o,id:l,className:`
            w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 
            focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-200 resize-none
            ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
            ${e}
          `,...s}),r&&(0,i.jsx)("p",{className:"text-sm text-red-400",children:r}),n&&!r&&(0,i.jsx)("p",{className:"text-sm text-gray-500",children:n})]})}).displayName="Textarea",(0,o.forwardRef)(({className:e="",label:t,error:r,helperText:n,options:a=[],children:s,id:o,...l},d)=>{let c=o||`select-${Math.random().toString(36).substr(2,9)}`;return(0,i.jsxs)("div",{className:"space-y-2",children:[t&&(0,i.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-300",children:t}),(0,i.jsxs)("select",{ref:d,id:c,className:`
            w-full p-3 bg-white/5 border rounded-xl text-white 
            focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-200
            ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
            ${e}
          `,...l,children:[a.map(e=>(0,i.jsx)("option",{value:e.value,disabled:e.disabled,className:"bg-gray-800 text-white",children:e.label},e.value)),s]}),r&&(0,i.jsx)("p",{className:"text-sm text-red-400",children:r}),n&&!r&&(0,i.jsx)("p",{className:"text-sm text-gray-500",children:n})]})}).displayName="Select";var np=r(54300);let nh=(0,e7.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-blue-50 text-blue-900 border-blue-200",destructive:"bg-red-50 text-red-900 border-red-200 [&>svg]:text-red-600"}},defaultVariants:{variant:"default"}}),ng=o.forwardRef(({className:e,variant:t,...r},n)=>(0,i.jsx)("div",{ref:n,role:"alert",className:`${nh({variant:t})} ${e||""}`,...r}));ng.displayName="Alert",o.forwardRef(({className:e,...t},r)=>(0,i.jsx)("h5",{ref:r,className:`mb-1 font-medium leading-none tracking-tight ${e||""}`,...t})).displayName="AlertTitle";let nx=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:`text-sm [&_p]:leading-relaxed ${e||""}`,...t}));nx.displayName="AlertDescription";let ny=(0,e4.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),nv=(0,e4.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),nb=(0,e4.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function nw({open:e,onOpenChange:t,onCreateApiKey:r,configName:n,creating:a,subscriptionTier:s}){let[l,d]=(0,o.useState)("form"),[c,u]=(0,o.useState)(null),[m,f]=(0,o.useState)(!0),[p,h]=(0,o.useState)(!1),[g,x]=(0,o.useState)({key_name:"",expires_at:""}),y=async e=>{if(e.preventDefault(),!g.key_name.trim())return void e8.oR.error("Please enter a name for your API key");try{let e=await r({key_name:g.key_name.trim(),expires_at:g.expires_at||void 0});u(e),d("success")}catch(e){}},v=async()=>{if(c?.api_key)try{await navigator.clipboard.writeText(c.api_key),h(!0),e8.oR.success("API key copied to clipboard"),setTimeout(()=>h(!1),2e3)}catch(e){e8.oR.error("Failed to copy API key")}},b=()=>{"form"===l&&(d("form"),u(null),f(!0),x({key_name:"",expires_at:""}),t(!1))};return"success"===l&&c?(0,i.jsx)(rB,{open:e,onOpenChange:()=>{},modal:!0,children:(0,i.jsxs)(nl,{className:"max-w-lg",children:[(0,i.jsxs)(nd,{className:"text-center space-y-3",children:[(0,i.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(e6,{className:"h-8 w-8 text-green-600"})}),(0,i.jsx)(nu,{className:"text-2xl font-bold text-gray-900",children:"API Key Created Successfully!"}),(0,i.jsx)(nm,{className:"text-gray-600",children:"Save your API key now - this is the only time you'll see it in full."})]}),(0,i.jsxs)("div",{className:"space-y-6 py-4",children:[(0,i.jsxs)(ng,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(ny,{className:"h-4 w-4 text-red-600"}),(0,i.jsxs)(nx,{className:"text-red-800 font-medium",children:[(0,i.jsx)("strong",{children:"Important:"})," This is the only time you'll see the full API key. Make sure to copy and store it securely."]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(np.J,{className:"text-sm font-medium text-gray-700",children:"Your API Key"}),(0,i.jsx)("div",{className:"relative",children:(0,i.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,i.jsx)("code",{className:"flex-1 text-sm font-mono text-gray-900 break-all select-all",children:m?c.api_key:`${c.key_prefix}_${"*".repeat(28)}${c.api_key.slice(-4)}`}),(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(e5.$,{variant:"ghost",size:"sm",onClick:()=>f(!m),className:"h-8 w-8 p-0",title:m?"Hide key":"Show key",children:m?(0,i.jsx)(nv,{className:"h-4 w-4"}):(0,i.jsx)(nb,{className:"h-4 w-4"})}),(0,i.jsx)(e5.$,{variant:"ghost",size:"sm",onClick:v,className:`h-8 w-8 p-0 ${p?"text-green-600":""}`,title:"Copy to clipboard",children:p?(0,i.jsx)("span",{className:"text-xs",children:"✓"}):(0,i.jsx)(tr,{className:"h-4 w-4"})})]})]})}),(0,i.jsx)(e5.$,{onClick:v,variant:"outline",className:"w-full",disabled:p,children:p?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{className:"text-green-600 mr-2",children:"✓"}),"Copied!"]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(tr,{className:"h-4 w-4 mr-2"}),"Copy API Key"]})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(np.J,{className:"text-gray-600",children:"Key Name"}),(0,i.jsx)("p",{className:"font-medium text-gray-900",children:c.key_name})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(np.J,{className:"text-gray-600",children:"Created"}),(0,i.jsx)("p",{className:"font-medium text-gray-900",children:new Date(c.created_at).toLocaleString()})]})]})]}),(0,i.jsx)(nc,{className:"pt-6",children:(0,i.jsx)(e5.$,{onClick:()=>{d("form"),u(null),f(!0),h(!1),x({key_name:"",expires_at:""}),t(!1)},className:"w-full",children:"I've Saved My API Key"})})]})}):(0,i.jsx)(rB,{open:e,onOpenChange:b,children:(0,i.jsxs)(nl,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(nd,{children:[(0,i.jsxs)(nu,{className:"flex items-center gap-2",children:[(0,i.jsx)(e6,{className:"h-5 w-5"}),"Create API Key"]}),(0,i.jsxs)(nm,{children:["Create a new API key for programmatic access to ",n]})]}),(0,i.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,i.jsx)("div",{className:"space-y-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(np.J,{htmlFor:"key_name",children:"API Key Name *"}),(0,i.jsx)(nf,{id:"key_name",value:g.key_name,onChange:e=>x(t=>({...t,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0,className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,i.jsx)("p",{className:"text-xs text-gray-600",children:"A descriptive name to help you identify this API key"})]})}),(0,i.jsx)("div",{className:"space-y-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(np.J,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,i.jsx)(nf,{id:"expires_at",type:"datetime-local",value:g.expires_at,onChange:e=>x(t=>({...t,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16),className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,i.jsx)("p",{className:"text-xs text-gray-600",children:"Leave empty for no expiration"})]})}),(0,i.jsxs)(nc,{children:[(0,i.jsx)(e5.$,{type:"button",variant:"outline",onClick:b,children:"Cancel"}),(0,i.jsx)(e5.$,{type:"submit",disabled:a,children:a?"Creating...":"Create API Key"})]})]})]})})}var nj=r(4847);function nN({configId:e,configName:t}){let[r,n]=(0,o.useState)([]),[a,s]=(0,o.useState)(!0),[l,d]=(0,o.useState)(!1),[c,u]=(0,o.useState)(!1),[m,f]=(0,o.useState)(null),p=(0,eJ.Z)(),h=async()=>{try{s(!0);let t=await fetch(`/api/user-api-keys?config_id=${e}`);if(!t.ok)throw Error("Failed to fetch API keys");let r=await t.json();n(r.api_keys||[])}catch(e){e8.oR.error("Failed to load API keys")}finally{s(!1)}},g=async e=>{try{let t=await fetch("/api/user/subscription-tier"),n=t.ok?await t.json():null,a=n?.tier||"starter",s={free:3,starter:50,professional:999999,enterprise:999999},i=void 0!==e?e:r.length;f({tier:a,keyLimit:s[a]||s.free,currentCount:i})}catch(t){f({tier:"free",keyLimit:3,currentCount:void 0!==e?e:r.length})}},x=async r=>{try{d(!0);let a=await fetch("/api/user-api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...r,custom_api_config_id:e})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to create API key")}let s=await a.json();e8.oR.success("API key created successfully!");let i={...s,custom_api_configs:{id:e,name:t}};return n(e=>{let t=[i,...e];return g(t.length),t}),await h(),s}catch(e){throw e8.oR.error(e.message||"Failed to create API key"),e}finally{d(!1)}},y=async e=>{let t=r.find(t=>t.id===e),a=t?.key_name||"this API key";p.showConfirmation({title:"Revoke API Key",message:`Are you sure you want to revoke "${a}"? This action cannot be undone and will immediately disable the key.`,confirmText:"Revoke Key",cancelText:"Cancel",type:"danger"},async()=>{try{let t=await fetch(`/api/user-api-keys/${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to revoke API key")}n(t=>t.map(t=>t.id===e?{...t,status:"revoked"}:t)),e8.oR.success("API key revoked successfully")}catch(e){throw e8.oR.error(e.message||"Failed to revoke API key"),e}})},v=!m||m.currentCount<m.keyLimit;return a?(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,i.jsx)(e3,{className:"h-6 w-6 animate-spin mr-2 text-gray-400"}),(0,i.jsx)("span",{className:"text-gray-400",children:"Loading API keys..."})]})}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2 text-white",children:[(0,i.jsx)(e6,{className:"h-6 w-6"}),"API Keys"]}),(0,i.jsxs)("p",{className:"text-gray-400 mt-1",children:["Generate API keys for programmatic access to ",t]})]}),v?(0,i.jsxs)(e5.$,{onClick:()=>u(!0),className:"flex items-center gap-2",children:[(0,i.jsx)(e9,{className:"h-4 w-4"}),"Create API Key"]}):(0,i.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,i.jsxs)(e5.$,{disabled:!0,className:"flex items-center gap-2 opacity-50",children:[(0,i.jsx)(e9,{className:"h-4 w-4"}),"Create API Key"]}),(0,i.jsx)("p",{className:"text-xs text-orange-400 font-medium",children:m?.tier==="free"?"Upgrade to Starter plan for more API keys":"API key limit reached - upgrade for unlimited keys"})]})]}),m&&(0,i.jsx)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-2 mb-4",children:(0,i.jsx)(nj.Jg,{current:m.currentCount,limit:m.keyLimit,label:"User-Generated API Keys",tier:m.tier,showUpgradeHint:!0})}),0===r.length?(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg text-center py-8",children:(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)(e6,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2 text-white",children:"No API Keys"}),(0,i.jsx)("p",{className:"text-gray-400 mb-4",children:"Create your first API key to start using the RouKey API programmatically."}),v?(0,i.jsxs)(e5.$,{onClick:()=>u(!0),children:[(0,i.jsx)(e9,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}):(0,i.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,i.jsxs)(e5.$,{disabled:!0,className:"opacity-50",children:[(0,i.jsx)(e9,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}),(0,i.jsx)("p",{className:"text-xs text-orange-400 font-medium",children:m?.tier==="free"?"Upgrade to Starter plan to create API keys":"API key limit reached - upgrade for unlimited keys"})]})]})}):(0,i.jsx)("div",{className:"grid gap-4",children:r.map(e=>(0,i.jsx)(tN,{apiKey:e,onRevoke:y},e.id))}),(0,i.jsx)(nw,{open:c,onOpenChange:e=>{u(e)},onCreateApiKey:x,configName:t,creating:l,subscriptionTier:m?.tier||"starter"}),(0,i.jsx)(eY.A,{isOpen:p.isOpen,onClose:p.hideConfirmation,onConfirm:p.onConfirm,title:p.title,message:p.message,confirmText:p.confirmText,cancelText:p.cancelText,type:p.type,isLoading:p.isLoading})]})}let nk=c.MG.map(e=>({value:e.id,label:e.name}));function n_(){let e=(0,d.useParams)().configId,t=(0,eJ.Z)(),r=(0,e2.bu)(),n=r?.navigateOptimistically||(e=>{window.location.href=e}),{getCachedData:a,isCached:s,clearCache:l}=(0,eZ._)(),{createHoverPrefetch:_}=(0,e1.c)(),[E,C]=(0,o.useState)(null),[A,S]=(0,o.useState)(!0),[R,P]=(0,o.useState)(!1),[T,M]=(0,o.useState)(nk[0]?.value||"openai"),[D,I]=(0,o.useState)(""),[O,L]=(0,o.useState)(""),[$,F]=(0,o.useState)(""),[W,z]=(0,o.useState)(1),[K,B]=(0,o.useState)(!1),[q,H]=(0,o.useState)(null),[U,V]=(0,o.useState)(null),[G,X]=(0,o.useState)(null),[Y,J]=(0,o.useState)(!1),[Z,Q]=(0,o.useState)(null),[ee,et]=(0,o.useState)([]),[er,en]=(0,o.useState)(!0),[ea,es]=(0,o.useState)(null),[ei,eo]=(0,o.useState)(null),[el,ed]=(0,o.useState)(null),[ec,eu]=(0,o.useState)(null),[em,ef]=(0,o.useState)(1),[ep,eh]=(0,o.useState)(""),[eg,ex]=(0,o.useState)(!1),[ey,ev]=(0,o.useState)([]),[eb,ew]=(0,o.useState)(!1),[ej,eN]=(0,o.useState)(null),[ek,e_]=(0,o.useState)(!1),[eE,eC]=(0,o.useState)(""),[eA,eS]=(0,o.useState)(""),[eR,eP]=(0,o.useState)(""),[eT,eM]=(0,o.useState)(!1),[eD,eI]=(0,o.useState)(null),[eO,eL]=(0,o.useState)(null),[e$,eF]=(0,o.useState)("provider-keys");(0,o.useCallback)(async()=>{if(!e)return;let t=a(e);if(t&&t.configDetails){C(t.configDetails),S(!1);return}s(e)||P(!0),S(!0),H(null);try{let t=await fetch("/api/custom-configs");if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch configurations list")}let r=(await t.json()).find(t=>t.id===e);if(!r)throw Error("Configuration not found in the list.");C(r)}catch(e){H(`Error loading model configuration: ${e.message}`),C(null)}finally{S(!1),P(!1)}},[e,a,s]),(0,o.useCallback)(async()=>{let t=a(e);if(t&&t.models){X(t.models),J(!1);return}J(!0),Q(null),X(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?X(t.models):X([])}catch(e){Q(`Error fetching models: ${e.message}`),X([])}finally{J(!1)}},[e,a]),(0,o.useCallback)(async()=>{let t=a(e);if(t&&t.userCustomRoles){ev(t.userCustomRoles),ew(!1);return}ew(!0),eN(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let t=await e.json();ev(t)}else{let t;try{t=await e.json()}catch(r){t={error:await e.text().catch(()=>`HTTP error ${e.status}`)}}let r=t.error||(t.issues?JSON.stringify(t.issues):`Failed to fetch custom roles (status: ${e.status})`);if(401===e.status)eN(r);else throw Error(r);ev([])}}catch(e){eN(e.message),ev([])}finally{ew(!1)}},[]);let eW=(0,o.useCallback)(async()=>{if(!e||!ey)return;let t=a(e);if(t&&t.apiKeys&&void 0!==t.defaultChatKeyId){let e=t.apiKeys.map(async e=>{let r=await fetch(`/api/keys/${e.id}/roles`),n=[];return r.ok&&(n=(await r.json()).map(e=>{let t=(0,u.Dc)(e.role_name);if(t)return t;let r=ey.find(t=>t.role_id===e.role_name);return r?{id:r.role_id,name:r.name,description:r.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:n,is_default_general_chat_model:t.defaultChatKeyId===e.id}});et(await Promise.all(e)),eo(t.defaultChatKeyId),en(!1);return}en(!0),H(e=>e&&e.startsWith("Error loading model configuration:")?e:null),V(null);try{let t=await fetch(`/api/keys?custom_config_id=${e}`);if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch API keys")}let r=await t.json(),n=await fetch(`/api/custom-configs/${e}/default-chat-key`);n.ok;let a=200===n.status?await n.json():null;eo(a?.id||null);let s=r.map(async e=>{let t=await fetch(`/api/keys/${e.id}/roles`),r=[];return t.ok&&(r=(await t.json()).map(e=>{let t=(0,u.Dc)(e.role_name);if(t)return t;let r=ey.find(t=>t.role_id===e.role_name);return r?{id:r.role_id,name:r.name,description:r.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:r,is_default_general_chat_model:a?.id===e.id}}),i=await Promise.all(s);et(i)}catch(e){H(t=>t?`${t}; ${e.message}`:e.message)}finally{en(!1)}},[e,ey]),ez=(0,o.useMemo)(()=>{if(G){let e=c.MG.find(e=>e.id===T);if(!e)return[];if("openrouter"===e.id)return G.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return G.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),G.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return G.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[G,T]),eK=(0,o.useMemo)(()=>{if(G&&ec){let e=c.MG.find(e=>e.id===ec.provider);if(!e)return[];if("openrouter"===e.id)return G.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return G.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),G.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return G.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[G,ec]),eB=async t=>{if(t.preventDefault(),!e)return void H("Configuration ID is missing.");if(ee.some(e=>e.predefined_model_id===D))return void H("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");B(!0),H(null),V(null);let r=[...ee];try{let t=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:e,provider:T,predefined_model_id:D,api_key_raw:O,label:$,temperature:W})}),r=await t.json();if(!t.ok)throw Error(r.details||r.error||"Failed to save API key");let n={id:r.id,custom_api_config_id:e,provider:T,predefined_model_id:D,label:$,temperature:W,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};et(e=>[...e,n]),l(e),V(`API key "${$}" saved successfully!`),M(nk[0]?.value||"openai"),L(""),F(""),z(1),ez.length>0&&I(ez[0].value)}catch(e){et(r),H(`Save Key Error: ${e.message}`)}finally{B(!1)}},eq=e=>{eu(e),ef(e.temperature||1),eh(e.predefined_model_id)},eH=async()=>{if(!ec)return;if(ee.some(e=>e.id!==ec.id&&e.predefined_model_id===ep))return void H("This model is already configured in this setup. Each model can only be used once per configuration.");ex(!0),H(null),V(null);let t=[...ee];et(e=>e.map(e=>e.id===ec.id?{...e,temperature:em,predefined_model_id:ep}:e));try{let r=await fetch(`/api/keys?id=${ec.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:em,predefined_model_id:ep})}),n=await r.json();if(!r.ok)throw et(t),Error(n.details||n.error||"Failed to update API key");l(e),V(`API key "${ec.label}" updated successfully!`),eu(null)}catch(e){H(`Update Key Error: ${e.message}`)}finally{ex(!1)}},eU=(r,n)=>{t.showConfirmation({title:"Delete API Key",message:`Are you sure you want to delete the API key "${n}"? This will permanently remove the key and unassign all its roles. This action cannot be undone.`,confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{es(r),H(null),V(null);let t=[...ee],a=ee.find(e=>e.id===r);et(e=>e.filter(e=>e.id!==r)),a?.is_default_general_chat_model&&eo(null);try{let a=await fetch(`/api/keys/${r}`,{method:"DELETE"}),s=await a.json();if(!a.ok){if(et(t),eo(ei),404===a.status){et(e=>e.filter(e=>e.id!==r)),V(`API key "${n}" was already deleted.`);return}throw Error(s.details||s.error||"Failed to delete API key")}l(e),V(`API key "${n}" deleted successfully!`)}catch(e){throw H(`Delete Key Error: ${e.message}`),e}finally{es(null)}})},eV=async t=>{if(!e)return;H(null),V(null);let r=[...ee];et(e=>e.map(e=>({...e,is_default_general_chat_model:e.id===t}))),eo(t);try{let n=await fetch(`/api/custom-configs/${e}/default-key-handler/${t}`,{method:"PUT"}),a=await n.json();if(!n.ok)throw et(r.map(e=>({...e}))),eo(ei),Error(a.details||a.error||"Failed to set default chat key");l(e),V(a.message||"Default general chat key updated!")}catch(e){H(`Set Default Error: ${e.message}`)}},eG=async(e,t,r)=>{H(null),V(null);let n=`/api/keys/${e.id}/roles`,a=[...u.p2.map(e=>({...e,isCustom:!1})),...ey.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===t)||{id:t,name:t,description:""},s=ee.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),i=null;el&&el.id===e.id&&(i={...el,assigned_roles:[...el.assigned_roles.map(e=>({...e}))]}),et(n=>n.map(n=>{if(n.id===e.id){let e=r?n.assigned_roles.filter(e=>e.id!==t):[...n.assigned_roles,a];return{...n,assigned_roles:e}}return n})),el&&el.id===e.id&&ed(e=>{if(!e)return null;let n=r?e.assigned_roles.filter(e=>e.id!==t):[...e.assigned_roles,a];return{...e,assigned_roles:n}});try{let o;o=r?await fetch(`${n}/${t}`,{method:"DELETE"}):await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:t})});let l=await o.json();if(!o.ok){if(et(s),i)ed(i);else if(el&&el.id===e.id){let t=s.find(t=>t.id===e.id);t&&ed(t)}let t=409===o.status&&l.error?l.error:l.details||l.error||(r?"Failed to unassign role":"Failed to assign role");throw Error(t)}V(l.message||`Role '${a.name}' ${r?"unassigned":"assigned"} successfully.`)}catch(e){H(`Role Update Error: ${e.message}`)}},e5=async()=>{if(!eE.trim()||eE.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eE.trim()))return void eI("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(u.p2.some(e=>e.id.toLowerCase()===eE.trim().toLowerCase())||ey.some(e=>e.role_id.toLowerCase()===eE.trim().toLowerCase()))return void eI("This Role ID is already in use (either predefined or as one of your custom roles).");if(!eA.trim())return void eI("Role Name is required.");eI(null),eM(!0);try{let t=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eE.trim(),name:eA.trim(),description:eR.trim()})});if(!t.ok){let e;try{e=await t.json()}catch(n){let r=await t.text().catch(()=>`HTTP status ${t.status}`);e={error:"Server error, could not parse response.",details:r}}let r=e.error||"Failed to create custom role.";if(e.details)r+=` (Details: ${e.details})`;else if(e.issues){let t=Object.entries(e.issues).map(([e,t])=>`${e}: ${t.join(", ")}`).join("; ");r+=` (Issues: ${t})`}throw Error(r)}let r=await t.json();eC(""),eS(""),eP(""),l(e);let n={id:r.id,role_id:r.role_id,name:r.name,description:r.description,user_id:r.user_id,created_at:r.created_at,updated_at:r.updated_at};ev(e=>[...e,n]),V(`Custom role '${r.name}' created successfully! It is now available globally.`)}catch(e){eI(e.message)}finally{eM(!1)}},e4=(r,n)=>{r&&t.showConfirmation({title:"Delete Custom Role",message:`Are you sure you want to delete the custom role "${n}"? This will unassign it from all API keys where it's currently used. This action cannot be undone.`,confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eL(r),eN(null),eI(null),V(null);try{let t=await fetch(`/api/user/custom-roles/${r}`,{method:"DELETE"}),a=await t.json();if(!t.ok)throw Error(a.error||"Failed to delete custom role");ev(e=>e.filter(e=>e.id!==r)),l(e),V(a.message||`Global custom role "${n}" deleted successfully.`),e&&eW()}catch(e){throw eN(`Error deleting role: ${e.message}`),e}finally{eL(null)}})};return R&&!s(e)?(0,i.jsx)(eQ,{}):A&&!E?(0,i.jsx)(e0,{}):(0,i.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,i.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsxs)("button",{onClick:()=>n("/my-models"),className:"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,i.jsx)(g.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6",children:(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,i.jsx)("div",{className:"flex-1",children:E?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,i.jsx)(p.A,{className:"h-6 w-6 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-white",children:E.name}),(0,i.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Model Configuration"})]})]}),(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit",children:[(0,i.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",E.id]})]}):q&&!A?(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4",children:(0,i.jsx)(m.A,{className:"h-6 w-6 text-red-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-red-400",children:"Configuration Error"}),(0,i.jsx)("p",{className:"text-red-300 mt-1",children:q.replace("Error loading model configuration: ","")})]})]}):(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4",children:(0,i.jsx)(x.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Loading Configuration..."}),(0,i.jsx)("p",{className:"text-gray-400 mt-1",children:"Please wait while we fetch your model details"})]})]})}),E&&(0,i.jsx)("div",{className:"flex flex-col sm:flex-row gap-3",children:(0,i.jsxs)("button",{onClick:()=>n(`/routing-setup/${e}?from=model-config`),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",..._(e),children:[(0,i.jsx)(p.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})})]})}),U&&(0,i.jsx)("div",{className:"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(y.A,{className:"h-5 w-5 text-green-400"}),(0,i.jsx)("p",{className:"text-green-300 font-medium",children:U})]})}),q&&(0,i.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(m.A,{className:"h-5 w-5 text-red-400"}),(0,i.jsx)("p",{className:"text-red-300 font-medium",children:q})]})})]}),E&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2",children:(0,i.jsxs)("div",{className:"flex space-x-1",children:[(0,i.jsx)("button",{onClick:()=>eF("provider-keys"),className:`flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${"provider-keys"===e$?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"}`,children:(0,i.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"Provider API Keys"})]})}),(0,i.jsx)("button",{onClick:()=>eF("user-api-keys"),className:`flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${"user-api-keys"===e$?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"}`,children:(0,i.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,i.jsx)(b.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"Generated API Keys"})]})})]})}),"provider-keys"===e$&&(0,i.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,i.jsx)("div",{className:"xl:col-span-2",children:(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8",children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,i.jsx)(w.A,{className:"h-5 w-5 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-white",children:"Add Provider API Key"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Configure new provider key"})]})]}),(0,i.jsxs)("form",{onSubmit:eB,className:"space-y-5",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,i.jsx)("select",{id:"provider",value:T,onChange:e=>{M(e.target.value)},className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm",children:nk.map(e=>(0,i.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key"}),(0,i.jsx)("input",{id:"apiKeyRaw",type:"password",value:O,onChange:e=>L(e.target.value),className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"Enter your API key"}),Y&&null===G&&(0,i.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,i.jsx)(x.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),Z&&(0,i.jsx)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:Z})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,i.jsx)("select",{id:"predefinedModelId",value:D,onChange:e=>I(e.target.value),disabled:!ez.length,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500",children:ez.length>0?ez.map(e=>(0,i.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value)):(0,i.jsx)("option",{value:"",disabled:!0,className:"bg-gray-800 text-gray-400",children:null===G&&Y?"Loading models...":"Select a provider first"})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,i.jsx)("input",{type:"text",id:"label",value:$,onChange:e=>F(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,i.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:W,onChange:e=>z(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,i.jsx)("div",{className:"flex items-center space-x-2",children:(0,i.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:W,onChange:e=>z(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,i.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,i.jsx)("button",{type:"submit",disabled:K||!D||""===D||!O.trim()||!$.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:K?(0,i.jsxs)("span",{className:"flex items-center justify-center",children:[(0,i.jsx)(x.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,i.jsxs)("span",{className:"flex items-center justify-center",children:[(0,i.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,i.jsx)("div",{className:"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)(j.A,{className:"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-blue-300 mb-1",children:"Key Configuration Rules"}),(0,i.jsxs)("div",{className:"text-xs text-blue-200 space-y-1",children:[(0,i.jsxs)("p",{children:["✅ ",(0,i.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,i.jsxs)("p",{children:["✅ ",(0,i.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,i.jsxs)("p",{children:["❌ ",(0,i.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,i.jsx)("div",{className:"xl:col-span-3",children:(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,i.jsx)(v.A,{className:"h-5 w-5 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-white",children:"API Keys & Roles"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Manage existing keys"})]})]}),er&&(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)(x.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm",children:"Loading API keys..."})]}),!er&&0===ee.length&&(!q||q&&q.startsWith("Error loading model configuration:"))&&(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,i.jsx)(v.A,{className:"h-6 w-6 text-gray-400"})}),(0,i.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!er&&ee.length>0&&(0,i.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:ee.map((e,t)=>(0,i.jsx)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in",style:{animationDelay:`${50*t}ms`},children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsxs)("div",{className:"flex items-center mb-2",children:[(0,i.jsx)("h3",{className:"text-sm font-semibold text-white truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,i.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0",children:[(0,i.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)("p",{className:"text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:[e.provider," (",e.predefined_model_id,")"]}),(0,i.jsxs)("p",{className:"text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50",children:["Temp: ",e.temperature]})]}),(0,i.jsx)(nj.sU,{feature:"custom_roles",fallback:(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:(0,i.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"Roles available on Starter plan+"})}),children:(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,i.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50",children:e.name},e.id)):(0,i.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"No roles"})})})]}),!e.is_default_general_chat_model&&(0,i.jsx)("button",{onClick:()=>eV(e.id),className:"text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"global-tooltip","data-tooltip-content":"Set as default chat model",children:"Set Default"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,i.jsx)("button",{onClick:()=>eq(e),disabled:ea===e.id,className:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Edit Model & Settings",children:(0,i.jsx)(k.A,{className:"h-4 w-4"})}),(0,i.jsx)(nj.sU,{feature:"custom_roles",fallback:(0,i.jsx)("button",{disabled:!0,className:"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Role management requires Starter plan or higher",children:(0,i.jsx)(p.A,{className:"h-4 w-4"})}),children:(0,i.jsx)("button",{onClick:()=>ed(e),disabled:ea===e.id,className:"p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Manage Roles",children:(0,i.jsx)(p.A,{className:"h-4 w-4"})})}),(0,i.jsx)("button",{onClick:()=>eU(e.id,e.label),disabled:ea===e.id,className:"p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Delete Key",children:ea===e.id?(0,i.jsx)(h.A,{className:"h-4 w-4 animate-pulse"}):(0,i.jsx)(h.A,{className:"h-4 w-4"})})]})]})},e.id))}),!er&&q&&!q.startsWith("Error loading model configuration:")&&(0,i.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(m.A,{className:"h-5 w-5 text-red-400"}),(0,i.jsxs)("p",{className:"text-red-300 font-medium text-sm",children:["Could not load API keys/roles: ",q]})]})})]})})]}),"user-api-keys"===e$&&(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:(0,i.jsx)(nN,{configId:e,configName:E.name})})]}),el&&(()=>{if(!el)return null;let e=[...u.p2.map(e=>({...e,isCustom:!1})),...ey.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,t)=>e.name.localeCompare(t.name));return(0,i.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,i.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,i.jsxs)("h2",{className:"text-xl font-semibold text-white",children:["Manage Roles for: ",(0,i.jsx)("span",{className:"text-orange-400",children:el.label})]}),(0,i.jsx)("button",{onClick:()=>{ed(null),e_(!1),eI(null)},className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,i.jsx)(m.A,{className:"h-6 w-6"})})]}),(0,i.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[ej&&(0,i.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,i.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",ej]})}),(0,i.jsxs)(nj.sU,{feature:"custom_roles",customMessage:"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.",children:[(0,i.jsx)("div",{className:"flex justify-end mb-4",children:(0,i.jsxs)("button",{onClick:()=>e_(!ek),className:"btn-primary text-sm inline-flex items-center",children:[(0,i.jsx)(f.A,{className:"h-4 w-4 mr-2"}),ek?"Cancel New Role":"Create New Custom Role"]})}),ek&&(0,i.jsxs)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4",children:[(0,i.jsx)("h3",{className:"text-md font-medium text-white mb-3",children:"Create New Custom Role for this Configuration"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,i.jsx)("input",{type:"text",id:"newCustomRoleId",value:eE,onChange:e=>eC(e.target.value.replace(/\s/g,"")),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-300 mb-1",children:"Display Name (max 100 chars)"}),(0,i.jsx)("input",{type:"text",id:"newCustomRoleName",value:eA,onChange:e=>eS(e.target.value),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-300 mb-1",children:"Description (optional, max 500 chars)"}),(0,i.jsx)("textarea",{id:"newCustomRoleDescription",value:eR,onChange:e=>eP(e.target.value),rows:2,className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eD&&(0,i.jsx)("div",{className:"bg-red-900/50 border border-red-800/50 rounded-lg p-3",children:(0,i.jsx)("p",{className:"text-red-300 text-sm",children:eD})}),(0,i.jsx)("button",{onClick:e5,disabled:eT,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eT?"Saving Role...":"Save Custom Role"})]})]})]})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-300 mb-3",children:"Select roles to assign:"}),(0,i.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eb&&(0,i.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let t=el.assigned_roles.some(t=>t.id===e.id);return(0,i.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${t?"bg-orange-500/20 border-orange-500/30 shadow-sm":"bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm"}`,children:[(0,i.jsxs)("label",{htmlFor:`role-${e.id}`,className:"flex items-center cursor-pointer flex-grow",children:[(0,i.jsx)("input",{type:"checkbox",id:`role-${e.id}`,checked:t,onChange:()=>eG(el,e.id,t),className:"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700"}),(0,i.jsx)("span",{className:`ml-3 text-sm font-medium ${t?"text-orange-300":"text-white"}`,children:e.name}),e.isCustom&&(0,i.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,i.jsx)("button",{onClick:()=>e4(e.databaseId,e.name),disabled:eO===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eO===e.databaseId?(0,i.jsx)(p.A,{className:"h-4 w-4 animate-spin"}):(0,i.jsx)(h.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,i.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,i.jsx)("div",{className:"flex justify-end",children:(0,i.jsx)("button",{onClick:()=>{ed(null),e_(!1),eI(null)},className:"btn-secondary",children:"Done"})})})]})})})(),ec&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,i.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Edit API Key"}),(0,i.jsx)("button",{onClick:()=>eu(null),className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,i.jsx)(m.A,{className:"h-6 w-6"})})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:ec.label}),(0,i.jsxs)("p",{className:"text-sm text-gray-400",children:["Current: ",ec.provider," (",ec.predefined_model_id,")"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,i.jsx)("div",{className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300",children:c.MG.find(e=>e.id===ec.provider)?.name||ec.provider}),(0,i.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Provider cannot be changed"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,i.jsx)("select",{id:"editModelId",value:ep,onChange:e=>eh(e.target.value),disabled:!eK.length,className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30",children:eK.length>0?eK.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value)):(0,i.jsx)("option",{value:"",disabled:!0,children:Y?"Loading models...":"No models available"})})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",em]}),(0,i.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:em,onChange:e=>ef(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,i.jsx)("span",{children:"0.0 (Focused)"}),(0,i.jsx)("span",{children:"1.0 (Balanced)"}),(0,i.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,i.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-3",children:(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,i.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{onClick:()=>eu(null),className:"btn-secondary",disabled:eg,children:"Cancel"}),(0,i.jsx)("button",{onClick:eH,disabled:eg,className:"btn-primary",children:eg?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!E&&!A&&!q&&(0,i.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,i.jsx)(j.A,{className:"h-8 w-8 text-gray-400"})}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,i.jsxs)("button",{onClick:()=>n("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,i.jsx)(eY.A,{isOpen:t.isOpen,onClose:t.hideConfirmation,onConfirm:t.onConfirm,title:t.title,message:t.message,confirmText:t.confirmText,cancelText:t.cancelText,type:t.type,isLoading:t.isLoading}),(0,i.jsx)(eX,{id:"global-tooltip"})]})})}},43985:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=function(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=function e(t){var r,n,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(n=e(t[r]))&&(a&&(a+=" "),a+=n)}else for(n in t)t[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=t);return n},s=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:o}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],a=null==o?void 0:o[e];if(null===t)return null;let s=n(t)||n(a);return i[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,l,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(43210);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},47281:(e,t)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=s(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=s(t,r));return t}(r)))}return e}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0===(r=(function(){return a}).apply(t,[]))||(e.exports=r)}()},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(43210);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},51983:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=r(65239),a=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["my-models",{children:["[configId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,20218)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(r.bind(r,92529)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/my-models/[configId]/page",pathname:"/my-models/[configId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var n=r(60687),a=r(43210),s=r(14163),i=a.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";let o=(0,r(43985).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(i,{ref:r,className:`${o()} ${e||""}`,...t}));l.displayName=i.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57891:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(43210);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},60925:(e,t,r)=>{"use strict";r.d(t,{c:()=>s});var n=r(43210);let a={};function s(){let[e,t]=(0,n.useState)({}),r=(0,n.useRef)({}),s=(0,n.useCallback)(e=>{let t=a[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),i=(0,n.useCallback)(e=>{let t=a[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete a[e],null):t.data},[]),o=(0,n.useCallback)(async(e,n="medium")=>{if(s(e))return i(e);if(a[e]?.isLoading)return null;r.current[e]&&r.current[e].abort();let o=new AbortController;r.current[e]=o,a[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===n?await new Promise(e=>setTimeout(e,200)):"medium"===n&&await new Promise(e=>setTimeout(e,50));let[r,s,i]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:o.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:o.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:o.signal})]),l=null,d=[],c="none",u={},m=[];"fulfilled"===r.status&&r.value.ok&&(c=(l=await r.value.json()).routing_strategy||"none",u=l.routing_strategy_params||{}),"fulfilled"===s.status&&s.value.ok&&(d=await s.value.json()),"fulfilled"===i.status&&i.value.ok&&(m=await i.value.json());let f={configDetails:l,apiKeys:d,routingStrategy:c,routingParams:u,complexityAssignments:m};return a[e]={data:f,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),f}catch(r){if("AbortError"===r.name)return null;return delete a[e],t(t=>({...t,[e]:"error"})),null}finally{delete r.current[e]}},[s,i]),l=(0,n.useCallback)(e=>({onMouseEnter:()=>{s(e)||o(e,"high")}}),[o,s]),d=(0,n.useCallback)(e=>{delete a[e],t(t=>{let r={...t};return delete r[e],r})},[]),c=(0,n.useCallback)(()=>{Object.keys(a).forEach(e=>{delete a[e]}),t({})},[]);return{prefetchRoutingSetupData:o,getCachedData:i,isCached:s,createHoverPrefetch:l,clearCache:d,clearAllCache:c,getStatus:(0,n.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,n.useCallback)(()=>({cachedConfigs:Object.keys(a),cacheSize:Object.keys(a).length,totalCacheAge:Object.values(a).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(a).length}),[]),prefetchStatus:e}}},62525:(e,t,r)=>{"use strict";r.d(t,{Dc:()=>a,p2:()=>n});let n=[{id:"general_chat",name:"General Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],a=e=>n.find(t=>t.id===e)},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:s="",children:i,iconNode:c,...u},m)=>(0,n.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:o("lucide",s),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...s},l)=>(0,n.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,r),...s}));return r.displayName=i(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66368:(e,t,r)=>{"use strict";r.d(t,{MG:()=>n});let n=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},70143:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(43210);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86099:(e,t,r)=>{Promise.resolve().then(r.bind(r,20218))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95753:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(43210);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))})},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>s});var n=r(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function i(...e){return n.useCallback(s(...e),e)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,1752,2535,6379,4912,4847,453],()=>r(51983));module.exports=n})();