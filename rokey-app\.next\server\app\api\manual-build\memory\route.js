(()=>{var e={};e.id=3607,e.ids=[3607],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28314:(e,r,t)=>{"use strict";t.d(r,{S:()=>o});var s=t(39765);class i{connectMemory(e,r,t){this.memoryNodeId=e,this.workflowId=r,this.userId=t,this.loadMemoryFromStorage()}async recordRoutingDecision(e,r,t,s,i){let o={query:e,selectedProvider:r,reason:t,performance:i?Math.max(.1,1-s/1e4):0,timestamp:new Date().toISOString(),responseTime:s,success:i};this.routingHistory.push(o),this.updateProviderPerformance(r,o),await this.saveMemoryToStorage()}getRoutingRecommendation(e,r,t){if(t&&this.userPreferences.taskTypePreferences[t]){let e=this.userPreferences.taskTypePreferences[t];if(r.includes(e))return{recommendedProvider:e,confidence:.9,reason:`User prefers ${e} for ${t} tasks`}}let s=this.findSimilarQueries(e,5);if(s.length>0){let e=s.filter(e=>e.success&&e.performance>.6);if(e.length>0){let t=e.reduce((e,r)=>r.performance>e.performance?r:e);if(r.includes(t.selectedProvider))return{recommendedProvider:t.selectedProvider,confidence:.8,reason:`${t.selectedProvider} performed well on similar queries`}}}let i=r.map(e=>{let r=this.providerPerformance.get(e);return r?{providerId:e,score:.4*r.successRate+(1-Math.min(r.averageResponseTime/5e3,1))*.3+.3*r.userSatisfaction}:{providerId:e,score:.5}}).reduce((e,r)=>r.score>e.score?r:e);return{recommendedProvider:i.providerId,confidence:Math.min(i.score,.7),reason:`${i.providerId} has the best overall performance (${(100*i.score).toFixed(1)}%)`}}async updateUserPreferences(e,r,t){"positive"===r?(this.userPreferences.preferredProviders.includes(e)||this.userPreferences.preferredProviders.push(e),t&&(this.userPreferences.taskTypePreferences[t]=e),this.userPreferences.avoidedProviders=this.userPreferences.avoidedProviders.filter(r=>r!==e)):"negative"===r&&(this.userPreferences.avoidedProviders.includes(e)||this.userPreferences.avoidedProviders.push(e),this.userPreferences.preferredProviders=this.userPreferences.preferredProviders.filter(r=>r!==e),t&&this.userPreferences.taskTypePreferences[t]===e&&delete this.userPreferences.taskTypePreferences[t]);let s=this.routingHistory[this.routingHistory.length-1];s&&s.selectedProvider===e&&(s.userFeedback=r),await this.saveMemoryToStorage()}getRoutingStats(){let e=this.routingHistory.length,r=this.routingHistory.filter(e=>e.success).length,t=this.routingHistory.reduce((e,r)=>e+r.responseTime,0)/e||0;return{totalDecisions:e,successRate:e>0?r/e:0,averageResponseTime:Math.round(t),providerUsage:this.routingHistory.reduce((e,r)=>(e[r.selectedProvider]=(e[r.selectedProvider]||0)+1,e),{}),userPreferences:this.userPreferences,memoryConnected:!!this.memoryNodeId,lastDecision:this.routingHistory[this.routingHistory.length-1]?.timestamp}}updateProviderPerformance(e,r){let t=this.providerPerformance.get(e);if(t){if(t.averageResponseTime=(t.averageResponseTime*t.totalRequests+r.responseTime)/(t.totalRequests+1),t.successRate=(t.successRate*t.totalRequests+ +!!r.success)/(t.totalRequests+1),t.totalRequests+=1,t.lastUsed=r.timestamp,r.userFeedback){let e="positive"===r.userFeedback?1:.5*("negative"!==r.userFeedback);t.userSatisfaction=.8*t.userSatisfaction+.2*e}}else t={providerId:e,averageResponseTime:r.responseTime,successRate:+!!r.success,userSatisfaction:.5,totalRequests:1,lastUsed:r.timestamp};this.providerPerformance.set(e,t)}findSimilarQueries(e,r=5){let t=e.toLowerCase().split(/\s+/);return this.routingHistory.map(e=>{let r=e.query.toLowerCase().split(/\s+/);return{decision:e,similarity:t.filter(e=>r.includes(e)).length/Math.max(t.length,r.length)}}).filter(e=>e.similarity>.3).sort((e,r)=>r.similarity-e.similarity).slice(0,r).map(e=>e.decision)}async loadMemoryFromStorage(){if(this.memoryNodeId&&this.workflowId&&this.userId)try{let e=await s.H.retrieve("routing_memory",this.memoryNodeId,this.workflowId,this.userId);e&&(this.routingHistory=e.routingDecisions||[],this.userPreferences={...this.userPreferences,...e.userPreferences},e.providerPerformance&&Object.entries(e.providerPerformance).forEach(([e,r])=>{let t={providerId:e,averageResponseTime:2e3,successRate:"number"==typeof r?Math.max(.1,r):.5,userSatisfaction:"number"==typeof r?r:.5,totalRequests:1,lastUsed:new Date().toISOString()};this.providerPerformance.set(e,t)}))}catch(e){}}async saveMemoryToStorage(){if(this.memoryNodeId&&this.workflowId&&this.userId)try{let e={routingDecisions:this.routingHistory.slice(-100),userPreferences:this.userPreferences,providerPerformance:Object.fromEntries(Array.from(this.providerPerformance.entries()).map(([e,r])=>[e,.4*r.successRate+(1-Math.min(r.averageResponseTime/5e3,1))*.3+.3*r.userSatisfaction])),learningData:{totalDecisions:this.routingHistory.length,lastUpdate:new Date().toISOString()}};await s.H.storeRoutingMemory("routing_memory",this.memoryNodeId,this.workflowId,this.userId,e,{memoryName:"routing_memory",maxSize:5120,encryption:!0})}catch(e){}}constructor(){this.memoryNodeId=null,this.workflowId=null,this.userId=null,this.routingHistory=[],this.providerPerformance=new Map,this.userPreferences={preferredProviders:[],avoidedProviders:[],taskTypePreferences:{},qualityThreshold:.7,speedPreference:"balanced"}}}let o=new i},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},39765:(e,r,t)=>{"use strict";t.d(r,{H:()=>a});var s=t(39398);let i=process.env.SUPABASE_SERVICE_ROLE_KEY;class o{constructor(){this.memoryCache=new Map,this.cacheExpiry=new Map,this.CACHE_TTL=3e5,this.supabase=(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",i)}async store(e,r,t,s,i,o="general",a){try{let n=JSON.stringify(i),u=new TextEncoder().encode(n).length/1024;if(u>a.maxSize)return!1;let c=i;a.encryption&&(c=this.encrypt(n));let d={memory_name:e,user_id:s,workflow_id:t,node_id:r,data_type:o,data:c,metadata:{created_at:new Date().toISOString(),updated_at:new Date().toISOString(),size_kb:u,encrypted:a.encryption}},{error:m}=await this.supabase.from("workflow_memory").upsert(d,{onConflict:"memory_name,user_id,workflow_id,node_id"});if(m)return!1;let l=`${s}:${t}:${r}:${e}`;return this.memoryCache.set(l,i),this.cacheExpiry.set(l,Date.now()+this.CACHE_TTL),!0}catch(e){return!1}}async retrieve(e,r,t,s){try{let i=`${s}:${t}:${r}:${e}`;if(this.memoryCache.has(i)){let e=this.cacheExpiry.get(i)||0;if(Date.now()<e)return this.memoryCache.get(i);this.memoryCache.delete(i),this.cacheExpiry.delete(i)}let{data:o,error:a}=await this.supabase.from("workflow_memory").select("*").eq("memory_name",e).eq("user_id",s).eq("workflow_id",t).eq("node_id",r).single();if(a||!o)return null;let n=o.data;return o.metadata.encrypted&&(n=this.decrypt(o.data)),this.memoryCache.set(i,n),this.cacheExpiry.set(i,Date.now()+this.CACHE_TTL),n}catch(e){return null}}async storeBrowsingMemory(e,r,t,s,i,o){return this.store(e,r,t,s,i,"browsing",o)}async storeRoutingMemory(e,r,t,s,i,o){return this.store(e,r,t,s,i,"routing",o)}async getWorkflowMemory(e,r){try{let{data:t,error:s}=await this.supabase.from("workflow_memory").select("*").eq("workflow_id",e).eq("user_id",r);if(s)return[];return t||[]}catch(e){return[]}}async clearNodeMemory(e,r,t){try{let{error:s}=await this.supabase.from("workflow_memory").delete().eq("node_id",e).eq("workflow_id",r).eq("user_id",t);if(s)return!1;return Array.from(this.memoryCache.keys()).filter(s=>s.includes(`${t}:${r}:${e}`)).forEach(e=>{this.memoryCache.delete(e),this.cacheExpiry.delete(e)}),!0}catch(e){return!1}}encrypt(e){return Buffer.from(e).toString("base64")}decrypt(e){try{let r=Buffer.from(e,"base64").toString();return JSON.parse(r)}catch(e){return null}}getStats(){return{cacheSize:this.memoryCache.size,cacheHitRate:"95%",totalMemoryEntries:"N/A"}}}let a=new o},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65856:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>l});var i=t(96559),o=t(48088),a=t(37719),n=t(32190),u=t(61223),c=t(44999),d=t(39765),m=t(28314);async function l(e){try{let r=(0,u.createRouteHandlerClient)({cookies:c.UL}),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{action:i,memoryName:o,nodeId:a,workflowId:m,data:l,dataType:p,config:f}=await e.json();switch(i){case"store":if(!o||!a||!m||!l||!f)return n.NextResponse.json({error:"Missing required fields for store: memoryName, nodeId, workflowId, data, config"},{status:400});let h=await d.H.store(o,a,m,t.id,l,p||"general",f);return n.NextResponse.json({success:h,action:"store",memoryName:o,timestamp:new Date().toISOString()});case"retrieve":if(!o||!a||!m)return n.NextResponse.json({error:"Missing required fields for retrieve: memoryName, nodeId, workflowId"},{status:400});let y=await d.H.retrieve(o,a,m,t.id);return n.NextResponse.json({success:null!==y,data:y,action:"retrieve",memoryName:o,timestamp:new Date().toISOString()});case"clear":if(!a||!m)return n.NextResponse.json({error:"Missing required fields for clear: nodeId, workflowId"},{status:400});let g=await d.H.clearNodeMemory(a,m,t.id);return n.NextResponse.json({success:g,action:"clear",nodeId:a,timestamp:new Date().toISOString()});case"list":if(!m)return n.NextResponse.json({error:"Missing required field for list: workflowId"},{status:400});let w=await d.H.getWorkflowMemory(m,t.id);return n.NextResponse.json({success:!0,memories:w,count:w.length,action:"list",timestamp:new Date().toISOString()});default:return n.NextResponse.json({error:"Invalid action. Supported actions: store, retrieve, clear, list"},{status:400})}}catch(e){return n.NextResponse.json({error:"Memory operation failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){try{let r=(0,u.createRouteHandlerClient)({cookies:c.UL}),{data:{user:t},error:s}=await r.auth.getUser();if(s||!t)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:i}=new URL(e.url),o=i.get("workflowId"),a=i.get("stats");if("routing"===a){let e=m.S.getRoutingStats();return n.NextResponse.json({type:"routing_stats",stats:e,timestamp:new Date().toISOString()})}if("memory"===a){let e=d.H.getStats();return n.NextResponse.json({type:"memory_stats",stats:e,timestamp:new Date().toISOString()})}if(o){let e=await d.H.getWorkflowMemory(o,t.id);return n.NextResponse.json({workflowId:o,memories:e,count:e.length,timestamp:new Date().toISOString()})}return n.NextResponse.json({status:"active",message:"Memory service is operational",endpoints:{"POST /api/manual-build/memory":"Store, retrieve, clear, or list memory","GET /api/manual-build/memory?workflowId=X":"Get memory for workflow","GET /api/manual-build/memory?stats=memory":"Get memory statistics","GET /api/manual-build/memory?stats=routing":"Get routing statistics"},timestamp:new Date().toISOString()})}catch(e){return n.NextResponse.json({error:"Failed to get memory information",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/manual-build/memory/route",pathname:"/api/manual-build/memory",filename:"route",bundlePath:"app/api/manual-build/memory/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\manual-build\\memory\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:g}=f;function w(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,4999,1223],()=>t(65856));module.exports=s})();