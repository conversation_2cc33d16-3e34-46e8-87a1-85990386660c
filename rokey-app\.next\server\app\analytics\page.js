(()=>{var e={};e.id=1745,e.ids=[1745],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15341:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(60687),a=s(43210),n=s(16189),o=s(11016),l=s(45700);let i=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))});var c=s(30922),d=s(66524);let x=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var m=s(59168);let p=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"}))});var h=s(17712),u=s(61245),g=s(45994),y=s(50515),v=s(31082),b=s(68589),f=s(70143);let j=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),w=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:4}).format(e),N=(e,t)=>{if(0===t)return{percentage:0,isPositive:e>0};let s=(e-t)/t*100;return{percentage:Math.abs(s),isPositive:s>=0}},k=({title:e,value:t,trend:s,icon:a,subtitle:n})=>(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:e}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-white",children:"number"==typeof t?j(t):t}),s&&(0,r.jsxs)("span",{className:`text-sm px-2 py-1 rounded-md flex items-center space-x-1 ${s.isPositive?"text-green-400 bg-green-400/10":"text-red-400 bg-red-400/10"}`,children:[s.isPositive?(0,r.jsx)(l.A,{className:"w-3 h-3"}):(0,r.jsx)(i,{className:"w-3 h-3"}),(0,r.jsxs)("span",{children:[s.percentage.toFixed(1),"%"]})]})]}),n&&(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:n})]}),(0,r.jsx)("div",{className:"text-gray-500",children:a})]})});function C(){(0,n.useRouter)();let e=(0,o.R)(),[t,s]=(0,a.useState)(null),[C,A]=(0,a.useState)(null),[S,M]=(0,a.useState)([]),[E,R]=(0,a.useState)([]),[L,_]=(0,a.useState)(!0),[P,q]=(0,a.useState)(null),[$,I]=(0,a.useState)("30"),[K,O]=(0,a.useState)(""),[D,B]=(0,a.useState)("overview"),W=(0,a.useCallback)(async()=>{try{_(!0),q(null);try{let e=await fetch("/api/analytics/summary?groupBy=provider");if(401===e.status)throw Error("Authentication required. Please log in to view analytics.")}catch(e){}let e=new URLSearchParams,t=new Date;t.setDate(t.getDate()-parseInt($)),e.append("startDate",t.toISOString()),K&&e.append("customApiConfigId",K);let r=await fetch(`/api/analytics/summary?${e.toString()}&groupBy=provider`);if(!r.ok){let e=await r.text();if(401===r.status)throw Error("Authentication required. Please log in to view analytics.");throw Error(`Failed to fetch analytics data: ${r.status} ${e}`)}let a=await r.json();if(s(a),a.summary?.total_requests>0)try{let t=new URLSearchParams,s=new Date;s.setDate(s.getDate()-2*parseInt($));let r=new Date;r.setDate(r.getDate()-parseInt($)),t.append("startDate",s.toISOString()),t.append("endDate",r.toISOString()),K&&t.append("customApiConfigId",K);let[a,n]=await Promise.all([fetch(`/api/analytics/summary?${t.toString()}&groupBy=provider`),fetch(`/api/analytics/summary?${e.toString()}&groupBy=day`)]),o=a.ok?await a.json():null,l=n.ok?await n.json():null;if(A(o),l?.grouped_data){let e=l.grouped_data.map(e=>({date:e.period||e.name,cost:e.cost||0,requests:e.requests||0,tokens:(e.input_tokens||0)+(e.output_tokens||0),latency:e.avg_latency||0}));M(e)}}catch(e){}}catch(e){q(e.message)}finally{_(!1)}},[$,K]);if(e?.loading!==!1||L)return(0,r.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white",children:(0,r.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-800 rounded w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"bg-gray-900 rounded-lg p-6 border border-gray-800",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-800 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-800 rounded w-1/3"})]},t))})]}),(0,r.jsxs)("div",{className:"fixed bottom-4 right-4 bg-gray-800 rounded-lg p-3 flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-300",children:e?.loading!==!1?"Authenticating...":"Loading analytics..."})]})]})});if(P)return(0,r.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white",children:(0,r.jsx)("div",{className:"w-full px-6 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold mb-4",children:"Analytics"}),(0,r.jsxs)("div",{className:"bg-red-900/20 border border-red-800 rounded-lg p-6 max-w-md mx-auto",children:[(0,r.jsxs)("p",{className:"text-red-400 mb-4",children:["Error loading analytics: ",P]}),(0,r.jsx)("button",{onClick:W,className:"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors",children:"Retry"})]})]})})});let F=t?.summary,G=C?.summary,H=G?N(F?.total_cost||0,G.total_cost):void 0,T=G?N(F?.total_requests||0,G.total_requests):void 0,U=G?N(F?.average_latency||0,G.average_latency||0):void 0,Z=G?N(F?.success_rate||0,G.success_rate):void 0;return(0,r.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,r.jsx)("div",{className:"border-b border-gray-800/50",children:(0,r.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Analytics"}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("button",{className:"px-3 py-1 text-sm text-gray-400 hover:text-white transition-colors",children:"Workspace"}),(0,r.jsx)("button",{className:"px-3 py-1 text-sm bg-cyan-500 text-white rounded",children:"Organisation"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search Filter",className:"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32"})]}),(0,r.jsxs)("select",{value:$,onChange:e=>I(e.target.value),className:"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500",children:[(0,r.jsx)("option",{value:"7",children:"Last 7 days"}),(0,r.jsx)("option",{value:"30",children:"Last 30 days"}),(0,r.jsx)("option",{value:"90",children:"Last 90 days"})]})]})]})})}),(0,r.jsx)("div",{className:"border-b border-gray-800/50",children:(0,r.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)("button",{onClick:()=>B("overview"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"overview"===D?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,r.jsx)(d.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Overview"})]}),(0,r.jsxs)("button",{onClick:()=>B("users"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"users"===D?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,r.jsx)(x,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Users"})]}),(0,r.jsxs)("button",{onClick:()=>B("errors"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"errors"===D?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Errors"})]}),(0,r.jsxs)("button",{onClick:()=>B("cache"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"cache"===D?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,r.jsx)(p,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Cache"})]}),(0,r.jsxs)("button",{onClick:()=>B("feedback"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"feedback"===D?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Feedback"})]}),(0,r.jsxs)("button",{onClick:()=>B("metadata"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"metadata"===D?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Metadata"})]})]})})}),(0,r.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:["overview"===D&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(k,{title:"Total Request Made",value:F?.total_requests||0,trend:T,icon:(0,r.jsx)(g.A,{className:"w-6 h-6"})}),(0,r.jsx)(k,{title:"Average Latency",value:`${Math.round(F?.average_latency||0)}ms`,trend:U,icon:(0,r.jsx)(y.A,{className:"w-6 h-6"})}),(0,r.jsx)(k,{title:"User Feedback",value:`${(F?.success_rate||0).toFixed(1)}%`,trend:Z,icon:(0,r.jsx)(h.A,{className:"w-6 h-6"})}),(0,r.jsx)(k,{title:"Total Cost",value:w(F?.total_cost||0),trend:H,icon:(0,r.jsx)(v.A,{className:"w-6 h-6"})})]}),(!F||0===F.total_requests)&&!L&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(g.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Analytics Data Yet"}),(0,r.jsx)("p",{className:"text-gray-400 mb-4",children:"Start making API requests to see your analytics data here."}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"Analytics will appear once you begin using your API configurations."}),!1]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cost"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-white",children:w(F?.total_cost||0)}),H&&(0,r.jsxs)("span",{className:`text-sm px-2 py-1 rounded flex items-center space-x-1 ${H.isPositive?"text-red-400 bg-red-400/10":"text-green-400 bg-green-400/10"}`,children:[H.isPositive?(0,r.jsx)(l.A,{className:"w-3 h-3"}):(0,r.jsx)(i,{className:"w-3 h-3"}),(0,r.jsxs)("span",{children:[H.percentage.toFixed(1),"%"]})]})]})]}),(0,r.jsx)("div",{className:"text-gray-500",children:(0,r.jsx)(v.A,{className:"w-6 h-6"})})]}),(0,r.jsx)("div",{className:"h-48 relative bg-gray-800/50 rounded",children:S.length>0?(0,r.jsx)("div",{className:"absolute inset-4",children:(0,r.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 400 120",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"costGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#10b981",stopOpacity:"0.3"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#10b981",stopOpacity:"0"})]})}),[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)("line",{x1:"0",y1:24*t,x2:"400",y2:24*t,stroke:"#374151",strokeWidth:"0.5",opacity:"0.3"},t)),(0,r.jsx)("polyline",{fill:"none",stroke:"#10b981",strokeWidth:"2",points:S.map((e,t)=>{let s=t/Math.max(S.length-1,1)*400,r=Math.max(...S.map(e=>e.cost)),a=120-e.cost/r*100;return`${s},${a}`}).join(" ")}),(0,r.jsx)("polygon",{fill:"url(#costGradient)",points:`0,120 ${S.map((e,t)=>{let s=t/Math.max(S.length-1,1)*400,r=Math.max(...S.map(e=>e.cost)),a=120-e.cost/r*100;return`${s},${a}`}).join(" ")} 400,120`})]})}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No data available"})]})})})]}),(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Latency"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,r.jsxs)("span",{className:"text-3xl font-bold text-white",children:[Math.round(F?.average_latency||0),"ms"]}),U&&(0,r.jsxs)("span",{className:`text-sm px-2 py-1 rounded flex items-center space-x-1 ${U.isPositive?"text-red-400 bg-red-400/10":"text-green-400 bg-green-400/10"}`,children:[U.isPositive?(0,r.jsx)(l.A,{className:"w-3 h-3"}):(0,r.jsx)(i,{className:"w-3 h-3"}),(0,r.jsxs)("span",{children:[U.percentage.toFixed(1),"%"]})]})]})]}),(0,r.jsx)("div",{className:"text-gray-500",children:(0,r.jsx)(y.A,{className:"w-6 h-6"})})]}),(0,r.jsx)("div",{className:"h-48 relative bg-gray-800/50 rounded",children:S.length>0?(0,r.jsx)("div",{className:"absolute inset-4",children:(0,r.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 400 120",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"latencyGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#f59e0b",stopOpacity:"0.3"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#f59e0b",stopOpacity:"0"})]})}),[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)("line",{x1:"0",y1:24*t,x2:"400",y2:24*t,stroke:"#374151",strokeWidth:"0.5",opacity:"0.3"},t)),(0,r.jsx)("polyline",{fill:"none",stroke:"#f59e0b",strokeWidth:"2",points:S.map((e,t)=>{let s=t/Math.max(S.length-1,1)*400,r=Math.max(...S.map(e=>e.latency||0)),a=120-(e.latency||0)/Math.max(r,1)*100;return`${s},${a}`}).join(" ")}),(0,r.jsx)("polygon",{fill:"url(#latencyGradient)",points:`0,120 ${S.map((e,t)=>{let s=t/Math.max(S.length-1,1)*400,r=Math.max(...S.map(e=>e.latency||0)),a=120-(e.latency||0)/Math.max(r,1)*100;return`${s},${a}`}).join(" ")} 400,120`})]})}):(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No data available"})]})})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Tokens Used"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"March 28"})]}),(0,r.jsx)("div",{className:"text-gray-500",children:(0,r.jsx)(b.A,{className:"w-6 h-6"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-white",children:j(F?.total_tokens||0)}),(0,r.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,r.jsx)(l.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"8.39%"})]})]}),(0,r.jsx)("div",{className:"h-32 relative bg-gray-800/50 rounded",children:(0,r.jsxs)("div",{className:"absolute inset-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4 text-xs",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),(0,r.jsx)("span",{className:"text-gray-400",children:"Input Token"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,r.jsx)("span",{className:"text-gray-400",children:"Output Token"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),(0,r.jsx)("span",{className:"text-gray-400",children:"Total Token"})]})]}),(0,r.jsx)("div",{className:"relative h-16",children:[...Array(20)].map((e,t)=>(0,r.jsx)("div",{className:`absolute w-1 h-1 rounded-full ${t%3==0?"bg-yellow-500":t%3==1?"bg-green-500":"bg-blue-500"}`,style:{left:`${90*Math.random()}%`,top:`${80*Math.random()}%`}},t))})]})})]}),(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Requests"})}),(0,r.jsx)("div",{className:"text-gray-500",children:(0,r.jsx)(f.A,{className:"w-6 h-6"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-white",children:j(F?.total_requests||0)}),(0,r.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,r.jsx)(l.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"3.39%"})]})]}),(0,r.jsx)("div",{className:"space-y-3",children:t?.grouped_data.slice(0,5).map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-20 text-sm text-gray-400 truncate",children:e.name}),(0,r.jsx)("div",{className:"flex-1 mx-3",children:(0,r.jsx)("div",{className:"h-2 bg-gray-800 rounded-full overflow-hidden",children:(0,r.jsx)("div",{className:`h-full rounded-full ${0===t?"bg-pink-500":1===t?"bg-purple-500":2===t?"bg-cyan-500":3===t?"bg-green-500":"bg-yellow-500"}`,style:{width:`${e.requests/(F?.total_requests||1)*100}%`}})})}),(0,r.jsx)("div",{className:"text-sm text-gray-400 w-12 text-right",children:j(e.requests)})]},e.name))})]}),(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Unique Users"})}),(0,r.jsx)("div",{className:"text-gray-500",children:(0,r.jsx)(x,{className:"w-6 h-6"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-white",children:F?.successful_requests||0}),(0,r.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,r.jsx)(l.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"3.39%"})]})]}),(0,r.jsx)("div",{className:"h-32 relative bg-gray-800/50 rounded",children:(0,r.jsx)("div",{className:"absolute inset-4",children:(0,r.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 200 80",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"waveGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#8b5cf6",stopOpacity:"0.3"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#8b5cf6",stopOpacity:"0"})]})}),(0,r.jsx)("path",{d:"M0,40 Q50,20 100,40 T200,40",fill:"none",stroke:"#8b5cf6",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z",fill:"url(#waveGradient)"})]})})})]})]})]}),"users"===D&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(x,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Users Analytics"}),(0,r.jsx)("p",{className:"text-gray-400",children:"User analytics coming soon..."})]}),"errors"===D&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(m.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Error Analytics"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Error analytics coming soon..."})]}),"cache"===D&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(p,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Cache Analytics"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Cache analytics coming soon..."})]}),"feedback"===D&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Feedback Analytics"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Feedback analytics coming soon..."})]}),"metadata"===D&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Metadata Analytics"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Metadata analytics coming soon..."})]})]})]})}function A(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-[#040716] text-white",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-800 rounded w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"bg-gray-900 rounded-lg p-6 border border-gray-800",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-800 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-800 rounded w-1/3"})]},t))})]})})}),children:(0,r.jsx)(C,{})})}},17712:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19153:(e,t,s)=>{Promise.resolve().then(s.bind(s,15341))},21031:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx","default")},23425:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),o=s.n(n),l=s(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let c={children:["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21031)),"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,28044)),"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28044:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413),a=s(47417);function n(){return(0,r.jsx)(a.AnalyticsSkeleton,{})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31082:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40877:(e,t,s)=>{Promise.resolve().then(s.bind(s,47417))},45700:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))})},47417:(e,t,s)=>{"use strict";s.d(t,{AnalyticsSkeleton:()=>i,ConfigSelectorSkeleton:()=>n,MessageSkeleton:()=>a,MyModelsSkeleton:()=>o,RoutingSetupSkeleton:()=>l});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},50515:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},54001:(e,t,s)=>{Promise.resolve().then(s.bind(s,21031))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64845:(e,t,s)=>{Promise.resolve().then(s.bind(s,35291))},66524:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},68589:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))})},70143:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,1752,4912],()=>s(23425));module.exports=r})();