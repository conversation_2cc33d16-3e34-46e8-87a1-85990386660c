"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7068],{61772:(e,t,n)=>{n.d(t,{Gc:()=>tE,H2:()=>tW,VS:()=>tL,Yu:()=>tP,ck:()=>tj,fM:()=>tD,h7:()=>eS,of:()=>t0,pk:()=>tN,rN:()=>i.rN,yX:()=>i.yX});var o,l=n(95155),r=n(12115),a=n(75694),i=n(89724),s=n(44741),d=n(75745);n(47650);let c=(0,r.createContext)(null),u=c.Provider,g=i.xc.error001();function f(e,t){let n=(0,r.useContext)(c);if(null===n)throw Error(g);return(0,s.n)(n,e,t)}function p(){let e=(0,r.useContext)(c);if(null===e)throw Error(g);return(0,r.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe}),[e])}let m={display:"none"},h={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},v="react-flow__node-desc",y="react-flow__edge-desc",b=e=>e.ariaLiveMessage,x=e=>e.ariaLabelConfig;function w(e){let{rfId:t}=e,n=f(b);return(0,l.jsx)("div",{id:"".concat("react-flow__aria-live","-").concat(t),"aria-live":"assertive","aria-atomic":"true",style:h,children:n})}function S(e){let{rfId:t,disableKeyboardA11y:n}=e,o=f(x);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{id:"".concat(v,"-").concat(t),style:m,children:n?o["node.a11yDescription.default"]:o["node.a11yDescription.keyboardDisabled"]}),(0,l.jsx)("div",{id:"".concat(y,"-").concat(t),style:m,children:o["edge.a11yDescription.default"]}),!n&&(0,l.jsx)(w,{rfId:t})]})}let C=(0,r.forwardRef)((e,t)=>{let{position:n="top-left",children:o,className:r,style:i,...s}=e,d="".concat(n).split("-");return(0,l.jsx)("div",{className:(0,a.A)(["react-flow__panel",r,...d]),style:i,ref:t,...s,children:o})});function E(e){let{proOptions:t,position:n="bottom-right"}=e;return(null==t?void 0:t.hideAttribution)?null:(0,l.jsx)(C,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev",children:(0,l.jsx)("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution",children:"React Flow"})})}C.displayName="Panel";let k=e=>{let t=[],n=[];for(let[,n]of e.nodeLookup)n.selected&&t.push(n.internals.userNode);for(let[,t]of e.edgeLookup)t.selected&&n.push(t);return{selectedNodes:t,selectedEdges:n}},N=e=>e.id;function M(e,t){return(0,d.x)(e.selectedNodes.map(N),t.selectedNodes.map(N))&&(0,d.x)(e.selectedEdges.map(N),t.selectedEdges.map(N))}function P(e){let{onSelectionChange:t}=e,n=p(),{selectedNodes:o,selectedEdges:l}=f(k,M);return(0,r.useEffect)(()=>{let e={nodes:o,edges:l};null==t||t(e),n.getState().onSelectionChangeHandlers.forEach(t=>t(e))},[o,l,t]),null}let j=e=>!!e.onSelectionChangeHandlers;function D(e){let{onSelectionChange:t}=e,n=f(j);return t||n?(0,l.jsx)(P,{onSelectionChange:t}):null}let _=[0,0],A={x:0,y:0,zoom:1},R=["nodes","edges","defaultNodes","defaultEdges","onConnect","onConnectStart","onConnectEnd","onClickConnectStart","onClickConnectEnd","nodesDraggable","autoPanOnNodeFocus","nodesConnectable","nodesFocusable","edgesFocusable","edgesReconnectable","elevateNodesOnSelect","elevateEdgesOnSelect","minZoom","maxZoom","nodeExtent","onNodesChange","onEdgesChange","elementsSelectable","connectionMode","snapGrid","snapToGrid","translateExtent","connectOnClick","defaultEdgeOptions","fitView","fitViewOptions","onNodesDelete","onEdgesDelete","onDelete","onNodeDrag","onNodeDragStart","onNodeDragStop","onSelectionDrag","onSelectionDragStart","onSelectionDragStop","onMoveStart","onMove","onMoveEnd","noPanClassName","nodeOrigin","autoPanOnConnect","autoPanOnNodeDrag","onError","connectionRadius","isValidConnection","selectNodesOnDrag","nodeDragThreshold","connectionDragThreshold","onBeforeDelete","debug","autoPanSpeed","paneClickDistance","ariaLabelConfig","rfId"],O=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setPaneClickDistance:e.setPaneClickDistance}),I={translateExtent:i.ZO,nodeOrigin:_,minZoom:.5,maxZoom:2,elementsSelectable:!0,noPanClassName:"nopan",rfId:"1",paneClickDistance:0};function L(e){let{setNodes:t,setEdges:n,setMinZoom:o,setMaxZoom:l,setTranslateExtent:a,setNodeExtent:s,reset:c,setDefaultNodesAndEdges:u,setPaneClickDistance:g}=f(O,d.x),m=p();(0,r.useEffect)(()=>(u(e.defaultNodes,e.defaultEdges),()=>{h.current=I,c()}),[]);let h=(0,r.useRef)(I);return(0,r.useEffect)(()=>{for(let r of R){let d=e[r];d!==h.current[r]&&void 0!==e[r]&&("nodes"===r?t(d):"edges"===r?n(d):"minZoom"===r?o(d):"maxZoom"===r?l(d):"translateExtent"===r?a(d):"nodeExtent"===r?s(d):"paneClickDistance"===r?g(d):"ariaLabelConfig"===r?m.setState({ariaLabelConfig:(0,i.Q6)(d)}):"fitView"===r?m.setState({fitViewQueued:d}):"fitViewOptions"===r?m.setState({fitViewOptions:d}):m.setState({[r]:d}))}h.current=e},R.map(t=>e[t])),null}function z(){return"undefined"!=typeof window&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null}let B="undefined"!=typeof document?document:null;function V(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{target:B,actInsideInputWithModifier:!0},[n,o]=(0,r.useState)(!1),l=(0,r.useRef)(!1),a=(0,r.useRef)(new Set([])),[s,d]=(0,r.useMemo)(()=>{if(null!==e){let t=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.replace("+","\n").replace("\n\n","\n+").split("\n")),n=t.reduce((e,t)=>e.concat(...t),[]);return[t,n]}return[[],[]]},[e]);return(0,r.useEffect)(()=>{var n,r;let c=null!=(n=null==t?void 0:t.target)?n:B,u=null==(r=null==t?void 0:t.actInsideInputWithModifier)||r;if(null!==e){let e=e=>{if(l.current=e.ctrlKey||e.metaKey||e.shiftKey||e.altKey,(!l.current||l.current&&!u)&&(0,i.v5)(e))return!1;let n=T(e.code,d);if(a.current.add(e[n]),H(s,a.current,!1)){var r,c;let n=(null==(c=e.composedPath)||null==(r=c.call(e))?void 0:r[0])||e.target,a=(null==n?void 0:n.nodeName)==="BUTTON"||(null==n?void 0:n.nodeName)==="A";!1!==t.preventDefault&&(l.current||!a)&&e.preventDefault(),o(!0)}},n=e=>{let t=T(e.code,d);H(s,a.current,!0)?(o(!1),a.current.clear()):a.current.delete(e[t]),"Meta"===e.key&&a.current.clear(),l.current=!1},r=()=>{a.current.clear(),o(!1)};return null==c||c.addEventListener("keydown",e),null==c||c.addEventListener("keyup",n),window.addEventListener("blur",r),window.addEventListener("contextmenu",r),()=>{null==c||c.removeEventListener("keydown",e),null==c||c.removeEventListener("keyup",n),window.removeEventListener("blur",r),window.removeEventListener("contextmenu",r)}}},[e,o]),n}function H(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function T(e,t){return t.includes(e)?"code":"key"}let F=()=>{let e=p();return(0,r.useMemo)(()=>({zoomIn:t=>{let{panZoom:n}=e.getState();return n?n.scaleBy(1.2,{duration:null==t?void 0:t.duration}):Promise.resolve(!1)},zoomOut:t=>{let{panZoom:n}=e.getState();return n?n.scaleBy(1/1.2,{duration:null==t?void 0:t.duration}):Promise.resolve(!1)},zoomTo:(t,n)=>{let{panZoom:o}=e.getState();return o?o.scaleTo(t,{duration:null==n?void 0:n.duration}):Promise.resolve(!1)},getZoom:()=>e.getState().transform[2],setViewport:async(t,n)=>{var o,l,r;let{transform:[a,i,s],panZoom:d}=e.getState();return d?(await d.setViewport({x:null!=(o=t.x)?o:a,y:null!=(l=t.y)?l:i,zoom:null!=(r=t.zoom)?r:s},n),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>{let[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},setCenter:async(t,n,o)=>e.getState().setCenter(t,n,o),fitBounds:async(t,n)=>{var o;let{width:l,height:r,minZoom:a,maxZoom:s,panZoom:d}=e.getState(),c=(0,i.R4)(t,l,r,a,s,null!=(o=null==n?void 0:n.padding)?o:.1);return d?(await d.setViewport(c,{duration:null==n?void 0:n.duration,ease:null==n?void 0:n.ease,interpolate:null==n?void 0:n.interpolate}),Promise.resolve(!0)):Promise.resolve(!1)},screenToFlowPosition:function(t){var n,o;let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{transform:r,snapGrid:a,snapToGrid:s,domNode:d}=e.getState();if(!d)return t;let{x:c,y:u}=d.getBoundingClientRect(),g={x:t.x-c,y:t.y-u},f=null!=(n=l.snapGrid)?n:a,p=null!=(o=l.snapToGrid)?o:s;return(0,i.Ff)(g,r,p,f)},flowToScreenPosition:t=>{let{transform:n,domNode:o}=e.getState();if(!o)return t;let{x:l,y:r}=o.getBoundingClientRect(),a=(0,i.zj)(t,n);return{x:a.x+l,y:a.y+r}}}),[])};function Z(e,t){let n=[],o=new Map,l=[];for(let t of e)if("add"===t.type){l.push(t);continue}else if("remove"===t.type||"replace"===t.type)o.set(t.id,[t]);else{let e=o.get(t.id);e?e.push(t):o.set(t.id,[t])}for(let e of t){let t=o.get(e.id);if(!t){n.push(e);continue}if("remove"===t[0].type)continue;if("replace"===t[0].type){n.push({...t[0].item});continue}let l={...e};for(let e of t){var r,a=e,i=l;switch(a.type){case"select":i.selected=a.selected;break;case"position":void 0!==a.position&&(i.position=a.position),void 0!==a.dragging&&(i.dragging=a.dragging);break;case"dimensions":if(void 0!==a.dimensions){null!=i.measured||(i.measured={}),i.measured.width=a.dimensions.width,i.measured.height=a.dimensions.height,a.setAttributes&&((!0===a.setAttributes||"width"===a.setAttributes)&&(i.width=a.dimensions.width),(!0===a.setAttributes||"height"===a.setAttributes)&&(i.height=a.dimensions.height))}"boolean"==typeof a.resizing&&(i.resizing=a.resizing)}}n.push(l)}return l.length&&l.forEach(e=>{void 0!==e.index?n.splice(e.index,0,{...e.item}):n.push({...e.item})}),n}function X(e,t){return{id:e,type:"select",selected:t}}function W(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=[];for(let[l,r]of e){let e=t.has(l);(void 0!==r.selected||e)&&r.selected!==e&&(n&&(r.selected=e),o.push(X(r.id,e)))}return o}function K(e){let{items:t=[],lookup:n}=e,o=[],l=new Map(t.map(e=>[e.id,e]));for(let[e,l]of t.entries()){var r,a;let t=n.get(l.id),i=null!=(a=null==t||null==(r=t.internals)?void 0:r.userNode)?a:t;void 0!==i&&i!==l&&o.push({id:l.id,item:l,type:"replace"}),void 0===i&&o.push({item:l,type:"add",index:e})}for(let[e]of n)void 0===l.get(e)&&o.push({id:e,type:"remove"});return o}function Y(e){return{id:e.id,type:"remove"}}let Q=e=>(0,i.oB)(e),U=e=>(0,i.b$)(e);function G(e){return(0,r.forwardRef)(e)}let q="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;function $(e){let[t,n]=(0,r.useState)(BigInt(0)),[o]=(0,r.useState)(()=>{var e;let t;return e=()=>n(e=>e+BigInt(1)),t=[],{get:()=>t,reset:()=>{t=[]},push:n=>{t.push(n),e()}}});return q(()=>{let t=o.get();t.length&&(e(t),o.reset())},[t]),o}let J=(0,r.createContext)(null);function ee(e){let{children:t}=e,n=p(),o=$((0,r.useCallback)(e=>{let{nodes:t=[],setNodes:o,hasDefaultNodes:l,onNodesChange:r,nodeLookup:a,fitViewQueued:i}=n.getState(),s=t;for(let t of e)s="function"==typeof t?t(s):t;let d=K({items:s,lookup:a});l&&o(s),d.length>0?null==r||r(d):i&&window.requestAnimationFrame(()=>{let{fitViewQueued:e,nodes:t,setNodes:o}=n.getState();e&&o(t)})},[])),a=$((0,r.useCallback)(e=>{let{edges:t=[],setEdges:o,hasDefaultEdges:l,onEdgesChange:r,edgeLookup:a}=n.getState(),i=t;for(let t of e)i="function"==typeof t?t(i):t;l?o(i):r&&r(K({items:i,lookup:a}))},[])),i=(0,r.useMemo)(()=>({nodeQueue:o,edgeQueue:a}),[]);return(0,l.jsx)(J.Provider,{value:i,children:t})}let et=e=>!!e.panZoom;function en(){let e=F(),t=p(),n=function(){let e=(0,r.useContext)(J);if(!e)throw Error("useBatchContext must be used within a BatchProvider");return e}(),o=f(et),l=(0,r.useMemo)(()=>{let e=e=>t.getState().nodeLookup.get(e),o=e=>{n.nodeQueue.push(e)},l=e=>{n.edgeQueue.push(e)},r=e=>{var n,o,l,r;let{nodeLookup:a,nodeOrigin:s}=t.getState(),d=Q(e)?e:a.get(e.id),c=d.parentId?(0,i.us)(d.position,d.measured,d.parentId,a,s):d.position,u={...d,position:c,width:null!=(l=null==(n=d.measured)?void 0:n.width)?l:d.width,height:null!=(r=null==(o=d.measured)?void 0:o.height)?r:d.height};return(0,i.kM)(u)},a=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{replace:!1};o(o=>o.map(o=>{if(o.id===e){let e="function"==typeof t?t(o):t;return n.replace&&Q(e)?e:{...o,...e}}return o}))},s=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{replace:!1};l(o=>o.map(o=>{if(o.id===e){let e="function"==typeof t?t(o):t;return n.replace&&U(e)?e:{...o,...e}}return o}))};return{getNodes:()=>t.getState().nodes.map(e=>({...e})),getNode:t=>{var n;return null==(n=e(t))?void 0:n.internals.userNode},getInternalNode:e,getEdges:()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},getEdge:e=>t.getState().edgeLookup.get(e),setNodes:o,setEdges:l,addNodes:e=>{let t=Array.isArray(e)?e:[e];n.nodeQueue.push(e=>[...e,...t])},addEdges:e=>{let t=Array.isArray(e)?e:[e];n.edgeQueue.push(e=>[...e,...t])},toObject:()=>{let{nodes:e=[],edges:n=[],transform:o}=t.getState(),[l,r,a]=o;return{nodes:e.map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:l,y:r,zoom:a}}},deleteElements:async e=>{let{nodes:n=[],edges:o=[]}=e,{nodes:l,edges:r,onNodesDelete:a,onEdgesDelete:s,triggerNodeChanges:d,triggerEdgeChanges:c,onDelete:u,onBeforeDelete:g}=t.getState(),{nodes:f,edges:p}=await (0,i.Tq)({nodesToRemove:n,edgesToRemove:o,nodes:l,edges:r,onBeforeDelete:g}),m=p.length>0,h=f.length>0;if(m){let e=p.map(Y);null==s||s(p),c(e)}if(h){let e=f.map(Y);null==a||a(f),d(e)}return(h||m)&&(null==u||u({nodes:f,edges:p})),{deletedNodes:f,deletedEdges:p}},getIntersectingNodes:function(e){let n=!(arguments.length>1)||void 0===arguments[1]||arguments[1],o=arguments.length>2?arguments[2]:void 0,l=(0,i.mW)(e),a=l?e:r(e),s=void 0!==o;return a?(o||t.getState().nodes).filter(o=>{let r=t.getState().nodeLookup.get(o.id);if(r&&!l&&(o.id===e.id||!r.internals.positionAbsolute))return!1;let d=(0,i.kM)(s?o:r),c=(0,i.X6)(d,a);return n&&c>0||c>=a.width*a.height}):[]},isNodeIntersecting:function(e,t){let n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o=(0,i.mW)(e)?e:r(e);if(!o)return!1;let l=(0,i.X6)(o,t);return n&&l>0||l>=o.width*o.height},updateNode:a,updateNodeData:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{replace:!1};a(e,e=>{let o="function"==typeof t?t(e):t;return n.replace?{...e,data:o}:{...e,data:{...e.data,...o}}},n)},updateEdge:s,updateEdgeData:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{replace:!1};s(e,e=>{let o="function"==typeof t?t(e):t;return n.replace?{...e,data:o}:{...e,data:{...e.data,...o}}},n)},getNodesBounds:e=>{let{nodeLookup:n,nodeOrigin:o}=t.getState();return(0,i.Jo)(e,{nodeLookup:n,nodeOrigin:o})},getHandleConnections:e=>{var n,o;let{type:l,id:r,nodeId:a}=e;return Array.from(null!=(o=null==(n=t.getState().connectionLookup.get("".concat(a,"-").concat(l).concat(r?"-".concat(r):"")))?void 0:n.values())?o:[])},getNodeConnections:e=>{var n,o;let{type:l,handleId:r,nodeId:a}=e;return Array.from(null!=(o=null==(n=t.getState().connectionLookup.get("".concat(a).concat(l?r?"-".concat(l,"-").concat(r):"-".concat(l):"")))?void 0:n.values())?o:[])},fitView:async e=>{var o;let l=null!=(o=t.getState().fitViewResolver)?o:(0,i.YN)();return t.setState({fitViewQueued:!0,fitViewOptions:e,fitViewResolver:l}),n.nodeQueue.push(e=>[...e]),l.promise}}},[]);return(0,r.useMemo)(()=>({...l,...e,viewportInitialized:o}),[o])}let eo=e=>e.selected,el="undefined"!=typeof window?window:void 0,er={position:"absolute",width:"100%",height:"100%",top:0,left:0},ea=e=>({userSelectionActive:e.userSelectionActive,lib:e.lib});function ei(e){let{onPaneContextMenu:t,zoomOnScroll:n=!0,zoomOnPinch:o=!0,panOnScroll:a=!1,panOnScrollSpeed:s=.5,panOnScrollMode:c=i.ny.Free,zoomOnDoubleClick:u=!0,panOnDrag:g=!0,defaultViewport:m,translateExtent:h,minZoom:v,maxZoom:y,zoomActivationKeyCode:b,preventScrolling:x=!0,children:w,noWheelClassName:S,noPanClassName:C,onViewportChange:E,isControlledViewport:k,paneClickDistance:N}=e,M=p(),P=(0,r.useRef)(null),{userSelectionActive:j,lib:D}=f(ea,d.x),_=V(b),A=(0,r.useRef)(),R=p();(0,r.useEffect)(()=>{let e=()=>{if(!P.current)return!1;let e=(0,i.Eo)(P.current);if(0===e.height||0===e.width){var t,n;null==(t=(n=R.getState()).onError)||t.call(n,"004",i.xc.error004())}R.setState({width:e.width||500,height:e.height||500})};if(P.current){e(),window.addEventListener("resize",e);let t=new ResizeObserver(()=>e());return t.observe(P.current),()=>{window.removeEventListener("resize",e),t&&P.current&&t.unobserve(P.current)}}},[]);let O=(0,r.useCallback)(e=>{null==E||E({x:e[0],y:e[1],zoom:e[2]}),k||M.setState({transform:e})},[E,k]);return(0,r.useEffect)(()=>{if(P.current){A.current=(0,i.kO)({domNode:P.current,minZoom:v,maxZoom:y,translateExtent:h,viewport:m,paneClickDistance:N,onDraggingChange:e=>M.setState({paneDragging:e}),onPanZoomStart:(e,t)=>{let{onViewportChangeStart:n,onMoveStart:o}=M.getState();null==o||o(e,t),null==n||n(t)},onPanZoom:(e,t)=>{let{onViewportChange:n,onMove:o}=M.getState();null==o||o(e,t),null==n||n(t)},onPanZoomEnd:(e,t)=>{let{onViewportChangeEnd:n,onMoveEnd:o}=M.getState();null==o||o(e,t),null==n||n(t)}});let{x:e,y:t,zoom:n}=A.current.getViewport();return M.setState({panZoom:A.current,transform:[e,t,n],domNode:P.current.closest(".react-flow")}),()=>{var e;null==(e=A.current)||e.destroy()}}},[]),(0,r.useEffect)(()=>{var e;null==(e=A.current)||e.update({onPaneContextMenu:t,zoomOnScroll:n,zoomOnPinch:o,panOnScroll:a,panOnScrollSpeed:s,panOnScrollMode:c,zoomOnDoubleClick:u,panOnDrag:g,zoomActivationKeyPressed:_,preventScrolling:x,noPanClassName:C,userSelectionActive:j,noWheelClassName:S,lib:D,onTransformChange:O})},[t,n,o,a,s,c,u,g,_,x,C,j,S,D,O]),(0,l.jsx)("div",{className:"react-flow__renderer",ref:P,style:er,children:w})}let es=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function ed(){let{userSelectionActive:e,userSelectionRect:t}=f(es,d.x);return e&&t?(0,l.jsx)("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:"translate(".concat(t.x,"px, ").concat(t.y,"px)")}}):null}let ec=(e,t)=>n=>{n.target===t.current&&(null==e||e(n))},eu=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,connectionInProgress:e.connection.inProgress,dragging:e.paneDragging});function eg(e){let{isSelecting:t,selectionKeyPressed:n,selectionMode:o=i.Qc.Full,panOnDrag:s,selectionOnDrag:c,onSelectionStart:u,onSelectionEnd:g,onPaneClick:m,onPaneContextMenu:h,onPaneScroll:v,onPaneMouseEnter:y,onPaneMouseMove:b,onPaneMouseLeave:x,children:w}=e,S=p(),{userSelectionActive:C,elementsSelectable:E,dragging:k,connectionInProgress:N}=f(eu,d.x),M=E&&(t||C),P=(0,r.useRef)(null),j=(0,r.useRef)(),D=(0,r.useRef)(new Set),_=(0,r.useRef)(new Set),A=(0,r.useRef)(!1),R=(0,r.useRef)(!1),O=e=>{if(A.current||N){A.current=!1;return}null==m||m(e),S.getState().resetSelectedElements(),S.setState({nodesSelectionActive:!1})},I=!0===s||Array.isArray(s)&&s.includes(0);return(0,l.jsxs)("div",{className:(0,a.A)(["react-flow__pane",{draggable:I,dragging:k,selection:t}]),onClick:M?void 0:ec(O,P),onContextMenu:ec(e=>{if(Array.isArray(s)&&(null==s?void 0:s.includes(2)))return void e.preventDefault();null==h||h(e)},P),onWheel:ec(v?e=>v(e):void 0,P),onPointerEnter:M?void 0:y,onPointerDown:M?e=>{var n,o;let{resetSelectedElements:l,domNode:r}=S.getState();if(j.current=null==r?void 0:r.getBoundingClientRect(),!E||!t||0!==e.button||e.target!==P.current||!j.current)return;null==(o=e.target)||null==(n=o.setPointerCapture)||n.call(o,e.pointerId),R.current=!0,A.current=!1;let{x:a,y:s}=(0,i.q1)(e.nativeEvent,j.current);l(),S.setState({userSelectionRect:{width:0,height:0,startX:a,startY:s,x:a,y:s}}),null==u||u(e)}:b,onPointerMove:M?e=>{var t,n;let{userSelectionRect:l,transform:r,nodeLookup:a,edgeLookup:s,connectionLookup:d,triggerNodeChanges:c,triggerEdgeChanges:u,defaultEdgeOptions:g}=S.getState();if(!j.current||!l)return;A.current=!0;let{x:f,y:p}=(0,i.q1)(e.nativeEvent,j.current),{startX:m,startY:h}=l,v={startX:m,startY:h,x:f<m?f:m,y:p<h?p:h,width:Math.abs(f-m),height:Math.abs(p-h)},y=D.current,b=_.current;D.current=new Set((0,i.U$)(a,v,r,o===i.Qc.Partial,!0).map(e=>e.id)),_.current=new Set;let x=null==(t=null==g?void 0:g.selectable)||t;for(let e of D.current){let t=d.get(e);if(t)for(let{edgeId:e}of t.values()){let t=s.get(e);t&&(null!=(n=t.selectable)?n:x)&&_.current.add(e)}}(0,i._s)(y,D.current)||c(W(a,D.current,!0)),(0,i._s)(b,_.current)||u(W(s,_.current)),S.setState({userSelectionRect:v,userSelectionActive:!0,nodesSelectionActive:!1})}:b,onPointerUp:M?e=>{var t,o;if(0!==e.button||!R.current)return;null==(o=e.target)||null==(t=o.releasePointerCapture)||t.call(o,e.pointerId);let{userSelectionRect:l}=S.getState();!C&&l&&e.target===P.current&&(null==O||O(e)),S.setState({userSelectionActive:!1,userSelectionRect:null,nodesSelectionActive:D.current.size>0}),null==g||g(e),(n||c)&&(A.current=!1),R.current=!1}:void 0,onPointerLeave:x,ref:P,style:er,children:[w,(0,l.jsx)(ed,{})]})}function ef(e){let{id:t,store:n,unselect:o=!1,nodeRef:l}=e,{addSelectedNodes:r,unselectNodesAndEdges:a,multiSelectionActive:s,nodeLookup:d,onError:c}=n.getState(),u=d.get(t);if(!u){null==c||c("012",i.xc.error012(t));return}n.setState({nodesSelectionActive:!1}),u.selected?(o||u.selected&&s)&&(a({nodes:[u],edges:[]}),requestAnimationFrame(()=>{var e;return null==l||null==(e=l.current)?void 0:e.blur()})):r([t])}function ep(e){let{nodeRef:t,disabled:n=!1,noDragClassName:o,handleSelector:l,nodeId:a,isSelectable:s,nodeClickDistance:d}=e,c=p(),[u,g]=(0,r.useState)(!1),f=(0,r.useRef)();return(0,r.useEffect)(()=>{f.current=(0,i.I$)({getStoreItems:()=>c.getState(),onNodeMouseDown:e=>{ef({id:e,store:c,nodeRef:t})},onDragStart:()=>{g(!0)},onDragStop:()=>{g(!1)}})},[]),(0,r.useEffect)(()=>{var e,r;if(n)null==(e=f.current)||e.destroy();else if(t.current)return null==(r=f.current)||r.update({noDragClassName:o,handleSelector:l,domNode:t.current,isSelectable:s,nodeId:a,nodeClickDistance:d}),()=>{var e;null==(e=f.current)||e.destroy()}},[o,l,n,s,t,a]),u}let em=e=>t=>t.selected&&(t.draggable||e&&void 0===t.draggable);function eh(){let e=p();return(0,r.useCallback)(t=>{let{nodeExtent:n,snapToGrid:o,snapGrid:l,nodesDraggable:r,onError:a,updateNodePositions:s,nodeLookup:d,nodeOrigin:c}=e.getState(),u=new Map,g=em(r),f=o?l[0]:5,p=o?l[1]:5,m=t.direction.x*f*t.factor,h=t.direction.y*p*t.factor;for(let[,e]of d){if(!g(e))continue;let t={x:e.internals.positionAbsolute.x+m,y:e.internals.positionAbsolute.y+h};o&&(t=(0,i.s_)(t,l));let{position:r,positionAbsolute:s}=(0,i.aE)({nodeId:e.id,nextPosition:t,nodeLookup:d,nodeExtent:n,nodeOrigin:c,onError:a});e.position=r,e.internals.positionAbsolute=s,u.set(e.id,e)}s(u)},[])}let ev=(0,r.createContext)(null),ey=ev.Provider;ev.Consumer;let eb=()=>(0,r.useContext)(ev),ex=e=>({connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName,rfId:e.rfId}),ew=(e,t,n)=>o=>{let{connectionClickStartHandle:l,connectionMode:r,connection:a}=o,{fromHandle:s,toHandle:d,isValid:c}=a,u=(null==d?void 0:d.nodeId)===e&&(null==d?void 0:d.id)===t&&(null==d?void 0:d.type)===n;return{connectingFrom:(null==s?void 0:s.nodeId)===e&&(null==s?void 0:s.id)===t&&(null==s?void 0:s.type)===n,connectingTo:u,clickConnecting:(null==l?void 0:l.nodeId)===e&&(null==l?void 0:l.id)===t&&(null==l?void 0:l.type)===n,isPossibleEndHandle:r===i.WZ.Strict?(null==s?void 0:s.type)!==n:e!==(null==s?void 0:s.nodeId)||t!==(null==s?void 0:s.id),connectionInProcess:!!s,clickConnectionInProcess:!!l,valid:u&&c}},eS=(0,r.memo)(G(function(e,t){let{type:n="source",position:o=i.yX.Top,isValidConnection:r,isConnectable:s=!0,isConnectableStart:c=!0,isConnectableEnd:u=!0,id:g,onConnect:m,children:h,className:v,onMouseDown:y,onTouchStart:b,...x}=e,w=g||null,S="target"===n,C=p(),E=eb(),{connectOnClick:k,noPanClassName:N,rfId:M}=f(ex,d.x),{connectingFrom:P,connectingTo:j,clickConnecting:D,isPossibleEndHandle:_,connectionInProcess:A,clickConnectionInProcess:R,valid:O}=f(ew(E,w,n),d.x);if(!E){var I,L;null==(I=(L=C.getState()).onError)||I.call(L,"010",i.xc.error010())}let z=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=C.getState(),l={...t,...e};if(o){let{edges:e,setEdges:t}=C.getState();t((0,i.rN)(l,e))}null==n||n(l),null==m||m(l)},B=e=>{if(!E)return;let t=(0,i.Er)(e.nativeEvent);if(c&&(t&&0===e.button||!t)){let t=C.getState();i.aQ.onPointerDown(e.nativeEvent,{autoPanOnConnect:t.autoPanOnConnect,connectionMode:t.connectionMode,connectionRadius:t.connectionRadius,domNode:t.domNode,nodeLookup:t.nodeLookup,lib:t.lib,isTarget:S,handleId:w,nodeId:E,flowId:t.rfId,panBy:t.panBy,cancelConnection:t.cancelConnection,onConnectStart:t.onConnectStart,onConnectEnd:t.onConnectEnd,updateConnection:t.updateConnection,onConnect:z,isValidConnection:r||t.isValidConnection,getTransform:()=>C.getState().transform,getFromHandle:()=>C.getState().connection.fromHandle,autoPanSpeed:t.autoPanSpeed,dragThreshold:t.connectionDragThreshold})}t?null==y||y(e):null==b||b(e)};return(0,l.jsx)("div",{"data-handleid":w,"data-nodeid":E,"data-handlepos":o,"data-id":"".concat(M,"-").concat(E,"-").concat(w,"-").concat(n),className:(0,a.A)(["react-flow__handle","react-flow__handle-".concat(o),"nodrag",N,v,{source:!S,target:S,connectable:s,connectablestart:c,connectableend:u,clickconnecting:D,connectingfrom:P,connectingto:j,valid:O,connectionindicator:s&&(!A||_)&&(A||R?u:c)}]),onMouseDown:B,onTouchStart:B,onClick:k?e=>{let{onClickConnectStart:t,onClickConnectEnd:o,connectionClickStartHandle:l,connectionMode:a,isValidConnection:s,lib:d,rfId:u,nodeLookup:g,connection:f}=C.getState();if(!E||!l&&!c)return;if(!l){null==t||t(e.nativeEvent,{nodeId:E,handleId:w,handleType:n}),C.setState({connectionClickStartHandle:{nodeId:E,type:n,id:w}});return}let p=(0,i.oj)(e.target),m=r||s,{connection:h,isValid:v}=i.aQ.isValid(e.nativeEvent,{handle:{nodeId:E,id:w,type:n},connectionMode:a,fromNodeId:l.nodeId,fromHandleId:l.id||null,fromType:l.type,isValidConnection:m,flowId:u,doc:p,lib:d,nodeLookup:g});v&&h&&z(h);let y=structuredClone(f);delete y.inProgress,y.toPosition=y.toHandle?y.toHandle.position:null,null==o||o(e,y),C.setState({connectionClickStartHandle:null})}:void 0,ref:t,...x,children:h})})),eC={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}},eE={input:function(e){let{data:t,isConnectable:n,sourcePosition:o=i.yX.Bottom}=e;return(0,l.jsxs)(l.Fragment,{children:[null==t?void 0:t.label,(0,l.jsx)(eS,{type:"source",position:o,isConnectable:n})]})},default:function(e){let{data:t,isConnectable:n,targetPosition:o=i.yX.Top,sourcePosition:r=i.yX.Bottom}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(eS,{type:"target",position:o,isConnectable:n}),null==t?void 0:t.label,(0,l.jsx)(eS,{type:"source",position:r,isConnectable:n})]})},output:function(e){let{data:t,isConnectable:n,targetPosition:o=i.yX.Top}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(eS,{type:"target",position:o,isConnectable:n}),null==t?void 0:t.label]})},group:function(){return null}},ek=e=>{let{width:t,height:n,x:o,y:l}=(0,i.aZ)(e.nodeLookup,{filter:e=>!!e.selected});return{width:(0,i.kf)(t)?t:null,height:(0,i.kf)(n)?n:null,userSelectionActive:e.userSelectionActive,transformString:"translate(".concat(e.transform[0],"px,").concat(e.transform[1],"px) scale(").concat(e.transform[2],") translate(").concat(o,"px,").concat(l,"px)")}};function eN(e){let{onSelectionContextMenu:t,noPanClassName:n,disableKeyboardA11y:o}=e,i=p(),{width:s,height:c,transformString:u,userSelectionActive:g}=f(ek,d.x),m=eh(),h=(0,r.useRef)(null);if((0,r.useEffect)(()=>{if(!o){var e;null==(e=h.current)||e.focus({preventScroll:!0})}},[o]),ep({nodeRef:h}),g||!s||!c)return null;let v=t?e=>{t(e,i.getState().nodes.filter(e=>e.selected))}:void 0;return(0,l.jsx)("div",{className:(0,a.A)(["react-flow__nodesselection","react-flow__container",n]),style:{transform:u},children:(0,l.jsx)("div",{ref:h,className:"react-flow__nodesselection-rect",onContextMenu:v,tabIndex:o?void 0:-1,onKeyDown:o?void 0:e=>{Object.prototype.hasOwnProperty.call(eC,e.key)&&(e.preventDefault(),m({direction:eC[e.key],factor:e.shiftKey?4:1}))},style:{width:s,height:c}})})}let eM="undefined"!=typeof window?window:void 0,eP=e=>({nodesSelectionActive:e.nodesSelectionActive,userSelectionActive:e.userSelectionActive});function ej(e){let{children:t,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:a,onPaneMouseLeave:i,onPaneContextMenu:s,onPaneScroll:d,paneClickDistance:c,deleteKeyCode:u,selectionKeyCode:g,selectionOnDrag:m,selectionMode:h,onSelectionStart:v,onSelectionEnd:y,multiSelectionKeyCode:b,panActivationKeyCode:x,zoomActivationKeyCode:w,elementsSelectable:S,zoomOnScroll:C,zoomOnPinch:E,panOnScroll:k,panOnScrollSpeed:N,panOnScrollMode:M,zoomOnDoubleClick:P,panOnDrag:j,defaultViewport:D,translateExtent:_,minZoom:A,maxZoom:R,preventScrolling:O,onSelectionContextMenu:I,noWheelClassName:L,noPanClassName:z,disableKeyboardA11y:B,onViewportChange:H,isControlledViewport:T}=e,{nodesSelectionActive:F,userSelectionActive:Z}=f(eP),X=V(g,{target:eM}),W=V(x,{target:eM}),K=W||j,Y=W||k,Q=m&&!0!==K,U=X||Z||Q;return!function(e){let{deleteKeyCode:t,multiSelectionKeyCode:n}=e,o=p(),{deleteElements:l}=en(),a=V(t,{actInsideInputWithModifier:!1}),i=V(n,{target:el});(0,r.useEffect)(()=>{if(a){let{edges:e,nodes:t}=o.getState();l({nodes:t.filter(eo),edges:e.filter(eo)}),o.setState({nodesSelectionActive:!1})}},[a]),(0,r.useEffect)(()=>{o.setState({multiSelectionActive:i})},[i])}({deleteKeyCode:u,multiSelectionKeyCode:b}),(0,l.jsx)(ei,{onPaneContextMenu:s,elementsSelectable:S,zoomOnScroll:C,zoomOnPinch:E,panOnScroll:Y,panOnScrollSpeed:N,panOnScrollMode:M,zoomOnDoubleClick:P,panOnDrag:!X&&K,defaultViewport:D,translateExtent:_,minZoom:A,maxZoom:R,zoomActivationKeyCode:w,preventScrolling:O,noWheelClassName:L,noPanClassName:z,onViewportChange:H,isControlledViewport:T,paneClickDistance:c,children:(0,l.jsxs)(eg,{onSelectionStart:v,onSelectionEnd:y,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:a,onPaneMouseLeave:i,onPaneContextMenu:s,onPaneScroll:d,panOnDrag:K,isSelecting:!!U,selectionMode:h,selectionKeyPressed:X,selectionOnDrag:Q,children:[t,F&&(0,l.jsx)(eN,{onSelectionContextMenu:I,noPanClassName:z,disableKeyboardA11y:B})]})})}ej.displayName="FlowRenderer";let eD=(0,r.memo)(ej),e_=e=>t=>e?(0,i.U$)(t.nodeLookup,{x:0,y:0,width:t.width,height:t.height},t.transform,!0).map(e=>e.id):Array.from(t.nodeLookup.keys()),eA=e=>e.updateNodeInternals;function eR(e){var t,n,o,s,c,u,g,m,h,y,b,x,w;let{id:S,onClick:C,onMouseEnter:E,onMouseMove:k,onMouseLeave:N,onContextMenu:M,onDoubleClick:P,nodesDraggable:j,elementsSelectable:D,nodesConnectable:_,nodesFocusable:A,resizeObserver:R,noDragClassName:O,noPanClassName:I,disableKeyboardA11y:L,rfId:z,nodeTypes:B,nodeClickDistance:V,onError:H}=e,{node:T,internals:F,isParent:Z}=f(e=>{let t=e.nodeLookup.get(S),n=e.parentLookup.has(S);return{node:t,internals:t.internals,isParent:n}},d.x),X=T.type||"default",W=(null==B?void 0:B[X])||eE[X];void 0===W&&(null==H||H("003",i.xc.error003(X)),X="default",W=eE.default);let K=!!(T.draggable||j&&void 0===T.draggable),Y=!!(T.selectable||D&&void 0===T.selectable),Q=!!(T.connectable||_&&void 0===T.connectable),U=!!(T.focusable||A&&void 0===T.focusable),G=p(),q=(0,i.QE)(T),$=function(e){let{node:t,nodeType:n,hasDimensions:o,resizeObserver:l}=e,a=p(),i=(0,r.useRef)(null),s=(0,r.useRef)(null),d=(0,r.useRef)(t.sourcePosition),c=(0,r.useRef)(t.targetPosition),u=(0,r.useRef)(n),g=o&&!!t.internals.handleBounds;return(0,r.useEffect)(()=>{!i.current||t.hidden||g&&s.current===i.current||(s.current&&(null==l||l.unobserve(s.current)),null==l||l.observe(i.current),s.current=i.current)},[g,t.hidden]),(0,r.useEffect)(()=>()=>{s.current&&(null==l||l.unobserve(s.current),s.current=null)},[]),(0,r.useEffect)(()=>{if(i.current){let e=u.current!==n,o=d.current!==t.sourcePosition,l=c.current!==t.targetPosition;(e||o||l)&&(u.current=n,d.current=t.sourcePosition,c.current=t.targetPosition,a.getState().updateNodeInternals(new Map([[t.id,{id:t.id,nodeElement:i.current,force:!0}]])))}},[t.id,n,t.sourcePosition,t.targetPosition]),i}({node:T,nodeType:X,hasDimensions:q,resizeObserver:R}),J=ep({nodeRef:$,disabled:T.hidden||!K,noDragClassName:O,handleSelector:T.dragHandle,nodeId:S,isSelectable:Y,nodeClickDistance:V}),ee=eh();if(T.hidden)return null;let et=(0,i.uD)(T),en=void 0===T.internals.handleBounds?{width:null!=(h=null!=(m=T.width)?m:T.initialWidth)?h:null==(u=T.style)?void 0:u.width,height:null!=(b=null!=(y=T.height)?y:T.initialHeight)?b:null==(g=T.style)?void 0:g.height}:{width:null!=(x=T.width)?x:null==(s=T.style)?void 0:s.width,height:null!=(w=T.height)?w:null==(c=T.style)?void 0:c.height},eo=Y||K||C||E||k||N,el=E?e=>E(e,{...F.userNode}):void 0,er=k?e=>k(e,{...F.userNode}):void 0,ea=N?e=>N(e,{...F.userNode}):void 0,ei=M?e=>M(e,{...F.userNode}):void 0,es=P?e=>P(e,{...F.userNode}):void 0;return(0,l.jsx)("div",{className:(0,a.A)(["react-flow__node","react-flow__node-".concat(X),{[I]:K},T.className,{selected:T.selected,selectable:Y,parent:Z,draggable:K,dragging:J}]),ref:$,style:{zIndex:F.z,transform:"translate(".concat(F.positionAbsolute.x,"px,").concat(F.positionAbsolute.y,"px)"),pointerEvents:eo?"all":"none",visibility:q?"visible":"hidden",...T.style,...en},"data-id":S,"data-testid":"rf__node-".concat(S),onMouseEnter:el,onMouseMove:er,onMouseLeave:ea,onContextMenu:ei,onClick:e=>{let{selectNodesOnDrag:t,nodeDragThreshold:n}=G.getState();Y&&(!t||!K||n>0)&&ef({id:S,store:G,nodeRef:$}),C&&C(e,{...F.userNode})},onDoubleClick:es,onKeyDown:U?e=>{if(!(0,i.v5)(e.nativeEvent)&&!L){if(i.tn.includes(e.key)&&Y)ef({id:S,store:G,unselect:"Escape"===e.key,nodeRef:$});else if(K&&T.selected&&Object.prototype.hasOwnProperty.call(eC,e.key)){e.preventDefault();let{ariaLabelConfig:t}=G.getState();G.setState({ariaLiveMessage:t["node.a11yDescription.ariaLiveMessage"]({direction:e.key.replace("Arrow","").toLowerCase(),x:~~F.positionAbsolute.x,y:~~F.positionAbsolute.y})}),ee({direction:eC[e.key],factor:e.shiftKey?4:1})}}}:void 0,tabIndex:U?0:void 0,onFocus:U?()=>{var e;if(L||!(null==(e=$.current)?void 0:e.matches(":focus-visible")))return;let{transform:t,width:n,height:o,autoPanOnNodeFocus:l,setCenter:r}=G.getState();l&&((0,i.U$)(new Map([[S,T]]),{x:0,y:0,width:n,height:o},t,!0).length>0||r(T.position.x+et.width/2,T.position.y+et.height/2,{zoom:t[2]}))}:void 0,role:null!=(t=T.ariaRole)?t:U?"group":void 0,"aria-roledescription":"node","aria-describedby":L?void 0:"".concat(v,"-").concat(z),"aria-label":T.ariaLabel,...T.domAttributes,children:(0,l.jsx)(ey,{value:S,children:(0,l.jsx)(W,{id:S,data:T.data,type:X,positionAbsoluteX:F.positionAbsolute.x,positionAbsoluteY:F.positionAbsolute.y,selected:null!=(n=T.selected)&&n,selectable:Y,draggable:K,deletable:null==(o=T.deletable)||o,isConnectable:Q,sourcePosition:T.sourcePosition,targetPosition:T.targetPosition,dragging:J,dragHandle:T.dragHandle,zIndex:F.z,parentId:T.parentId,...et})})})}let eO=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,onError:e.onError});function eI(e){var t;let{nodesDraggable:n,nodesConnectable:o,nodesFocusable:a,elementsSelectable:i,onError:s}=f(eO,d.x),c=(t=e.onlyRenderVisibleElements,f((0,r.useCallback)(e_(t),[t]),d.x)),u=function(){let e=f(eA),[t]=(0,r.useState)(()=>"undefined"==typeof ResizeObserver?null:new ResizeObserver(t=>{let n=new Map;t.forEach(e=>{let t=e.target.getAttribute("data-id");n.set(t,{id:t,nodeElement:e.target,force:!0})}),e(n)}));return(0,r.useEffect)(()=>()=>{null==t||t.disconnect()},[t]),t}();return(0,l.jsx)("div",{className:"react-flow__nodes",style:er,children:c.map(t=>(0,l.jsx)(eR,{id:t,nodeTypes:e.nodeTypes,nodeExtent:e.nodeExtent,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,resizeObserver:u,nodesDraggable:n,nodesConnectable:o,nodesFocusable:a,elementsSelectable:i,nodeClickDistance:e.nodeClickDistance,onError:s},t))})}eI.displayName="NodeRenderer";let eL=(0,r.memo)(eI),ez={[i.TG.Arrow]:e=>{let{color:t="none",strokeWidth:n=1}=e;return(0,l.jsx)("polyline",{style:{stroke:t,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"})},[i.TG.ArrowClosed]:e=>{let{color:t="none",strokeWidth:n=1}=e;return(0,l.jsx)("polyline",{style:{stroke:t,fill:t,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})}},eB=e=>{let{id:t,type:n,color:o,width:a=12.5,height:s=12.5,markerUnits:d="strokeWidth",strokeWidth:c,orient:u="auto-start-reverse"}=e,g=function(e){let t=p();return(0,r.useMemo)(()=>{if(!Object.prototype.hasOwnProperty.call(ez,e)){var n,o;return null==(n=(o=t.getState()).onError)||n.call(o,"009",i.xc.error009(e)),null}return ez[e]},[e])}(n);return g?(0,l.jsx)("marker",{className:"react-flow__arrowhead",id:t,markerWidth:"".concat(a),markerHeight:"".concat(s),viewBox:"-10 -10 20 20",markerUnits:d,orient:u,refX:"0",refY:"0",children:(0,l.jsx)(g,{color:o,strokeWidth:c})}):null},eV=e=>{let{defaultColor:t,rfId:n}=e,o=f(e=>e.edges),a=f(e=>e.defaultEdgeOptions),s=(0,r.useMemo)(()=>(0,i.Hm)(o,{id:n,defaultColor:t,defaultMarkerStart:null==a?void 0:a.markerStart,defaultMarkerEnd:null==a?void 0:a.markerEnd}),[o,a,n,t]);return s.length?(0,l.jsx)("svg",{className:"react-flow__marker","aria-hidden":"true",children:(0,l.jsx)("defs",{children:s.map(e=>(0,l.jsx)(eB,{id:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient},e.id))})}):null};eV.displayName="MarkerDefinitions";var eH=(0,r.memo)(eV);function eT(e){let{x:t,y:n,label:o,labelStyle:i,labelShowBg:s=!0,labelBgStyle:d,labelBgPadding:c=[2,4],labelBgBorderRadius:u=2,children:g,className:f,...p}=e,[m,h]=(0,r.useState)({x:1,y:0,width:0,height:0}),v=(0,a.A)(["react-flow__edge-textwrapper",f]),y=(0,r.useRef)(null);return((0,r.useEffect)(()=>{if(y.current){let e=y.current.getBBox();h({x:e.x,y:e.y,width:e.width,height:e.height})}},[o]),o)?(0,l.jsxs)("g",{transform:"translate(".concat(t-m.width/2," ").concat(n-m.height/2,")"),className:v,visibility:m.width?"visible":"hidden",...p,children:[s&&(0,l.jsx)("rect",{width:m.width+2*c[0],x:-c[0],y:-c[1],height:m.height+2*c[1],className:"react-flow__edge-textbg",style:d,rx:u,ry:u}),(0,l.jsx)("text",{className:"react-flow__edge-text",y:m.height/2,dy:"0.3em",ref:y,style:i,children:o}),g]}):null}eT.displayName="EdgeText";let eF=(0,r.memo)(eT);function eZ(e){let{path:t,labelX:n,labelY:o,label:r,labelStyle:s,labelShowBg:d,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:g,interactionWidth:f=20,...p}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("path",{...p,d:t,fill:"none",className:(0,a.A)(["react-flow__edge-path",p.className])}),f&&(0,l.jsx)("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:f,className:"react-flow__edge-interaction"}),r&&(0,i.kf)(n)&&(0,i.kf)(o)?(0,l.jsx)(eF,{x:n,y:o,label:r,labelStyle:s,labelShowBg:d,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:g}):null]})}function eX(e){let{pos:t,x1:n,y1:o,x2:l,y2:r}=e;return t===i.yX.Left||t===i.yX.Right?[.5*(n+l),o]:[n,.5*(o+r)]}function eW(e){let{sourceX:t,sourceY:n,sourcePosition:o=i.yX.Bottom,targetX:l,targetY:r,targetPosition:a=i.yX.Top}=e,[s,d]=eX({pos:o,x1:t,y1:n,x2:l,y2:r}),[c,u]=eX({pos:a,x1:l,y1:r,x2:t,y2:n}),[g,f,p,m]=(0,i.e_)({sourceX:t,sourceY:n,targetX:l,targetY:r,sourceControlX:s,sourceControlY:d,targetControlX:c,targetControlY:u});return["M".concat(t,",").concat(n," C").concat(s,",").concat(d," ").concat(c,",").concat(u," ").concat(l,",").concat(r),g,f,p,m]}function eK(e){return(0,r.memo)(t=>{let{id:n,sourceX:o,sourceY:r,targetX:a,targetY:i,sourcePosition:s,targetPosition:d,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:f,labelBgPadding:p,labelBgBorderRadius:m,style:h,markerEnd:v,markerStart:y,interactionWidth:b}=t,[x,w,S]=eW({sourceX:o,sourceY:r,sourcePosition:s,targetX:a,targetY:i,targetPosition:d}),C=e.isInternal?void 0:n;return(0,l.jsx)(eZ,{id:C,path:x,labelX:w,labelY:S,label:c,labelStyle:u,labelShowBg:g,labelBgStyle:f,labelBgPadding:p,labelBgBorderRadius:m,style:h,markerEnd:v,markerStart:y,interactionWidth:b})})}let eY=eK({isInternal:!1}),eQ=eK({isInternal:!0});function eU(e){return(0,r.memo)(t=>{let{id:n,sourceX:o,sourceY:r,targetX:a,targetY:s,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:f,labelBgBorderRadius:p,style:m,sourcePosition:h=i.yX.Bottom,targetPosition:v=i.yX.Top,markerEnd:y,markerStart:b,pathOptions:x,interactionWidth:w}=t,[S,C,E]=(0,i.oN)({sourceX:o,sourceY:r,sourcePosition:h,targetX:a,targetY:s,targetPosition:v,borderRadius:null==x?void 0:x.borderRadius,offset:null==x?void 0:x.offset}),k=e.isInternal?void 0:n;return(0,l.jsx)(eZ,{id:k,path:S,labelX:C,labelY:E,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:f,labelBgBorderRadius:p,style:m,markerEnd:y,markerStart:b,interactionWidth:w})})}eY.displayName="SimpleBezierEdge",eQ.displayName="SimpleBezierEdgeInternal";let eG=eU({isInternal:!1}),eq=eU({isInternal:!0});function e$(e){return(0,r.memo)(t=>{var n;let{id:o,...a}=t,i=e.isInternal?void 0:o;return(0,l.jsx)(eG,{...a,id:i,pathOptions:(0,r.useMemo)(()=>{var e;return{borderRadius:0,offset:null==(e=a.pathOptions)?void 0:e.offset}},[null==(n=a.pathOptions)?void 0:n.offset])})})}eG.displayName="SmoothStepEdge",eq.displayName="SmoothStepEdgeInternal";let eJ=e$({isInternal:!1}),e0=e$({isInternal:!0});function e1(e){return(0,r.memo)(t=>{let{id:n,sourceX:o,sourceY:r,targetX:a,targetY:s,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:f,labelBgBorderRadius:p,style:m,markerEnd:h,markerStart:v,interactionWidth:y}=t,[b,x,w]=(0,i.ah)({sourceX:o,sourceY:r,targetX:a,targetY:s}),S=e.isInternal?void 0:n;return(0,l.jsx)(eZ,{id:S,path:b,labelX:x,labelY:w,label:d,labelStyle:c,labelShowBg:u,labelBgStyle:g,labelBgPadding:f,labelBgBorderRadius:p,style:m,markerEnd:h,markerStart:v,interactionWidth:y})})}eJ.displayName="StepEdge",e0.displayName="StepEdgeInternal";let e2=e1({isInternal:!1}),e3=e1({isInternal:!0});function e4(e){return(0,r.memo)(t=>{let{id:n,sourceX:o,sourceY:r,targetX:a,targetY:s,sourcePosition:d=i.yX.Bottom,targetPosition:c=i.yX.Top,label:u,labelStyle:g,labelShowBg:f,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:h,style:v,markerEnd:y,markerStart:b,pathOptions:x,interactionWidth:w}=t,[S,C,E]=(0,i.Fp)({sourceX:o,sourceY:r,sourcePosition:d,targetX:a,targetY:s,targetPosition:c,curvature:null==x?void 0:x.curvature}),k=e.isInternal?void 0:n;return(0,l.jsx)(eZ,{id:k,path:S,labelX:C,labelY:E,label:u,labelStyle:g,labelShowBg:f,labelBgStyle:p,labelBgPadding:m,labelBgBorderRadius:h,style:v,markerEnd:y,markerStart:b,interactionWidth:w})})}e2.displayName="StraightEdge",e3.displayName="StraightEdgeInternal";let e5=e4({isInternal:!1}),e6=e4({isInternal:!0});e5.displayName="BezierEdge",e6.displayName="BezierEdgeInternal";let e7={default:e6,straight:e3,step:e0,smoothstep:eq,simplebezier:eQ},e8={sourceX:null,sourceY:null,targetX:null,targetY:null,sourcePosition:null,targetPosition:null},e9=(e,t,n)=>n===i.yX.Left?e-t:n===i.yX.Right?e+t:e,te=(e,t,n)=>n===i.yX.Top?e-t:n===i.yX.Bottom?e+t:e,tt="react-flow__edgeupdater";function tn(e){let{position:t,centerX:n,centerY:o,radius:r=10,onMouseDown:i,onMouseEnter:s,onMouseOut:d,type:c}=e;return(0,l.jsx)("circle",{onMouseDown:i,onMouseEnter:s,onMouseOut:d,className:(0,a.A)([tt,"".concat(tt,"-").concat(c)]),cx:e9(n,r,t),cy:te(o,r,t),r:r,stroke:"transparent",fill:"transparent"})}function to(e){let{isReconnectable:t,reconnectRadius:n,edge:o,sourceX:r,sourceY:a,targetX:s,targetY:d,sourcePosition:c,targetPosition:u,onReconnect:g,onReconnectStart:f,onReconnectEnd:m,setReconnecting:h,setUpdateHover:v}=e,y=p(),b=(e,t)=>{if(0!==e.button)return;let{autoPanOnConnect:n,domNode:l,isValidConnection:r,connectionMode:a,connectionRadius:s,lib:d,onConnectStart:c,onConnectEnd:u,cancelConnection:p,nodeLookup:v,rfId:b,panBy:x,updateConnection:w}=y.getState(),S="target"===t.type;i.aQ.onPointerDown(e.nativeEvent,{autoPanOnConnect:n,connectionMode:a,connectionRadius:s,domNode:l,handleId:t.id,nodeId:t.nodeId,nodeLookup:v,isTarget:S,edgeUpdaterType:t.type,lib:d,flowId:b,cancelConnection:p,panBy:x,isValidConnection:r,onConnect:e=>null==g?void 0:g(o,e),onConnectStart:(n,l)=>{h(!0),null==f||f(e,o,t.type),null==c||c(n,l)},onConnectEnd:u,onReconnectEnd:(e,n)=>{h(!1),null==m||m(e,o,t.type,n)},updateConnection:w,getTransform:()=>y.getState().transform,getFromHandle:()=>y.getState().connection.fromHandle,dragThreshold:y.getState().connectionDragThreshold})},x=()=>v(!0),w=()=>v(!1);return(0,l.jsxs)(l.Fragment,{children:[(!0===t||"source"===t)&&(0,l.jsx)(tn,{position:c,centerX:r,centerY:a,radius:n,onMouseDown:e=>{var t;return b(e,{nodeId:o.target,id:null!=(t=o.targetHandle)?t:null,type:"target"})},onMouseEnter:x,onMouseOut:w,type:"source"}),(!0===t||"target"===t)&&(0,l.jsx)(tn,{position:u,centerX:s,centerY:d,radius:n,onMouseDown:e=>{var t;return b(e,{nodeId:o.source,id:null!=(t=o.sourceHandle)?t:null,type:"source"})},onMouseEnter:x,onMouseOut:w,type:"target"})]})}function tl(e){var t,n;let{id:o,edgesFocusable:s,edgesReconnectable:c,elementsSelectable:u,onClick:g,onDoubleClick:m,onContextMenu:h,onMouseEnter:v,onMouseMove:b,onMouseLeave:x,reconnectRadius:w,onReconnect:S,onReconnectStart:C,onReconnectEnd:E,rfId:k,edgeTypes:N,noPanClassName:M,onError:P,disableKeyboardA11y:j}=e,D=f(e=>e.edgeLookup.get(o)),_=f(e=>e.defaultEdgeOptions),A=(D=_?{..._,...D}:D).type||"default",R=(null==N?void 0:N[A])||e7[A];void 0===R&&(null==P||P("011",i.xc.error011(A)),A="default",R=e7.default);let O=!!(D.focusable||s&&void 0===D.focusable),I=void 0!==S&&(D.reconnectable||c&&void 0===D.reconnectable),L=!!(D.selectable||u&&void 0===D.selectable),z=(0,r.useRef)(null),[B,V]=(0,r.useState)(!1),[H,T]=(0,r.useState)(!1),F=p(),{zIndex:Z,sourceX:X,sourceY:W,targetX:K,targetY:Y,sourcePosition:Q,targetPosition:U}=f((0,r.useCallback)(e=>{let t=e.nodeLookup.get(D.source),n=e.nodeLookup.get(D.target);if(!t||!n)return{zIndex:D.zIndex,...e8};let l=(0,i.b5)({id:o,sourceNode:t,targetNode:n,sourceHandle:D.sourceHandle||null,targetHandle:D.targetHandle||null,connectionMode:e.connectionMode,onError:P});return{zIndex:(0,i.qX)({selected:D.selected,zIndex:D.zIndex,sourceNode:t,targetNode:n,elevateOnSelect:e.elevateEdgesOnSelect}),...l||e8}},[D.source,D.target,D.sourceHandle,D.targetHandle,D.selected,D.zIndex]),d.x),G=(0,r.useMemo)(()=>D.markerStart?"url('#".concat((0,i.aW)(D.markerStart,k),"')"):void 0,[D.markerStart,k]),q=(0,r.useMemo)(()=>D.markerEnd?"url('#".concat((0,i.aW)(D.markerEnd,k),"')"):void 0,[D.markerEnd,k]);if(D.hidden||null===X||null===W||null===K||null===Y)return null;let $=m?e=>{m(e,{...D})}:void 0,J=h?e=>{h(e,{...D})}:void 0,ee=v?e=>{v(e,{...D})}:void 0,et=b?e=>{b(e,{...D})}:void 0,en=x?e=>{x(e,{...D})}:void 0;return(0,l.jsx)("svg",{style:{zIndex:Z},children:(0,l.jsxs)("g",{className:(0,a.A)(["react-flow__edge","react-flow__edge-".concat(A),D.className,M,{selected:D.selected,animated:D.animated,inactive:!L&&!g,updating:B,selectable:L}]),onClick:e=>{let{addSelectedEdges:t,unselectNodesAndEdges:n,multiSelectionActive:l}=F.getState();if(L)if(F.setState({nodesSelectionActive:!1}),D.selected&&l){var r;n({nodes:[],edges:[D]}),null==(r=z.current)||r.blur()}else t([o]);g&&g(e,D)},onDoubleClick:$,onContextMenu:J,onMouseEnter:ee,onMouseMove:et,onMouseLeave:en,onKeyDown:O?e=>{if(!j&&i.tn.includes(e.key)&&L){let{unselectNodesAndEdges:n,addSelectedEdges:l}=F.getState();if("Escape"===e.key){var t;null==(t=z.current)||t.blur(),n({edges:[D]})}else l([o])}}:void 0,tabIndex:O?0:void 0,role:null!=(t=D.ariaRole)?t:O?"group":"img","aria-roledescription":"edge","data-id":o,"data-testid":"rf__edge-".concat(o),"aria-label":null===D.ariaLabel?void 0:D.ariaLabel||"Edge from ".concat(D.source," to ").concat(D.target),"aria-describedby":O?"".concat(y,"-").concat(k):void 0,ref:z,...D.domAttributes,children:[!H&&(0,l.jsx)(R,{id:o,source:D.source,target:D.target,type:D.type,selected:D.selected,animated:D.animated,selectable:L,deletable:null==(n=D.deletable)||n,label:D.label,labelStyle:D.labelStyle,labelShowBg:D.labelShowBg,labelBgStyle:D.labelBgStyle,labelBgPadding:D.labelBgPadding,labelBgBorderRadius:D.labelBgBorderRadius,sourceX:X,sourceY:W,targetX:K,targetY:Y,sourcePosition:Q,targetPosition:U,data:D.data,style:D.style,sourceHandleId:D.sourceHandle,targetHandleId:D.targetHandle,markerStart:G,markerEnd:q,pathOptions:"pathOptions"in D?D.pathOptions:void 0,interactionWidth:D.interactionWidth}),I&&(0,l.jsx)(to,{edge:D,isReconnectable:I,reconnectRadius:w,onReconnect:S,onReconnectStart:C,onReconnectEnd:E,sourceX:X,sourceY:W,targetX:K,targetY:Y,sourcePosition:Q,targetPosition:U,setUpdateHover:V,setReconnecting:T})]})})}let tr=e=>({edgesFocusable:e.edgesFocusable,edgesReconnectable:e.edgesReconnectable,elementsSelectable:e.elementsSelectable,connectionMode:e.connectionMode,onError:e.onError});function ta(e){let{defaultMarkerColor:t,onlyRenderVisibleElements:n,rfId:o,edgeTypes:a,noPanClassName:s,onReconnect:c,onEdgeContextMenu:u,onEdgeMouseEnter:g,onEdgeMouseMove:p,onEdgeMouseLeave:m,onEdgeClick:h,reconnectRadius:v,onEdgeDoubleClick:y,onReconnectStart:b,onReconnectEnd:x,disableKeyboardA11y:w}=e,{edgesFocusable:S,edgesReconnectable:C,elementsSelectable:E,onError:k}=f(tr,d.x),N=f((0,r.useCallback)(e=>{if(!n)return e.edges.map(e=>e.id);let t=[];if(e.width&&e.height)for(let n of e.edges){let o=e.nodeLookup.get(n.source),l=e.nodeLookup.get(n.target);o&&l&&(0,i.uj)({sourceNode:o,targetNode:l,width:e.width,height:e.height,transform:e.transform})&&t.push(n.id)}return t},[n]),d.x);return(0,l.jsxs)("div",{className:"react-flow__edges",children:[(0,l.jsx)(eH,{defaultColor:t,rfId:o}),N.map(e=>(0,l.jsx)(tl,{id:e,edgesFocusable:S,edgesReconnectable:C,elementsSelectable:E,noPanClassName:s,onReconnect:c,onContextMenu:u,onMouseEnter:g,onMouseMove:p,onMouseLeave:m,onClick:h,reconnectRadius:v,onDoubleClick:y,onReconnectStart:b,onReconnectEnd:x,rfId:o,onError:k,edgeTypes:a,disableKeyboardA11y:w},e))]})}ta.displayName="EdgeRenderer";let ti=(0,r.memo)(ta),ts=e=>"translate(".concat(e.transform[0],"px,").concat(e.transform[1],"px) scale(").concat(e.transform[2],")");function td(e){let{children:t}=e,n=f(ts);return(0,l.jsx)("div",{className:"react-flow__viewport xyflow__viewport react-flow__container",style:{transform:n},children:t})}let tc=e=>{var t;return null==(t=e.panZoom)?void 0:t.syncViewport};function tu(e){return e.connection.inProgress?{...e.connection,to:(0,i.Ff)(e.connection.to,e.transform)}:{...e.connection}}let tg=e=>({nodesConnectable:e.nodesConnectable,isValid:e.connection.isValid,inProgress:e.connection.inProgress,width:e.width,height:e.height});function tf(e){let{containerStyle:t,style:n,type:o,component:r}=e,{nodesConnectable:s,width:c,height:u,isValid:g,inProgress:p}=f(tg,d.x);return c&&s&&p?(0,l.jsx)("svg",{style:t,width:c,height:u,className:"react-flow__connectionline react-flow__container",children:(0,l.jsx)("g",{className:(0,a.A)(["react-flow__connection",(0,i.HF)(g)]),children:(0,l.jsx)(tp,{style:n,type:o,CustomComponent:r,isValid:g})})}):null}let tp=e=>{let{style:t,type:n=i.Do.Bezier,CustomComponent:o,isValid:r}=e,{inProgress:a,from:s,fromNode:c,fromHandle:u,fromPosition:g,to:p,toNode:m,toHandle:h,toPosition:v}=function(e){return f(tu,d.x)}();if(!a)return;if(o)return(0,l.jsx)(o,{connectionLineType:n,connectionLineStyle:t,fromNode:c,fromHandle:u,fromX:s.x,fromY:s.y,toX:p.x,toY:p.y,fromPosition:g,toPosition:v,connectionStatus:(0,i.HF)(r),toNode:m,toHandle:h});let y="",b={sourceX:s.x,sourceY:s.y,sourcePosition:g,targetX:p.x,targetY:p.y,targetPosition:v};switch(n){case i.Do.Bezier:[y]=(0,i.Fp)(b);break;case i.Do.SimpleBezier:[y]=eW(b);break;case i.Do.Step:[y]=(0,i.oN)({...b,borderRadius:0});break;case i.Do.SmoothStep:[y]=(0,i.oN)(b);break;default:[y]=(0,i.ah)(b)}return(0,l.jsx)("path",{d:y,fill:"none",className:"react-flow__connection-path",style:t})};tp.displayName="ConnectionLine";let tm={};function th(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tm;(0,r.useRef)(e),p(),(0,r.useEffect)(()=>{},[e])}function tv(e){let{nodeTypes:t,edgeTypes:n,onInit:o,onNodeClick:a,onEdgeClick:i,onNodeDoubleClick:s,onEdgeDoubleClick:d,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:g,onNodeContextMenu:m,onSelectionContextMenu:h,onSelectionStart:v,onSelectionEnd:y,connectionLineType:b,connectionLineStyle:x,connectionLineComponent:w,connectionLineContainerStyle:S,selectionKeyCode:C,selectionOnDrag:E,selectionMode:k,multiSelectionKeyCode:N,panActivationKeyCode:M,zoomActivationKeyCode:P,deleteKeyCode:j,onlyRenderVisibleElements:D,elementsSelectable:_,defaultViewport:A,translateExtent:R,minZoom:O,maxZoom:I,preventScrolling:L,defaultMarkerColor:z,zoomOnScroll:B,zoomOnPinch:V,panOnScroll:H,panOnScrollSpeed:T,panOnScrollMode:F,zoomOnDoubleClick:Z,panOnDrag:X,onPaneClick:W,onPaneMouseEnter:K,onPaneMouseMove:Y,onPaneMouseLeave:Q,onPaneScroll:U,onPaneContextMenu:G,paneClickDistance:q,nodeClickDistance:$,onEdgeContextMenu:J,onEdgeMouseEnter:ee,onEdgeMouseMove:et,onEdgeMouseLeave:eo,reconnectRadius:el,onReconnect:er,onReconnectStart:ea,onReconnectEnd:ei,noDragClassName:es,noWheelClassName:ed,noPanClassName:ec,disableKeyboardA11y:eu,nodeExtent:eg,rfId:ef,viewport:ep,onViewportChange:em}=e;th(t),th(n),p(),(0,r.useRef)(!1),(0,r.useEffect)(()=>{},[]);let eh=en(),ev=(0,r.useRef)(!1);return(0,r.useEffect)(()=>{!ev.current&&eh.viewportInitialized&&o&&(setTimeout(()=>o(eh),1),ev.current=!0)},[o,eh.viewportInitialized]),!function(e){let t=f(tc),n=p();(0,r.useEffect)(()=>{e&&(null==t||t(e),n.setState({transform:[e.x,e.y,e.zoom]}))},[e,t])}(ep),(0,l.jsx)(eD,{onPaneClick:W,onPaneMouseEnter:K,onPaneMouseMove:Y,onPaneMouseLeave:Q,onPaneContextMenu:G,onPaneScroll:U,paneClickDistance:q,deleteKeyCode:j,selectionKeyCode:C,selectionOnDrag:E,selectionMode:k,onSelectionStart:v,onSelectionEnd:y,multiSelectionKeyCode:N,panActivationKeyCode:M,zoomActivationKeyCode:P,elementsSelectable:_,zoomOnScroll:B,zoomOnPinch:V,zoomOnDoubleClick:Z,panOnScroll:H,panOnScrollSpeed:T,panOnScrollMode:F,panOnDrag:X,defaultViewport:A,translateExtent:R,minZoom:O,maxZoom:I,onSelectionContextMenu:h,preventScrolling:L,noDragClassName:es,noWheelClassName:ed,noPanClassName:ec,disableKeyboardA11y:eu,onViewportChange:em,isControlledViewport:!!ep,children:(0,l.jsxs)(td,{children:[(0,l.jsx)(ti,{edgeTypes:n,onEdgeClick:i,onEdgeDoubleClick:d,onReconnect:er,onReconnectStart:ea,onReconnectEnd:ei,onlyRenderVisibleElements:D,onEdgeContextMenu:J,onEdgeMouseEnter:ee,onEdgeMouseMove:et,onEdgeMouseLeave:eo,reconnectRadius:el,defaultMarkerColor:z,noPanClassName:ec,disableKeyboardA11y:eu,rfId:ef}),(0,l.jsx)(tf,{style:x,type:b,component:w,containerStyle:S}),(0,l.jsx)("div",{className:"react-flow__edgelabel-renderer"}),(0,l.jsx)(eL,{nodeTypes:t,onNodeClick:a,onNodeDoubleClick:s,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:g,onNodeContextMenu:m,nodeClickDistance:$,onlyRenderVisibleElements:D,noPanClassName:ec,noDragClassName:es,disableKeyboardA11y:eu,nodeExtent:eg,rfId:ef}),(0,l.jsx)("div",{className:"react-flow__viewport-portal"})]})})}tv.displayName="GraphView";let ty=(0,r.memo)(tv),tb=function(){var e,t,n;let{nodes:o,edges:l,defaultNodes:r,defaultEdges:a,width:s,height:d,fitView:c,fitViewOptions:u,minZoom:g=.5,maxZoom:f=2,nodeOrigin:p,nodeExtent:m}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},h=new Map,v=new Map,y=new Map,b=new Map,x=null!=(e=null!=a?a:l)?e:[],w=null!=(t=null!=r?r:o)?t:[],S=null!=p?p:[0,0],C=null!=m?m:i.ZO;(0,i.qn)(y,b,x);let E=(0,i.bi)(w,h,v,{nodeOrigin:S,nodeExtent:C,elevateNodesOnSelect:!1}),k=[0,0,1];if(c&&s&&d){let e=(0,i.aZ)(h,{filter:e=>!!((e.width||e.initialWidth)&&(e.height||e.initialHeight))}),{x:t,y:o,zoom:l}=(0,i.R4)(e,s,d,g,f,null!=(n=null==u?void 0:u.padding)?n:.1);k=[t,o,l]}return{rfId:"1",width:0,height:0,transform:k,nodes:w,nodesInitialized:E,nodeLookup:h,parentLookup:v,edges:x,edgeLookup:b,connectionLookup:y,onNodesChange:null,onEdgesChange:null,hasDefaultNodes:void 0!==r,hasDefaultEdges:void 0!==a,panZoom:null,minZoom:g,maxZoom:f,translateExtent:i.ZO,nodeExtent:C,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionMode:i.WZ.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:S,nodeDragThreshold:1,connectionDragThreshold:1,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesReconnectable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,elevateEdgesOnSelect:!1,selectNodesOnDrag:!0,multiSelectionActive:!1,fitViewQueued:null!=c&&c,fitViewOptions:u,fitViewResolver:null,connection:{...i.bK},connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,autoPanOnNodeFocus:!0,autoPanSpeed:15,connectionRadius:20,onError:i.KE,isValidConnection:void 0,onSelectionChangeHandlers:[],lib:"react",debug:!1,ariaLabelConfig:i.tM}},tx=e=>{let{nodes:t,edges:n,defaultNodes:o,defaultEdges:l,width:r,height:a,fitView:d,fitViewOptions:c,minZoom:u,maxZoom:g,nodeOrigin:f,nodeExtent:p}=e;return(0,s.h)((e,s)=>{async function m(){let{nodeLookup:t,panZoom:n,fitViewOptions:o,fitViewResolver:l,width:r,height:a,minZoom:d,maxZoom:c}=s();n&&(await (0,i.IO)({nodes:t,width:r,height:a,panZoom:n,minZoom:d,maxZoom:c},o),null==l||l.resolve(!0),e({fitViewResolver:null}))}return{...tb({nodes:t,edges:n,width:r,height:a,fitView:d,fitViewOptions:c,minZoom:u,maxZoom:g,nodeOrigin:f,nodeExtent:p,defaultNodes:o,defaultEdges:l}),setNodes:t=>{let{nodeLookup:n,parentLookup:o,nodeOrigin:l,elevateNodesOnSelect:r,fitViewQueued:a}=s(),d=(0,i.bi)(t,n,o,{nodeOrigin:l,nodeExtent:p,elevateNodesOnSelect:r,checkEquality:!0});a&&d?(m(),e({nodes:t,nodesInitialized:d,fitViewQueued:!1,fitViewOptions:void 0})):e({nodes:t,nodesInitialized:d})},setEdges:t=>{let{connectionLookup:n,edgeLookup:o}=s();(0,i.qn)(n,o,t),e({edges:t})},setDefaultNodesAndEdges:(t,n)=>{if(t){let{setNodes:n}=s();n(t),e({hasDefaultNodes:!0})}if(n){let{setEdges:t}=s();t(n),e({hasDefaultEdges:!0})}},updateNodeInternals:t=>{let{triggerNodeChanges:n,nodeLookup:o,parentLookup:l,domNode:r,nodeOrigin:a,nodeExtent:d,debug:c,fitViewQueued:u}=s(),{changes:g,updatedInternals:f}=(0,i.uL)(t,o,l,r,a,d);f&&((0,i.vS)(o,l,{nodeOrigin:a,nodeExtent:d}),u?(m(),e({fitViewQueued:!1,fitViewOptions:void 0})):e({}),(null==g?void 0:g.length)>0&&(null==n||n(g)))},updateNodePositions:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=[],o=[],{nodeLookup:l,triggerNodeChanges:r}=s();for(let[r,i]of e){let e=l.get(r),s=!!((null==e?void 0:e.expandParent)&&(null==e?void 0:e.parentId)&&(null==i?void 0:i.position)),c={id:r,type:"position",position:s?{x:Math.max(0,i.position.x),y:Math.max(0,i.position.y)}:i.position,dragging:t};if(s&&e.parentId){var a,d;n.push({id:r,parentId:e.parentId,rect:{...i.internals.positionAbsolute,width:null!=(a=i.measured.width)?a:0,height:null!=(d=i.measured.height)?d:0}})}o.push(c)}if(n.length>0){let{parentLookup:e,nodeOrigin:t}=s(),r=(0,i.r8)(n,l,e,t);o.push(...r)}r(o)},triggerNodeChanges:e=>{let{onNodesChange:t,setNodes:n,nodes:o,hasDefaultNodes:l,debug:r}=s();(null==e?void 0:e.length)&&(l&&n(Z(e,o)),null==t||t(e))},triggerEdgeChanges:e=>{let{onEdgesChange:t,setEdges:n,edges:o,hasDefaultEdges:l,debug:r}=s();(null==e?void 0:e.length)&&(l&&n(Z(e,o)),null==t||t(e))},addSelectedNodes:e=>{let{multiSelectionActive:t,edgeLookup:n,nodeLookup:o,triggerNodeChanges:l,triggerEdgeChanges:r}=s();if(t)return void l(e.map(e=>X(e,!0)));l(W(o,new Set([...e]),!0)),r(W(n))},addSelectedEdges:e=>{let{multiSelectionActive:t,edgeLookup:n,nodeLookup:o,triggerNodeChanges:l,triggerEdgeChanges:r}=s();if(t)return void r(e.map(e=>X(e,!0)));r(W(n,new Set([...e]))),l(W(o,new Set,!0))},unselectNodesAndEdges:function(){let{nodes:e,edges:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{edges:n,nodes:o,nodeLookup:l,triggerNodeChanges:r,triggerEdgeChanges:a}=s(),i=(e||o).map(e=>{let t=l.get(e.id);return t&&(t.selected=!1),X(e.id,!1)}),d=(t||n).map(e=>X(e.id,!1));r(i),a(d)},setMinZoom:t=>{let{panZoom:n,maxZoom:o}=s();null==n||n.setScaleExtent([t,o]),e({minZoom:t})},setMaxZoom:t=>{let{panZoom:n,minZoom:o}=s();null==n||n.setScaleExtent([o,t]),e({maxZoom:t})},setTranslateExtent:t=>{var n;null==(n=s().panZoom)||n.setTranslateExtent(t),e({translateExtent:t})},setPaneClickDistance:e=>{var t;null==(t=s().panZoom)||t.setClickDistance(e)},resetSelectedElements:()=>{let{edges:e,nodes:t,triggerNodeChanges:n,triggerEdgeChanges:o,elementsSelectable:l}=s();if(!l)return;let r=t.reduce((e,t)=>t.selected?[...e,X(t.id,!1)]:e,[]),a=e.reduce((e,t)=>t.selected?[...e,X(t.id,!1)]:e,[]);n(r),o(a)},setNodeExtent:t=>{let{nodes:n,nodeLookup:o,parentLookup:l,nodeOrigin:r,elevateNodesOnSelect:a,nodeExtent:d}=s();(t[0][0]!==d[0][0]||t[0][1]!==d[0][1]||t[1][0]!==d[1][0]||t[1][1]!==d[1][1])&&((0,i.bi)(n,o,l,{nodeOrigin:r,nodeExtent:t,elevateNodesOnSelect:a,checkEquality:!1}),e({nodeExtent:t}))},panBy:e=>{let{transform:t,width:n,height:o,panZoom:l,translateExtent:r}=s();return(0,i.No)({delta:e,panZoom:l,transform:t,translateExtent:r,width:n,height:o})},setCenter:async(e,t,n)=>{let{width:o,height:l,maxZoom:r,panZoom:a}=s();if(!a)return Promise.resolve(!1);let i=void 0!==(null==n?void 0:n.zoom)?n.zoom:r;return await a.setViewport({x:o/2-e*i,y:l/2-t*i,zoom:i},{duration:null==n?void 0:n.duration,ease:null==n?void 0:n.ease,interpolate:null==n?void 0:n.interpolate}),Promise.resolve(!0)},cancelConnection:()=>{e({connection:{...i.bK}})},updateConnection:t=>{e({connection:t})},reset:()=>e({...tb()})}},Object.is)};function tw(e){let{initialNodes:t,initialEdges:n,defaultNodes:o,defaultEdges:a,initialWidth:i,initialHeight:s,initialMinZoom:d,initialMaxZoom:c,initialFitViewOptions:g,fitView:f,nodeOrigin:p,nodeExtent:m,children:h}=e,[v]=(0,r.useState)(()=>tx({nodes:t,edges:n,defaultNodes:o,defaultEdges:a,width:i,height:s,fitView:f,minZoom:d,maxZoom:c,fitViewOptions:g,nodeOrigin:p,nodeExtent:m}));return(0,l.jsx)(u,{value:v,children:(0,l.jsx)(ee,{children:h})})}function tS(e){let{children:t,nodes:n,edges:o,defaultNodes:a,defaultEdges:i,width:s,height:d,fitView:u,fitViewOptions:g,minZoom:f,maxZoom:p,nodeOrigin:m,nodeExtent:h}=e;return(0,r.useContext)(c)?(0,l.jsx)(l.Fragment,{children:t}):(0,l.jsx)(tw,{initialNodes:n,initialEdges:o,defaultNodes:a,defaultEdges:i,initialWidth:s,initialHeight:d,fitView:u,initialFitViewOptions:g,initialMinZoom:f,initialMaxZoom:p,nodeOrigin:m,nodeExtent:h,children:t})}let tC={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};var tE=G(function(e,t){let{nodes:n,edges:o,defaultNodes:s,defaultEdges:d,className:c,nodeTypes:u,edgeTypes:g,onNodeClick:f,onEdgeClick:p,onInit:m,onMove:h,onMoveStart:v,onMoveEnd:y,onConnect:b,onConnectStart:x,onConnectEnd:w,onClickConnectStart:C,onClickConnectEnd:k,onNodeMouseEnter:N,onNodeMouseMove:M,onNodeMouseLeave:P,onNodeContextMenu:j,onNodeDoubleClick:R,onNodeDragStart:O,onNodeDrag:I,onNodeDragStop:B,onNodesDelete:V,onEdgesDelete:H,onDelete:T,onSelectionChange:F,onSelectionDragStart:Z,onSelectionDrag:X,onSelectionDragStop:W,onSelectionContextMenu:K,onSelectionStart:Y,onSelectionEnd:Q,onBeforeDelete:U,connectionMode:G,connectionLineType:q=i.Do.Bezier,connectionLineStyle:$,connectionLineComponent:J,connectionLineContainerStyle:ee,deleteKeyCode:et="Backspace",selectionKeyCode:en="Shift",selectionOnDrag:eo=!1,selectionMode:el=i.Qc.Full,panActivationKeyCode:er="Space",multiSelectionKeyCode:ea=(0,i.Ue)()?"Meta":"Control",zoomActivationKeyCode:ei=(0,i.Ue)()?"Meta":"Control",snapToGrid:es,snapGrid:ed,onlyRenderVisibleElements:ec=!1,selectNodesOnDrag:eu,nodesDraggable:eg,autoPanOnNodeFocus:ef,nodesConnectable:ep,nodesFocusable:em,nodeOrigin:eh=_,edgesFocusable:ev,edgesReconnectable:ey,elementsSelectable:eb=!0,defaultViewport:ex=A,minZoom:ew=.5,maxZoom:eS=2,translateExtent:eC=i.ZO,preventScrolling:eE=!0,nodeExtent:ek,defaultMarkerColor:eN="#b1b1b7",zoomOnScroll:eM=!0,zoomOnPinch:eP=!0,panOnScroll:ej=!1,panOnScrollSpeed:eD=.5,panOnScrollMode:e_=i.ny.Free,zoomOnDoubleClick:eA=!0,panOnDrag:eR=!0,onPaneClick:eO,onPaneMouseEnter:eI,onPaneMouseMove:eL,onPaneMouseLeave:ez,onPaneScroll:eB,onPaneContextMenu:eV,paneClickDistance:eH=0,nodeClickDistance:eT=0,children:eF,onReconnect:eZ,onReconnectStart:eX,onReconnectEnd:eW,onEdgeContextMenu:eK,onEdgeDoubleClick:eY,onEdgeMouseEnter:eQ,onEdgeMouseMove:eU,onEdgeMouseLeave:eG,reconnectRadius:eq=10,onNodesChange:e$,onEdgesChange:eJ,noDragClassName:e0="nodrag",noWheelClassName:e1="nowheel",noPanClassName:e2="nopan",fitView:e3,fitViewOptions:e4,connectOnClick:e5,attributionPosition:e6,proOptions:e7,defaultEdgeOptions:e8,elevateNodesOnSelect:e9,elevateEdgesOnSelect:te,disableKeyboardA11y:tt=!1,autoPanOnConnect:tn,autoPanOnNodeDrag:to,autoPanSpeed:tl,connectionRadius:tr,isValidConnection:ta,onError:ti,style:ts,id:td,nodeDragThreshold:tc,connectionDragThreshold:tu,viewport:tg,onViewportChange:tf,width:tp,height:tm,colorMode:th="light",debug:tv,onScroll:tb,ariaLabelConfig:tx,...tw}=e,tE=td||"1",tk=function(e){var t;let[n,o]=(0,r.useState)("system"===e?null:e);return(0,r.useEffect)(()=>{if("system"!==e)return void o(e);let t=z(),n=()=>o((null==t?void 0:t.matches)?"dark":"light");return n(),null==t||t.addEventListener("change",n),()=>{null==t||t.removeEventListener("change",n)}},[e]),null!==n?n:(null==(t=z())?void 0:t.matches)?"dark":"light"}(th),tN=(0,r.useCallback)(e=>{e.currentTarget.scrollTo({top:0,left:0,behavior:"instant"}),null==tb||tb(e)},[tb]);return(0,l.jsx)("div",{"data-testid":"rf__wrapper",...tw,onScroll:tN,style:{...ts,...tC},ref:t,className:(0,a.A)(["react-flow",c,tk]),id:td,role:"application",children:(0,l.jsxs)(tS,{nodes:n,edges:o,width:tp,height:tm,fitView:e3,fitViewOptions:e4,minZoom:ew,maxZoom:eS,nodeOrigin:eh,nodeExtent:ek,children:[(0,l.jsx)(ty,{onInit:m,onNodeClick:f,onEdgeClick:p,onNodeMouseEnter:N,onNodeMouseMove:M,onNodeMouseLeave:P,onNodeContextMenu:j,onNodeDoubleClick:R,nodeTypes:u,edgeTypes:g,connectionLineType:q,connectionLineStyle:$,connectionLineComponent:J,connectionLineContainerStyle:ee,selectionKeyCode:en,selectionOnDrag:eo,selectionMode:el,deleteKeyCode:et,multiSelectionKeyCode:ea,panActivationKeyCode:er,zoomActivationKeyCode:ei,onlyRenderVisibleElements:ec,defaultViewport:ex,translateExtent:eC,minZoom:ew,maxZoom:eS,preventScrolling:eE,zoomOnScroll:eM,zoomOnPinch:eP,zoomOnDoubleClick:eA,panOnScroll:ej,panOnScrollSpeed:eD,panOnScrollMode:e_,panOnDrag:eR,onPaneClick:eO,onPaneMouseEnter:eI,onPaneMouseMove:eL,onPaneMouseLeave:ez,onPaneScroll:eB,onPaneContextMenu:eV,paneClickDistance:eH,nodeClickDistance:eT,onSelectionContextMenu:K,onSelectionStart:Y,onSelectionEnd:Q,onReconnect:eZ,onReconnectStart:eX,onReconnectEnd:eW,onEdgeContextMenu:eK,onEdgeDoubleClick:eY,onEdgeMouseEnter:eQ,onEdgeMouseMove:eU,onEdgeMouseLeave:eG,reconnectRadius:eq,defaultMarkerColor:eN,noDragClassName:e0,noWheelClassName:e1,noPanClassName:e2,rfId:tE,disableKeyboardA11y:tt,nodeExtent:ek,viewport:tg,onViewportChange:tf}),(0,l.jsx)(L,{nodes:n,edges:o,defaultNodes:s,defaultEdges:d,onConnect:b,onConnectStart:x,onConnectEnd:w,onClickConnectStart:C,onClickConnectEnd:k,nodesDraggable:eg,autoPanOnNodeFocus:ef,nodesConnectable:ep,nodesFocusable:em,edgesFocusable:ev,edgesReconnectable:ey,elementsSelectable:eb,elevateNodesOnSelect:e9,elevateEdgesOnSelect:te,minZoom:ew,maxZoom:eS,nodeExtent:ek,onNodesChange:e$,onEdgesChange:eJ,snapToGrid:es,snapGrid:ed,connectionMode:G,translateExtent:eC,connectOnClick:e5,defaultEdgeOptions:e8,fitView:e3,fitViewOptions:e4,onNodesDelete:V,onEdgesDelete:H,onDelete:T,onNodeDragStart:O,onNodeDrag:I,onNodeDragStop:B,onSelectionDrag:X,onSelectionDragStart:Z,onSelectionDragStop:W,onMove:h,onMoveStart:v,onMoveEnd:y,noPanClassName:e2,nodeOrigin:eh,rfId:tE,autoPanOnConnect:tn,autoPanOnNodeDrag:to,autoPanSpeed:tl,onError:ti,connectionRadius:tr,isValidConnection:ta,selectNodesOnDrag:eu,nodeDragThreshold:tc,connectionDragThreshold:tu,onBeforeDelete:U,paneClickDistance:eH,debug:tv,ariaLabelConfig:tx}),(0,l.jsx)(D,{onSelectionChange:F}),eF,(0,l.jsx)(E,{proOptions:e7,position:e6}),(0,l.jsx)(S,{rfId:tE,disableKeyboardA11y:tt})]})})});let tk=e=>e.nodes;function tN(){return f(tk,d.x)}let tM=e=>e.edges;function tP(){return f(tM,d.x)}function tj(e){let[t,n]=(0,r.useState)(e),o=(0,r.useCallback)(e=>n(t=>Z(e,t)),[]);return[t,n,o]}function tD(e){let[t,n]=(0,r.useState)(e),o=(0,r.useCallback)(e=>n(t=>Z(e,t)),[]);return[t,n,o]}function t_(e){let{dimensions:t,lineWidth:n,variant:o,className:r}=e;return(0,l.jsx)("path",{strokeWidth:n,d:"M".concat(t[0]/2," 0 V").concat(t[1]," M0 ").concat(t[1]/2," H").concat(t[0]),className:(0,a.A)(["react-flow__background-pattern",o,r])})}function tA(e){let{radius:t,className:n}=e;return(0,l.jsx)("circle",{cx:t,cy:t,r:t,className:(0,a.A)(["react-flow__background-pattern","dots",n])})}i.xc.error014(),!function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(o||(o={}));let tR={[o.Dots]:1,[o.Lines]:1,[o.Cross]:6},tO=e=>({transform:e.transform,patternId:"pattern-".concat(e.rfId)});function tI(e){let{id:t,variant:n=o.Dots,gap:i=20,size:s,lineWidth:c=1,offset:u=0,color:g,bgColor:p,style:m,className:h,patternClassName:v}=e,y=(0,r.useRef)(null),{transform:b,patternId:x}=f(tO,d.x),w=s||tR[n],S=n===o.Dots,C=n===o.Cross,E=Array.isArray(i)?i:[i,i],k=[E[0]*b[2]||1,E[1]*b[2]||1],N=w*b[2],M=Array.isArray(u)?u:[u,u],P=C?[N,N]:k,j=[M[0]*b[2]||1+P[0]/2,M[1]*b[2]||1+P[1]/2],D="".concat(x).concat(t||"");return(0,l.jsxs)("svg",{className:(0,a.A)(["react-flow__background",h]),style:{...m,...er,"--xy-background-color-props":p,"--xy-background-pattern-color-props":g},ref:y,"data-testid":"rf__background",children:[(0,l.jsx)("pattern",{id:D,x:b[0]%k[0],y:b[1]%k[1],width:k[0],height:k[1],patternUnits:"userSpaceOnUse",patternTransform:"translate(-".concat(j[0],",-").concat(j[1],")"),children:S?(0,l.jsx)(tA,{radius:N/2,className:v}):(0,l.jsx)(t_,{dimensions:P,lineWidth:c,variant:n,className:v})}),(0,l.jsx)("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:"url(#".concat(D,")")})]})}tI.displayName="Background";let tL=(0,r.memo)(tI);function tz(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",children:(0,l.jsx)("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"})})}function tB(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5",children:(0,l.jsx)("path",{d:"M0 0h32v4.2H0z"})})}function tV(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30",children:(0,l.jsx)("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"})})}function tH(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,l.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"})})}function tT(){return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:(0,l.jsx)("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"})})}function tF(e){let{children:t,className:n,...o}=e;return(0,l.jsx)("button",{type:"button",className:(0,a.A)(["react-flow__controls-button",n]),...o,children:t})}let tZ=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom,ariaLabelConfig:e.ariaLabelConfig});function tX(e){let{style:t,showZoom:n=!0,showFitView:o=!0,showInteractive:r=!0,fitViewOptions:i,onZoomIn:s,onZoomOut:c,onFitView:u,onInteractiveChange:g,className:m,children:h,position:v="bottom-left",orientation:y="vertical","aria-label":b}=e,x=p(),{isInteractive:w,minZoomReached:S,maxZoomReached:E,ariaLabelConfig:k}=f(tZ,d.x),{zoomIn:N,zoomOut:M,fitView:P}=en();return(0,l.jsxs)(C,{className:(0,a.A)(["react-flow__controls","horizontal"===y?"horizontal":"vertical",m]),position:v,style:t,"data-testid":"rf__controls","aria-label":null!=b?b:k["controls.ariaLabel"],children:[n&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(tF,{onClick:()=>{N(),null==s||s()},className:"react-flow__controls-zoomin",title:k["controls.zoomIn.ariaLabel"],"aria-label":k["controls.zoomIn.ariaLabel"],disabled:E,children:(0,l.jsx)(tz,{})}),(0,l.jsx)(tF,{onClick:()=>{M(),null==c||c()},className:"react-flow__controls-zoomout",title:k["controls.zoomOut.ariaLabel"],"aria-label":k["controls.zoomOut.ariaLabel"],disabled:S,children:(0,l.jsx)(tB,{})})]}),o&&(0,l.jsx)(tF,{className:"react-flow__controls-fitview",onClick:()=>{P(i),null==u||u()},title:k["controls.fitView.ariaLabel"],"aria-label":k["controls.fitView.ariaLabel"],children:(0,l.jsx)(tV,{})}),r&&(0,l.jsx)(tF,{className:"react-flow__controls-interactive",onClick:()=>{x.setState({nodesDraggable:!w,nodesConnectable:!w,elementsSelectable:!w}),null==g||g(!w)},title:k["controls.interactive.ariaLabel"],"aria-label":k["controls.interactive.ariaLabel"],children:w?(0,l.jsx)(tT,{}):(0,l.jsx)(tH,{})}),h]})}tX.displayName="Controls";let tW=(0,r.memo)(tX),tK=(0,r.memo)(function(e){let{id:t,x:n,y:o,width:r,height:i,style:s,color:d,strokeColor:c,strokeWidth:u,className:g,borderRadius:f,shapeRendering:p,selected:m,onClick:h}=e,{background:v,backgroundColor:y}=s||{};return(0,l.jsx)("rect",{className:(0,a.A)(["react-flow__minimap-node",{selected:m},g]),x:n,y:o,rx:f,ry:f,width:r,height:i,style:{fill:d||v||y,stroke:c,strokeWidth:u},shapeRendering:p,onClick:h?e=>h(e,t):void 0})}),tY=e=>e.nodes.map(e=>e.id),tQ=e=>e instanceof Function?e:()=>e,tU=(0,r.memo)(function(e){let{id:t,nodeColorFunc:n,nodeStrokeColorFunc:o,nodeClassNameFunc:r,nodeBorderRadius:a,nodeStrokeWidth:s,shapeRendering:c,NodeComponent:u,onClick:g}=e,{node:p,x:m,y:h,width:v,height:y}=f(e=>{let{internals:n}=e.nodeLookup.get(t),o=n.userNode,{x:l,y:r}=n.positionAbsolute,{width:a,height:s}=(0,i.uD)(o);return{node:o,x:l,y:r,width:a,height:s}},d.x);return p&&!p.hidden&&(0,i.QE)(p)?(0,l.jsx)(u,{x:m,y:h,width:v,height:y,style:p.style,selected:!!p.selected,className:r(p),color:n(p),borderRadius:a,strokeColor:o(p),strokeWidth:s,shapeRendering:c,onClick:g,id:p.id}):null});var tG=(0,r.memo)(function(e){let{nodeStrokeColor:t,nodeColor:n,nodeClassName:o="",nodeBorderRadius:r=5,nodeStrokeWidth:a,nodeComponent:i=tK,onClick:s}=e,c=f(tY,d.x),u=tQ(n),g=tQ(t),p=tQ(o),m="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return(0,l.jsx)(l.Fragment,{children:c.map(e=>(0,l.jsx)(tU,{id:e,nodeColorFunc:u,nodeStrokeColorFunc:g,nodeClassNameFunc:p,nodeBorderRadius:r,nodeStrokeWidth:a,NodeComponent:i,onClick:s,shapeRendering:m},e))})});let tq=e=>!e.hidden,t$=e=>{let t={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:t,boundingRect:e.nodeLookup.size>0?(0,i.Mi)((0,i.aZ)(e.nodeLookup,{filter:tq}),t):t,rfId:e.rfId,panZoom:e.panZoom,translateExtent:e.translateExtent,flowWidth:e.width,flowHeight:e.height,ariaLabelConfig:e.ariaLabelConfig}};function tJ(e){var t,n;let{style:o,className:s,nodeStrokeColor:c,nodeColor:u,nodeClassName:g="",nodeBorderRadius:m=5,nodeStrokeWidth:h,nodeComponent:v,bgColor:y,maskColor:b,maskStrokeColor:x,maskStrokeWidth:w,position:S="bottom-right",onClick:E,onNodeClick:k,pannable:N=!1,zoomable:M=!1,ariaLabel:P,inversePan:j,zoomStep:D=10,offsetScale:_=5}=e,A=p(),R=(0,r.useRef)(null),{boundingRect:O,viewBB:I,rfId:L,panZoom:z,translateExtent:B,flowWidth:V,flowHeight:H,ariaLabelConfig:T}=f(t$,d.x),F=null!=(t=null==o?void 0:o.width)?t:200,Z=null!=(n=null==o?void 0:o.height)?n:150,X=Math.max(O.width/F,O.height/Z),W=X*F,K=X*Z,Y=_*X,Q=O.x-(W-O.width)/2-Y,U=O.y-(K-O.height)/2-Y,G=W+2*Y,q=K+2*Y,$="".concat("react-flow__minimap-desc","-").concat(L),J=(0,r.useRef)(0),ee=(0,r.useRef)();J.current=X,(0,r.useEffect)(()=>{if(R.current&&z)return ee.current=(0,i.di)({domNode:R.current,panZoom:z,getTransform:()=>A.getState().transform,getViewScale:()=>J.current}),()=>{var e;null==(e=ee.current)||e.destroy()}},[z]),(0,r.useEffect)(()=>{var e;null==(e=ee.current)||e.update({translateExtent:B,width:V,height:H,inversePan:j,pannable:N,zoomStep:D,zoomable:M})},[N,M,j,D,B,V,H]);let et=E?e=>{var t;let[n,o]=(null==(t=ee.current)?void 0:t.pointer(e))||[0,0];E(e,{x:n,y:o})}:void 0,en=k?(0,r.useCallback)((e,t)=>{k(e,A.getState().nodeLookup.get(t).internals.userNode)},[]):void 0,eo=null!=P?P:T["minimap.ariaLabel"];return(0,l.jsx)(C,{position:S,style:{...o,"--xy-minimap-background-color-props":"string"==typeof y?y:void 0,"--xy-minimap-mask-background-color-props":"string"==typeof b?b:void 0,"--xy-minimap-mask-stroke-color-props":"string"==typeof x?x:void 0,"--xy-minimap-mask-stroke-width-props":"number"==typeof w?w*X:void 0,"--xy-minimap-node-background-color-props":"string"==typeof u?u:void 0,"--xy-minimap-node-stroke-color-props":"string"==typeof c?c:void 0,"--xy-minimap-node-stroke-width-props":"number"==typeof h?h:void 0},className:(0,a.A)(["react-flow__minimap",s]),"data-testid":"rf__minimap",children:(0,l.jsxs)("svg",{width:F,height:Z,viewBox:"".concat(Q," ").concat(U," ").concat(G," ").concat(q),className:"react-flow__minimap-svg",role:"img","aria-labelledby":$,ref:R,onClick:et,children:[eo&&(0,l.jsx)("title",{id:$,children:eo}),(0,l.jsx)(tG,{onClick:en,nodeColor:u,nodeStrokeColor:c,nodeBorderRadius:m,nodeClassName:g,nodeStrokeWidth:h,nodeComponent:v}),(0,l.jsx)("path",{className:"react-flow__minimap-mask",d:"M".concat(Q-Y,",").concat(U-Y,"h").concat(G+2*Y,"v").concat(q+2*Y,"h").concat(-G-2*Y,"z\n        M").concat(I.x,",").concat(I.y,"h").concat(I.width,"v").concat(I.height,"h").concat(-I.width,"z"),fillRule:"evenodd",pointerEvents:"none"})]})})}tJ.displayName="MiniMap";let t0=(0,r.memo)(tJ),t1=(i.xN.Line,i.xN.Handle,e=>{var t;return null==(t=e.domNode)?void 0:t.querySelector(".react-flow__renderer")}),t2=(e,t)=>(null==e?void 0:e.internals.positionAbsolute.x)!==(null==t?void 0:t.internals.positionAbsolute.x)||(null==e?void 0:e.internals.positionAbsolute.y)!==(null==t?void 0:t.internals.positionAbsolute.y)||(null==e?void 0:e.measured.width)!==(null==t?void 0:t.measured.width)||(null==e?void 0:e.measured.height)!==(null==t?void 0:t.measured.height)||(null==e?void 0:e.selected)!==(null==t?void 0:t.selected)||(null==e?void 0:e.internals.z)!==(null==t?void 0:t.internals.z)}}]);