// Manual Build Workflow Types
// These types define the structure for visual workflow building in RouKey

import { Node, Edge } from '@xyflow/react';

// Core workflow types
export interface ManualBuildWorkflow {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  settings: WorkflowSettings;
  custom_api_config_id?: string;
  is_active: boolean;
  is_template: boolean;
  template_category?: string;
  version: number;
  created_at: string;
  updated_at: string;
}

// Extended Node type for React Flow
export interface WorkflowNode extends Node {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
  width?: number;
  height?: number;
}

// Extended Edge type for React Flow
export interface WorkflowEdge extends Edge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string | null;
  targetHandle?: string | null;
  type?: string;
  animated?: boolean;
  style?: React.CSSProperties;
}

// Node types available in the system
export type NodeType =
  | 'userRequest'
  | 'classifier'
  | 'provider'
  | 'vision'
  | 'output'
  | 'roleAgent'
  | 'centralRouter'
  | 'conditional'
  | 'merge'
  | 'loop'
  | 'tool'
  | 'memory'
  | 'switch'
  | 'planner'
  | 'browsing';

// Base node data interface
export interface NodeData extends Record<string, unknown> {
  label: string;
  config: Record<string, any>;
  isConfigured: boolean;
  hasError?: boolean;
  errorMessage?: string;
  description?: string;
}

// Provider-specific node data
export interface ProviderNodeData extends NodeData {
  config: {
    providerId: '' | 'openai' | 'anthropic' | 'google' | 'deepseek' | 'xai' | 'openrouter';
    modelId: string;
    apiKey?: string; // Optional - can use config keys
    parameters: {
      temperature: number;
      maxTokens?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };
    fallbackProvider?: {
      providerId: string;
      modelId: string;
    };
    costLimit?: number;
  };
}

// Vision-specific node data (similar to Provider but for multimodal models only)
export interface VisionNodeData extends NodeData {
  config: {
    providerId: '' | 'openai' | 'anthropic' | 'google' | 'deepseek' | 'xai' | 'openrouter';
    modelId: string;
    apiKey?: string; // Optional - can use config keys
    parameters: {
      temperature: number;
      maxTokens?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };
    fallbackProvider?: {
      providerId: string;
      modelId: string;
    };
    costLimit?: number;
  };
}

// Role agent node data
export interface RoleAgentNodeData extends NodeData {
  config: {
    roleId: string;
    roleName: string;
    roleType: 'predefined' | 'custom' | 'new'; // Type of role selection
    customPrompt?: string;
    tools?: string[];
    memoryEnabled: boolean;
    // For creating new roles
    newRoleName?: string;
    newRoleDescription?: string;
  };
}

// Central Router node data
export interface CentralRouterNodeData extends NodeData {
  config: {
    routingStrategy: 'smart' | 'round_robin' | 'load_balanced' | 'priority';
    fallbackProvider?: string;
    maxRetries: number;
    timeout: number;
    enableCaching: boolean;
    debugMode: boolean;
  };
}

// Conditional node data
export interface ConditionalNodeData extends NodeData {
  config: {
    condition: string;
    conditionType: 'contains' | 'equals' | 'regex' | 'length' | 'custom';
    trueLabel: string;
    falseLabel: string;
  };
}

// Tool node data (removed web_browsing since it's now a dedicated node)
export interface ToolNodeData extends NodeData {
  config: {
    toolType: 'google_drive' | 'google_docs' | 'zapier' | 'notion' | 'google_sheets' | 'calendar' | 'gmail' | 'youtube' | 'supabase';
    toolConfig: Record<string, any>;
    timeout?: number;
    // Authentication status for tools
    isAuthenticated?: boolean;
    authProvider?: string;
    connectionStatus?: 'connected' | 'disconnected' | 'error';
  };
}

// Planner node data (similar to AI Provider but for planning browsing tasks)
export interface PlannerNodeData extends NodeData {
  config: {
    providerId: '' | 'openai' | 'anthropic' | 'google' | 'deepseek' | 'xai' | 'openrouter';
    modelId: string;
    apiKey?: string; // Optional - can use config keys
    parameters: {
      temperature: number;
      maxTokens?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };
    maxSubtasks?: number; // Maximum number of subtasks to generate
    fallbackProvider?: {
      providerId: string;
      modelId: string;
    };
    costLimit?: number;
  };
}

// Memory node data - plug-and-play brain for connected nodes
export interface MemoryNodeData extends NodeData {
  config: {
    memoryName: string; // Simple name for this memory instance
    maxSize?: number; // Maximum storage size in KB (default: 10MB)
    encryption?: boolean; // Whether to encrypt stored data (default: true)
    description?: string; // Optional description of what this memory stores
  };
}

// Browsing node data (intelligent web browsing agent)
export interface BrowsingNodeData extends NodeData {
  config: {
    maxSites?: number; // Maximum number of sites to visit
    timeout?: number; // Timeout per browsing operation
    enableScreenshots?: boolean; // Whether to take screenshots
    enableFormFilling?: boolean; // Whether to fill forms automatically
    enableCaptchaSolving?: boolean; // Whether to attempt CAPTCHA solving
    searchEngines?: ('google' | 'bing')[]; // Preferred search engines
    maxDepth?: number; // Maximum browsing depth (links to follow)
    respectRobots?: boolean; // Whether to respect robots.txt
    userAgent?: string; // Custom user agent
    enableJavaScript?: boolean; // Whether to enable JavaScript
    extractionGoals?: string[]; // What to extract from pages
    screenshotQuality?: number; // Screenshot quality (1-100)
    waitForSelector?: string; // CSS selector to wait for before proceeding
    customSelectors?: Record<string, string>; // Custom CSS selectors for extraction
    retryAttempts?: number; // Number of retry attempts for failed operations
    delayBetweenRequests?: number; // Delay between requests in milliseconds
  };
}

// Workflow settings
export interface WorkflowSettings {
  maxExecutionTime: number;
  retryCount: number;
  memoryEnabled: boolean;
  streamingEnabled: boolean;
  debugMode?: boolean;
  costLimit?: number;
}

// Execution types
export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  user_id: string;
  input_data: any;
  output_data?: any;
  execution_logs: ExecutionLog[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  current_node_id?: string;
  execution_time_ms?: number;
  total_tokens_used?: number;
  total_cost_usd?: number;
  error_message?: string;
  error_node_id?: string;
  started_at: string;
  completed_at?: string;
}

export interface ExecutionLog {
  timestamp: string;
  node_id: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  data?: any;
}

// Template types
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  template_nodes: WorkflowNode[];
  template_edges: WorkflowEdge[];
  default_settings: WorkflowSettings;
  is_official: boolean;
  created_by?: string;
  download_count: number;
  rating: number;
  tags: string[];
  preview_image_url?: string;
  created_at: string;
  updated_at: string;
}

// Node palette item for drag and drop
export interface NodePaletteItem {
  type: NodeType;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  category: 'core' | 'ai' | 'logic' | 'tools' | 'advanced';
  isAvailable: boolean; // Based on user tier
  defaultData: Partial<NodeData>;
}

// Workflow validation result
export interface WorkflowValidationResult {
  isValid: boolean;
  errors: WorkflowValidationError[];
  warnings: WorkflowValidationWarning[];
}

export interface WorkflowValidationError {
  nodeId?: string;
  edgeId?: string;
  type: 'missing_connection' | 'invalid_config' | 'circular_dependency' | 'missing_required_node';
  message: string;
}

export interface WorkflowValidationWarning {
  nodeId?: string;
  type: 'performance' | 'cost' | 'best_practice';
  message: string;
}

// Tier-based limits
export interface TierLimits {
  maxWorkflows: number;
  maxNodesPerWorkflow?: number; // null = unlimited
  maxExecutionsPerMonth?: number;
  canUseAdvancedNodes: boolean;
  canUseTemplates: boolean;
  canCreateTemplates: boolean;
  canShareWorkflows: boolean;
}

// Workflow editor state
export interface WorkflowEditorState {
  workflow: ManualBuildWorkflow | null;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  selectedNodes: string[];
  selectedEdges: string[];
  isExecuting: boolean;
  executionLogs: ExecutionLog[];
  validationResult: WorkflowValidationResult | null;
  isDirty: boolean;
  lastSaved: string | null;
}

// API request/response types
export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  custom_api_config_id?: string;
  template_id?: string;
}

export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  nodes?: WorkflowNode[];
  edges?: WorkflowEdge[];
  settings?: WorkflowSettings;
  is_active?: boolean;
}

export interface ExecuteWorkflowRequest {
  workflow_id: string;
  input_data: any;
  settings_override?: Partial<WorkflowSettings>;
}

export interface ExecuteWorkflowResponse {
  execution_id: string;
  status: 'started' | 'completed' | 'failed';
  output_data?: any;
  error_message?: string;
  execution_time_ms?: number;
  total_tokens_used?: number;
  total_cost_usd?: number;
}

// Provider information for node configuration
export interface ProviderInfo {
  id: string;
  name: string;
  models: ModelInfo[];
  supportsStreaming: boolean;
  averageCostPer1kTokens: number;
  color: string; // For UI theming
}

export interface ModelInfo {
  id: string;
  name: string;
  contextWindow: number;
  costPer1kTokens: number;
  description?: string;
}

// Export utility types
export type WorkflowNodeType = WorkflowNode['type'];
export type WorkflowStatus = WorkflowExecution['status'];
export type LogLevel = ExecutionLog['level'];
