"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2662],{175:(t,e,i)=>{i.d(e,{Q:()=>r});let s=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r(t){if("string"!=typeof t||t.includes("-"));else if(s.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}},2735:(t,e,i)=>{function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function r(t,e,i,r){if("function"==typeof e){let[o,n]=s(r);e=e(void 0!==i?i:t.custom,o,n)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[o,n]=s(r);e=e(void 0!==i?i:t.custom,o,n)}return e}i.d(e,{a:()=>r})},6642:(t,e,i)=>{i.d(e,{B:()=>r});let s={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},r={};for(let t in s)r[t]={isEnabled:e=>s[t].some(t=>!!e[t])}},9480:(t,e,i)=>{i.d(e,{Y:()=>r});var s=i(6642);function r(t){for(let e in t)s.B[e]={...s.B[e],...t[e]}}},13513:(t,e,i)=>{i.d(e,{K:()=>o});var s=i(81786),r=i(40956);class o extends r.B{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(e in t){let i=t[e];if("string"==typeof i||"number"==typeof i)return i}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return(0,s.ge)()}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}},14060:(t,e,i)=>{function s(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}i.d(e,{I:()=>s}),i(21448)},18070:(t,e,i)=>{i.d(e,{Y:()=>o});var s=i(21448);let r=(t,e)=>t.depth-e.depth;class o{constructor(){this.children=[],this.isDirty=!1}add(t){(0,s.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,s.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(r),this.isDirty=!1,this.children.forEach(t)}}},18802:(t,e,i)=>{i.d(e,{U:()=>n});var s=i(43891),r=i(5910),o=i(20419);function n(t,e){let{transitionEnd:i={},transition:n={},...a}=(0,o.K)(t,e)||{};for(let e in a={...a,...i}){var l;let i=(l=a[e],(0,r.p)(l)?l[l.length-1]||0:l);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,s.OQ)(i))}}},19253:(t,e,i)=>{i.d(e,{O:()=>a,e:()=>n});var s=i(6340),r=i(65305),o=i(98312);function n(t){return(0,s.N)(t.animate)||o._.some(e=>(0,r.w)(t[e]))}function a(t){return!!(n(t)||t.variants)}},19394:(t,e,i)=>{i.d(e,{o:()=>s});let s=Symbol.for("motionComponentSymbol")},20419:(t,e,i)=>{i.d(e,{K:()=>r});var s=i(2735);function r(t,e,i){let r=t.getProps();return(0,s.a)(r,e,void 0!==i?i:r.custom,t)}},20600:(t,e,i)=>{i.d(e,{e:()=>s});function s(t,{style:e,vars:i},s,r){for(let o in Object.assign(t.style,e,r&&r.getProjectionStyles(s)),i)t.style.setProperty(o,i[o])}},20637:(t,e,i)=>{i.d(e,{$:()=>o,H:()=>r});var s=i(43891);let r={};function o(t){for(let e in t)r[e]=t[e],(0,s.j4)(e)&&(r[e].isCSSVariable=!0)}},21014:(t,e,i)=>{i.d(e,{Z:()=>S});var s=i(95155);i(21448);var r=i(12115),o=i(90869),n=i(25214),a=i(51508),l=i(2999),h=i(24132),u=i(68972),d=i(6642),c=i(9480),p=i(19394),m=i(33991),f=i(43891),g=i(31788),v=i(80845),y=i(70797),x=i(97494);function S(t){var e,i;let{preloadedFeatures:S,createVisualElement:T,useRender:P,useVisualState:V,Component:w}=t;function b(t,e){var i,c,p;let S,b={...(0,r.useContext)(a.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,r.useContext)(o.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:A}=b,D=(0,h.z)(t),j=V(t,A);if(!A&&u.B){c=0,p=0,(0,r.useContext)(n.Y).strict;let t=function(t){let{drag:e,layout:i}=d.B;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(b);S=t.MeasureLayout,D.visualElement=function(t,e,i,s,o){let{visualElement:h}=(0,r.useContext)(l.A),u=(0,r.useContext)(n.Y),d=(0,r.useContext)(v.t),c=(0,r.useContext)(a.Q).reducedMotion,p=(0,r.useRef)(null);s=s||u.renderer,!p.current&&s&&(p.current=s(t,{visualState:e,parent:h,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:c}));let S=p.current,T=(0,r.useContext)(y.N);S&&!S.projection&&o&&("html"===S.type||"svg"===S.type)&&function(t,e,i,s){let{layoutId:r,layout:o,drag:n,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:o,alwaysMeasureLayout:!!n||a&&(0,m.X)(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(p.current,i,o,T);let P=(0,r.useRef)(!1);(0,r.useInsertionEffect)(()=>{S&&P.current&&S.update(i,d)});let V=i[g.n],w=(0,r.useRef)(!!V&&!window.MotionHandoffIsComplete?.(V)&&window.MotionHasOptimisedAnimation?.(V));return(0,x.E)(()=>{S&&(P.current=!0,window.MotionIsMounted=!0,S.updateFeatures(),f.k2.render(S.render),w.current&&S.animationState&&S.animationState.animateChanges())}),(0,r.useEffect)(()=>{S&&(!w.current&&S.animationState&&S.animationState.animateChanges(),w.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(V)}),w.current=!1))}),S}(w,j,b,T,t.ProjectionNode)}return(0,s.jsxs)(l.A.Provider,{value:D,children:[S&&D.visualElement?(0,s.jsx)(S,{visualElement:D.visualElement,...b}):null,P(w,t,(i=D.visualElement,(0,r.useCallback)(t=>{t&&j.onMount&&j.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):(0,m.X)(e)&&(e.current=t))},[i])),j,A,D.visualElement)]})}S&&(0,c.Y)(S),b.displayName="motion.".concat("string"==typeof w?w:"create(".concat(null!=(i=null!=(e=w.displayName)?e:w.name)?i:"",")"));let A=(0,r.forwardRef)(b);return A[p.o]=w,A}},22397:(t,e,i)=>{i.d(e,{b:()=>o});var s=i(43891),r=i(40956);class o extends r.B{constructor(){super(...arguments),this.KeyframeResolver=s.KN}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,s.SS)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}},29203:(t,e,i)=>{i.d(e,{O:()=>o});var s=i(43891),r=i(54577);function o(t,e,i){let{style:o,vars:n,transformOrigin:a}=t,l=!1,h=!1;for(let t in e){let i=e[t];if(s.fu.has(t)){l=!0;continue}if((0,s.j4)(t)){n[t]=i;continue}{let e=(0,s.eK)(i,s.Wh[t]);t.startsWith("origin")?(h=!0,a[t]=e):o[t]=e}}if(!e.transform&&(l||i?o.transform=(0,r.d)(e,t.transform,i):o.transform&&(o.transform="none")),h){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;o.transformOrigin=`${t} ${e} ${i}`}}},29901:(t,e,i)=>{i.d(e,{w:()=>s});let s={hasAnimatedSinceResize:!0,hasEverUpdated:!1}},33055:(t,e,i)=>{i.d(e,{z:()=>o});var s=i(43891),r=i(20637);function o(t,{layout:e,layoutId:i}){return s.fu.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!r.H[t]||"opacity"===t)}},33757:(t,e,i)=>{i.d(e,{L:()=>n,m:()=>o});var s=i(78588),r=i(96147);function o(t,e){return(0,s.FY)((0,s.bS)(t.getBoundingClientRect(),e))}function n(t,e,i){let s=o(t,i),{scroll:n}=e;return n&&((0,r.Ql)(s.x,n.offset.x),(0,r.Ql)(s.y,n.offset.y)),s}},34527:(t,e,i)=>{i.d(e,{x:()=>o});var s=i(43891),r=i(60990);function o(t,e,i){let o=(0,r.x)(t,e,i);for(let i in t)((0,s.SS)(t[i])||(0,s.SS)(e[i]))&&(o[-1!==s.Us.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return o}},36324:(t,e,i)=>{i.d(e,{n:()=>p});var s=i(19726),r=i(19624),o=i(49441),n=i(52290);let a=new WeakMap,l=new WeakMap,h=t=>{let e=a.get(t.target);e&&e(t)},u=t=>{t.forEach(h)},d={some:0,all:1};class c extends n.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:r}=t,o={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:d[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;l.has(i)||l.set(i,{});let s=l.get(i),r=JSON.stringify(e);return s[r]||(s[r]=new IntersectionObserver(u,{root:t,...e})),s[r]}(e);return a.set(t,i),s.observe(t),()=>{a.delete(t),s.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),o=e?i:s;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let p={inView:{Feature:c},tap:{Feature:o.H},focus:{Feature:r.c},hover:{Feature:s.e}}},36545:(t,e,i)=>{i.d(e,{P:()=>d});var s=i(14060),r=i(51563),o=i(44420),n=i(36324),a=i(53292),l=i(54160),h=i(59003);let u=(0,l.C)({...r.W,...n.n,...o.$,...a.Z},h.J),d=(0,s.I)(u)},40956:(t,e,i)=>{i.d(e,{B:()=>p});var s=i(43891),r=i(21448),o=i(6642),n=i(81786),a=i(42843),l=i(50408),h=i(65511),u=i(19253),d=i(2735);let c=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class p{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:o,visualState:n},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=s.hP,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=s.kB.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,s.Gt.render(this.render,!1,!0))};let{latestValues:l,renderState:h}=n;this.latestValues=l,this.baseTarget={...l},this.initialValues=e.initial?{...l}:{},this.renderState=h,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=(0,u.e)(e),this.isVariantNode=(0,u.O)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:d,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==l[t]&&(0,s.SS)(e)&&e.set(l[t],!1)}}mount(t){this.current=t,h.C.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),l.r.current||(0,a.U)(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||l.O.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,s.WG)(this.notifyUpdate),(0,s.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=s.fu.has(t);r&&this.onBindTransform&&this.onBindTransform();let o=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&s.Gt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{o(),n(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in o.B){let e=o.B[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,n.ge)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<c.length;e++){let i=c[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let r in e){let o=e[r],n=i[r];if((0,s.SS)(o))t.addValue(r,o);else if((0,s.SS)(n))t.addValue(r,(0,s.OQ)(o,{owner:t}));else if(n!==o)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(o):e.hasAnimated||e.set(o)}else{let e=t.getStaticValue(r);t.addValue(r,(0,s.OQ)(void 0!==e?e:o,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,s.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&((0,r.iW)(i)||(0,r.$X)(i))?i=parseFloat(i):!(0,s.tD)(i)&&s.f.test(e)&&(i=(0,s.Ju)(t,e)),this.setBaseTarget(t,(0,s.SS)(i)?i.get():i)),(0,s.SS)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=(0,d.a)(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||(0,s.SS)(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new r.vY),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}},44420:(t,e,i)=>{i.d(e,{$:()=>a});var s=i(78),r=i(38160),o=i(49267),n=i(44976);let a={pan:{Feature:r.f},drag:{Feature:s.w,ProjectionNode:n.P,MeasureLayout:o.$}}},44976:(t,e,i)=>{i.d(e,{P:()=>ty});var s=i(43891),r=i(21448),o=i(35580),n=i(46926),a=i(18070),l=i(73334),h=i(95902);let u=["TopLeft","TopRight","BottomLeft","BottomRight"],d=u.length,c=t=>"string"==typeof t?parseFloat(t):t,p=t=>"number"==typeof t||s.px.test(t);function m(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let f=v(0,.5,r.yT),g=v(.5,.95,r.lQ);function v(t,e,i){return s=>s<t?0:s>e?1:i((0,r.qB)(t,e,s))}function y(t,e){t.min=e.min,t.max=e.max}function x(t,e){y(t.x,e.x),y(t.y,e.y)}function S(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var T=i(96147),P=i(64200);function V(t,e,i,s,r){return t-=e,t=(0,T.hq)(t,1/i,s),void 0!==r&&(t=(0,T.hq)(t,1/r,s)),t}function w(t,e,[i,r,o],n,a){!function(t,e=0,i=1,r=.5,o,n=t,a=t){if(s.rq.test(e)&&(e=parseFloat(e),e=(0,s.k$)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let l=(0,s.k$)(n.min,n.max,r);t===n&&(l-=e),t.min=V(t.min,e,i,l,o),t.max=V(t.max,e,i,l,o)}(t,e[i],e[r],e[o],e.scale,n,a)}let b=["x","scaleX","originX"],A=["y","scaleY","originY"];function D(t,e,i,s){w(t.x,e,b,i?i.x:void 0,s?s.x:void 0),w(t.y,e,A,i?i.y:void 0,s?s.y:void 0)}var j=i(81786);function C(t){return 0===t.translate&&1===t.scale}function R(t){return C(t.x)&&C(t.y)}function B(t,e){return t.min===e.min&&t.max===e.max}function k(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function M(t,e){return k(t.x,e.x)&&k(t.y,e.y)}function E(t){return(0,P.CQ)(t.x)/(0,P.CQ)(t.y)}function L(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class F{constructor(){this.members=[]}add(t){(0,r.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,r.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var U=i(20637),O=i(94198),I=i(62662),$=i(29901);let N={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},W=["","X","Y","Z"],Q={visibility:"hidden"},H=0;function z(t,e,i,s){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),s&&(s[t]=0))}function Y({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:v,resetTransform:y}){return class{constructor(t={},i=e?.()){this.id=H++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,s.Qu.value&&(N.nodes=N.calculatedTargetDeltas=N.calculatedProjections=0),this.nodes.forEach(q),this.nodes.forEach(ti),this.nodes.forEach(ts),this.nodes.forEach(K),s.Qu.addProjectionMetrics&&s.Qu.addProjectionMetrics(N)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new a.Y)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new r.vY),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,s.xZ)(e)&&!(0,s.h1)(e),this.instance=e;let{layoutId:i,layout:r,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=(0,l.c)(s,250),$.w.hasAnimatedSinceResize&&($.w.hasAnimatedSinceResize=!1,this.nodes.forEach(te))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||o.getDefaultTransition()||th,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=o.getProps(),h=!this.targetLayout||!M(this.targetLayout,r),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...(0,s.rU)(n,"layout"),onPlay:a,onComplete:l};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||te(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,s.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(tr),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=(0,n.P)(i);if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",s.Gt,!(t||i))}let{parent:o}=e;o&&!o.hasCheckedOptimisedAppear&&t(o)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Z);return}this.isUpdating||this.nodes.forEach(J),this.isUpdating=!1,this.nodes.forEach(tt),this.nodes.forEach(X),this.nodes.forEach(G),this.clearAllSnapshots();let t=s.kB.now();s.uv.delta=(0,r.qE)(0,1e3/60,t-s.uv.timestamp),s.uv.timestamp=t,s.uv.isProcessing=!0,s.PP.update.process(s.uv),s.PP.preRender.process(s.uv),s.PP.render.process(s.uv),s.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,s.k2.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(_),this.sharedNodes.forEach(to)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,s.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){s.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||(0,P.CQ)(this.snapshot.measuredBox.x)||(0,P.CQ)(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,j.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=v(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!y)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!R(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||(0,I.HD)(this.latestValues)||r)&&(y(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),tc((e=s).x),tc(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,j.ge)();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(tm))){let{scroll:t}=this.root;t&&((0,T.Ql)(e.x,t.offset.x),(0,T.Ql)(e.y,t.offset.y))}return e}removeElementScroll(t){let e=(0,j.ge)();if(x(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:o}=s;s!==this.root&&r&&o.layoutScroll&&(r.wasRoot&&x(e,t),(0,T.Ql)(e.x,r.offset.x),(0,T.Ql)(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=(0,j.ge)();x(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&(0,T.Ww)(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),(0,I.HD)(s.latestValues)&&(0,T.Ww)(i,s.latestValues)}return(0,I.HD)(this.latestValues)&&(0,T.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,j.ge)();x(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,I.HD)(i.latestValues))continue;(0,I.vk)(i.latestValues)&&i.updateSnapshot();let s=(0,j.ge)();x(s,i.measurePageBox()),D(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return(0,I.HD)(this.latestValues)&&D(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==s.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:o}=this.options;if(this.layout&&(r||o)){if(this.resolvedRelativeTargetAt=s.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,j.ge)(),this.relativeTargetOrigin=(0,j.ge)(),(0,P.jA)(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),x(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,j.ge)(),this.targetWithTransforms=(0,j.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,P.N)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):x(this.target,this.layout.layoutBox),(0,T.o4)(this.target,this.targetDelta)):x(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,j.ge)(),this.relativeTargetOrigin=(0,j.ge)(),(0,P.jA)(this.relativeTargetOrigin,this.target,t.target),x(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}s.Qu.value&&N.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,I.vk)(this.parent.latestValues)||(0,I.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===s.uv.timestamp&&(i=!1),i)return;let{layout:r,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||o))return;x(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,a=this.treeScale.y;(0,T.OU)(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=(0,j.ge)());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(S(this.prevProjectionDelta.x,this.projectionDelta.x),S(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,P.vb)(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===n&&this.treeScale.y===a&&L(this.projectionDelta.x,this.prevProjectionDelta.x)&&L(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),s.Qu.value&&N.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,j.xU)(),this.projectionDelta=(0,j.xU)(),this.projectionDeltaWithTransform=(0,j.xU)()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,o=r?r.latestValues:{},n={...this.latestValues},a=(0,j.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let l=(0,j.ge)(),h=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),v=this.getStack(),y=!v||v.members.length<=1,S=!!(h&&!y&&!0===this.options.crossfade&&!this.path.some(tl));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(tn(a.x,t.x,r),tn(a.y,t.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var v,T,V,w,b,A;(0,P.jA)(l,this.layout.layoutBox,this.relativeParent.layout.layoutBox),V=this.relativeTarget,w=this.relativeTargetOrigin,b=l,A=r,ta(V.x,w.x,b.x,A),ta(V.y,w.y,b.y,A),i&&(v=this.relativeTarget,T=i,B(v.x,T.x)&&B(v.y,T.y))&&(this.isProjectionDirty=!1),i||(i=(0,j.ge)()),x(i,this.relativeTarget)}h&&(this.animationValues=n,function(t,e,i,r,o,n){o?(t.opacity=(0,s.k$)(0,i.opacity??1,f(r)),t.opacityExit=(0,s.k$)(e.opacity??1,0,g(r))):n&&(t.opacity=(0,s.k$)(e.opacity??1,i.opacity??1,r));for(let o=0;o<d;o++){let n=`border${u[o]}Radius`,a=m(e,n),l=m(i,n);(void 0!==a||void 0!==l)&&(a||(a=0),l||(l=0),0===a||0===l||p(a)===p(l)?(t[n]=Math.max((0,s.k$)(c(a),c(l),r),0),(s.rq.test(l)||s.rq.test(a))&&(t[n]+="%")):t[n]=l)}(e.rotate||i.rotate)&&(t.rotate=(0,s.k$)(e.rotate||0,i.rotate||0,r))}(n,o,this.latestValues,r,S,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,s.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=s.Gt.update(()=>{$.w.hasAnimatedSinceResize=!0,s.qU.layout++,this.motionValue||(this.motionValue=(0,s.OQ)(0)),this.currentAnimation=(0,o.z)(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{s.qU.layout--},onComplete:()=>{s.qU.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:r}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&tp(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||(0,j.ge)();let e=(0,P.CQ)(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=(0,P.CQ)(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}x(e,i),(0,T.Ww)(e,r),(0,P.vb)(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new F),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&z("z",t,s,this.animationValues);for(let e=0;e<W.length;e++)z(`rotate${W[e]}`,t,s,this.animationValues),z(`skew${W[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return Q;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=(0,h.u)(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=(0,h.u)(t?.pointerEvents)||""),this.hasProjected&&!(0,I.HD)(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",r=t.x.translate/e.x,o=t.y.translate/e.y,n=i?.z||0;if((r||o||n)&&(s=`translate3d(${r}px, ${o}px, ${n}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:o,skewX:n,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),r&&(s+=`rotateX(${r}deg) `),o&&(s+=`rotateY(${o}deg) `),n&&(s+=`skewX(${n}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:o,y:n}=this.projectionDelta;for(let t in e.transformOrigin=`${100*o.origin}% ${100*n.origin}% 0`,s.animationValues?e.opacity=s===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=s===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,U.H){if(void 0===r[t])continue;let{correct:i,applyTo:o,isCSSVariable:n}=U.H[t],a="none"===e.transform?r[t]:i(r[t],s);if(o){let t=o.length;for(let i=0;i<t;i++)e[o[i]]=a}else n?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?(0,h.u)(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(Z),this.root.sharedNodes.clear()}}}function X(t){t.updateLayout()}function G(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:r}=t.options,o=e.source!==t.layout.source;"size"===r?(0,O.X)(t=>{let s=o?e.measuredBox[t]:e.layoutBox[t],r=(0,P.CQ)(s);s.min=i[t].min,s.max=s.min+r}):tp(r,e.layoutBox,i)&&(0,O.X)(s=>{let r=o?e.measuredBox[s]:e.layoutBox[s],n=(0,P.CQ)(i[s]);r.max=r.min+n,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+n)});let n=(0,j.xU)();(0,P.vb)(n,i,e.layoutBox);let a=(0,j.xU)();o?(0,P.vb)(a,t.applyTransform(s,!0),e.measuredBox):(0,P.vb)(a,i,e.layoutBox);let l=!R(n),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:o}=s;if(r&&o){let n=(0,j.ge)();(0,P.jA)(n,e.layoutBox,r.layoutBox);let a=(0,j.ge)();(0,P.jA)(a,i,o.layoutBox),M(n,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=n,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:n,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function q(t){s.Qu.value&&N.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function K(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function _(t){t.clearSnapshot()}function Z(t){t.clearMeasurements()}function J(t){t.isLayoutDirty=!1}function tt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function te(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ti(t){t.resolveTargetDelta()}function ts(t){t.calcProjection()}function tr(t){t.resetSkewAndRotation()}function to(t){t.removeLeadSnapshot()}function tn(t,e,i){t.translate=(0,s.k$)(e.translate,0,i),t.scale=(0,s.k$)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function ta(t,e,i,r){t.min=(0,s.k$)(e.min,i.min,r),t.max=(0,s.k$)(e.max,i.max,r)}function tl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let th={duration:.45,ease:[.4,0,.1,1]},tu=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),td=tu("applewebkit/")&&!tu("chrome/")?Math.round:r.lQ;function tc(t){t.min=td(t.min),t.max=td(t.max)}function tp(t,e,i){return"position"===t||"preserve-aspect"===t&&!(0,P.HQ)(E(e),E(i),.2)}function tm(t){return t!==t.root&&t.scroll?.wasRoot}var tf=i(51442);let tg=Y({attachResizeListener:(t,e)=>(0,tf.k)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tv={current:void 0},ty=Y({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!tv.current){let t=new tg({});t.mount(window),t.setOptions({layoutScroll:!0}),tv.current=t}return tv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position})},45380:(t,e,i)=>{i(21448),i(43891),i(46756)},46756:(t,e,i)=>{i(43891),i(21448);let s={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};new WeakMap,new WeakMap,new WeakMap},49267:(t,e,i)=>{i.d(e,{$:()=>m});var s=i(95155),r=i(43891),o=i(12115),n=i(32082),a=i(90869),l=i(70797),h=i(29901);function u(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let d={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!r.px.test(t))return t;else t=parseFloat(t);let i=u(t,e.target.x),s=u(t,e.target.y);return`${i}% ${s}%`}};var c=i(20637);class p extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=t;(0,c.$)(f),r&&(e.group&&e.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),h.w.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:o}=this.props,{projection:n}=i;return n&&(n.isPresent=o,s||t.layoutDependency!==e||void 0===e||t.isPresent!==o?n.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?n.promote():n.relegate()||r.Gt.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),r.k2.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function m(t){let[e,i]=(0,n.xQ)(),r=(0,o.useContext)(a.L);return(0,s.jsx)(p,{...t,layoutGroup:r,switchLayoutGroup:(0,o.useContext)(l.N),isPresent:e,safeToRemove:i})}let f={borderRadius:{...d,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d,borderTopRightRadius:d,borderBottomLeftRadius:d,borderBottomRightRadius:d,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=r.f.parse(t);if(s.length>5)return t;let o=r.f.createTransformer(t),n=+("number"!=typeof s[0]),a=i.x.scale*e.x,l=i.y.scale*e.y;s[0+n]/=a,s[1+n]/=l;let h=(0,r.k$)(a,l,.5);return"number"==typeof s[2+n]&&(s[2+n]/=h),"number"==typeof s[3+n]&&(s[3+n]/=h),o(s)}}}},51563:(t,e,i)=>{i.d(e,{W:()=>x});var s=i(6340),r=i(78660),o=i(5910),n=i(93045),a=i(65305),l=i(98312);let h=l._.length;var u=i(20419);let d=[...l.U].reverse(),c=l.U.length;function p(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function m(){return{animate:p(!0),whileInView:p(),whileHover:p(),whileTap:p(),whileDrag:p(),whileFocus:p(),exit:p()}}var f=i(52290);class g extends f.X{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(0,r._)(t,e,i))),i=m(),p=!0,f=e=>(i,s)=>{let r=(0,u.K)(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function g(r){let{props:m}=t,g=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<h;t++){let s=l._[t],r=e.props[s];((0,a.w)(r)||!1===r)&&(i[s]=r)}return i}(t.parent)||{},v=[],y=new Set,x={},S=1/0;for(let e=0;e<c;e++){var T,P;let l=d[e],h=i[l],u=void 0!==m[l]?m[l]:g[l],c=(0,a.w)(u),V=l===r?h.isActive:null;!1===V&&(S=e);let w=u===g[l]&&u!==m[l]&&c;if(w&&p&&t.manuallyAnimateOnMount&&(w=!1),h.protectedKeys={...x},!h.isActive&&null===V||!u&&!h.prevProp||(0,s.N)(u)||"boolean"==typeof u)continue;let b=(T=h.prevProp,"string"==typeof(P=u)?P!==T:!!Array.isArray(P)&&!(0,n.a)(P,T)),A=b||l===r&&h.isActive&&!w&&c||e>S&&c,D=!1,j=Array.isArray(u)?u:[u],C=j.reduce(f(l),{});!1===V&&(C={});let{prevResolvedValues:R={}}=h,B={...R,...C},k=e=>{A=!0,y.has(e)&&(D=!0,y.delete(e)),h.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in B){let e=C[t],i=R[t];if(x.hasOwnProperty(t))continue;let s=!1;((0,o.p)(e)&&(0,o.p)(i)?(0,n.a)(e,i):e===i)?void 0!==e&&y.has(t)?k(t):h.protectedKeys[t]=!0:null!=e?k(t):y.add(t)}h.prevProp=u,h.prevResolvedValues=C,h.isActive&&(x={...x,...C}),p&&t.blockInitialAnimation&&(A=!1);let M=!(w&&b)||D;A&&M&&v.push(...j.map(t=>({animation:t,options:{type:l}})))}if(y.size){let e={};if("boolean"!=typeof m.initial){let i=(0,u.K)(t,Array.isArray(m.initial)?m.initial[0]:m.initial);i&&i.transition&&(e.transition=i.transition)}y.forEach(i=>{let s=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=s??null}),v.push({animation:e})}let V=!!v.length;return p&&(!1===m.initial||m.initial===m.animate)&&!t.manuallyAnimateOnMount&&(V=!1),p=!1,V?e(v):Promise.resolve()}return{animateChanges:g,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let r=g(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=m(),p=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,s.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let v=0;class y extends f.X{constructor(){super(...arguments),this.id=v++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let x={animation:{Feature:g},exit:{Feature:y}}},52290:(t,e,i)=>{i.d(e,{X:()=>s});class s{constructor(t){this.isMounted=!1,this.node=t}update(){}}},53292:(t,e,i)=>{i.d(e,{Z:()=>o});var s=i(44976),r=i(49267);let o={layout:{ProjectionNode:s.P,MeasureLayout:r.$}}},54160:(t,e,i)=>{i.d(e,{C:()=>S});var s=i(21014),r=i(43891),o=i(12115),n=i(33055),a=i(29203);let l=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function h(t,e,i){for(let s in e)(0,r.SS)(e[s])||(0,n.z)(s,i)||(t[s]=e[s])}var u=i(82076);let d=()=>({...l(),attrs:{}});var c=i(93095),p=i(99776),m=i(175),f=i(96488),g=i(60990);let v={useVisualState:(0,f.T)({scrapeMotionValuesFromProps:g.x,createRenderState:l})};var y=i(34527);let x={useVisualState:(0,f.T)({scrapeMotionValuesFromProps:y.x,createRenderState:d})};function S(t,e){return function(i,{forwardMotionProps:n}={forwardMotionProps:!1}){let f={...(0,m.Q)(i)?x:v,preloadedFeatures:t,useRender:function(t=!1){return(e,i,s,{latestValues:n},f)=>{let g=((0,m.Q)(e)?function(t,e,i,s){let r=(0,o.useMemo)(()=>{let i=d();return(0,u.B)(i,e,(0,c.n)(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};h(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return h(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,o.useMemo)(()=>{let i=l();return(0,a.O)(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,f,e),v=(0,p.J)(i,"string"==typeof e,t),y=e!==o.Fragment?{...v,...g,ref:s}:{},{children:x}=i,S=(0,o.useMemo)(()=>(0,r.SS)(x)?x.get():x,[x]);return(0,o.createElement)(e,{...y,children:S})}}(n),createVisualElement:e,Component:i};return(0,s.Z)(f)}}},54577:(t,e,i)=>{i.d(e,{d:()=>n});var s=i(43891);let r={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},o=s.Us.length;function n(t,e,i){let n="",a=!0;for(let l=0;l<o;l++){let o=s.Us[l],h=t[o];if(void 0===h)continue;let u=!0;if(!(u="number"==typeof h?h===+!!o.startsWith("scale"):0===parseFloat(h))||i){let t=(0,s.eK)(h,s.Wh[o]);if(!u){a=!1;let e=r[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,a?"":n):a&&(n="none"),n}},55020:(t,e,i)=>{i.d(e,{_EF:()=>u,PY1:()=>s.P}),i(60760),i(43050),i(51251),i(56787),i(14060),i(54160);var s=i(36545);i(26953),i(51586);var r=i(51563);i(96488),i(64200),i(81786),i(99776),i(68972),i(39174),i(97494),i(86811);var o=i(36324),n=i(59003);let a={renderer:n.J,...r.W,...o.n};var l=i(44420),h=i(53292);l.$,h.Z,n.J,r.W,i(95056),i(70749),i(90795),i(6464),i(8619),i(7302),i(97178),i(93486),i(62094),i(64132),i(78657),i(58492),i(95902),i(93084),i(3961),i(21448),i(198),i(71492),i(98828),i(98663),i(78660),i(32082),i(90693),i(93810),i(16242),i(21014),i(19394),i(74830),i(20637),i(87455),i(12115),i(44976),i(54577),i(65511),i(40956),i(14087),i(30903),i(29240),i(74008),i(31788),i(1265),i(90869),i(51508),i(2999),i(80845),i(70797),i(18070),i(88558),i(2736),i(39126),i(80303),i(73334);var u=i(19209);i(36464),i(55539),i(45380),i(46756),i(82182),i(49489),i(2986),i(43891)},59003:(t,e,i)=>{i.d(e,{J:()=>a});var s=i(12115),r=i(75245),o=i(60728),n=i(175);let a=(t,e)=>(0,n.Q)(t)?new o.l(e):new r.M(e,{allowProjection:t!==s.Fragment})},60728:(t,e,i)=>{i.d(e,{l:()=>c});var s=i(43891),r=i(81786),o=i(22397),n=i(78450),a=i(82076);let l=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);var h=i(93095),u=i(20600),d=i(34527);class c extends o.b{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=r.ge}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(s.fu.has(e)){let t=(0,s.Df)(e);return t&&t.default||0}return e=l.has(e)?e:(0,n.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return(0,d.x)(t,e,i)}build(t,e,i){(0,a.B)(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in(0,u.e)(t,e,void 0,s),e.attrs)t.setAttribute(l.has(i)?i:(0,n.I)(i),e.attrs[i])}mount(t){this.isSVGTag=(0,h.n)(t.tagName),super.mount(t)}}},60990:(t,e,i)=>{i.d(e,{x:()=>o});var s=i(43891),r=i(33055);function o(t,e,i){let{style:o}=t,n={};for(let a in o)((0,s.SS)(o[a])||e.style&&(0,s.SS)(e.style[a])||(0,r.z)(a,t)||i?.getValue(a)?.liveStyle!==void 0)&&(n[a]=o[a]);return n}},62662:(t,e,i)=>{function s(t){return void 0===t||1===t}function r({scale:t,scaleX:e,scaleY:i}){return!s(t)||!s(e)||!s(i)}function o(t){return r(t)||n(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function n(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>o,vF:()=>n,vk:()=>r})},64200:(t,e,i)=>{i.d(e,{CQ:()=>r,HQ:()=>o,N:()=>h,jA:()=>d,vb:()=>a});var s=i(43891);function r(t){return t.max-t.min}function o(t,e,i){return Math.abs(t-e)<=i}function n(t,e,i,o=.5){t.origin=o,t.originPoint=(0,s.k$)(e.min,e.max,t.origin),t.scale=r(i)/r(e),t.translate=(0,s.k$)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function a(t,e,i,s){n(t.x,e.x,i.x,s?s.originX:void 0),n(t.y,e.y,i.y,s?s.originY:void 0)}function l(t,e,i){t.min=i.min+e.min,t.max=t.min+r(e)}function h(t,e,i){l(t.x,e.x,i.x),l(t.y,e.y,i.y)}function u(t,e,i){t.min=e.min-i.min,t.max=t.min+r(e)}function d(t,e,i){u(t.x,e.x,i.x),u(t.y,e.y,i.y)}},65305:(t,e,i)=>{i.d(e,{w:()=>s});function s(t){return"string"==typeof t||Array.isArray(t)}},65511:(t,e,i)=>{i.d(e,{C:()=>s});let s=new WeakMap},74830:(t,e,i)=>{i.d(e,{S:()=>r});let s=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||s.has(t)}},75245:(t,e,i)=>{i.d(e,{M:()=>h});var s=i(43891),r=i(33757),o=i(22397),n=i(29203),a=i(20600),l=i(60990);class h extends o.b{constructor(){super(...arguments),this.type="html",this.renderInstance=a.e}readValueFromInstance(t,e){if(s.fu.has(e))return this.projection?.isProjecting?(0,s.zs)(e):(0,s.Ib)(t,e);{let i=window.getComputedStyle(t),r=((0,s.j4)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,r.m)(t,e)}build(t,e,i){(0,n.O)(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return(0,l.x)(t,e,i)}}},78450:(t,e,i)=>{i.d(e,{I:()=>s});let s=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},78588:(t,e,i)=>{function s({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function r({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function o(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}i.d(e,{FY:()=>s,bS:()=>o,pA:()=>r})},80131:(t,e,i)=>{},81786:(t,e,i)=>{i.d(e,{ge:()=>n,xU:()=>r});let s=()=>({translate:0,scale:1,origin:0,originPoint:0}),r=()=>({x:s(),y:s()}),o=()=>({min:0,max:0}),n=()=>({x:o(),y:o()})},82076:(t,e,i)=>{i.d(e,{B:()=>a});var s=i(29203),r=i(43891);let o={offset:"stroke-dashoffset",array:"stroke-dasharray"},n={offset:"strokeDashoffset",array:"strokeDasharray"};function a(t,{attrX:e,attrY:i,attrScale:a,pathLength:l,pathSpacing:h=1,pathOffset:u=0,...d},c,p,m){if((0,s.O)(t,d,p),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:f,style:g}=t;f.transform&&(g.transform=f.transform,delete f.transform),(g.transform||f.transformOrigin)&&(g.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),g.transform&&(g.transformBox=m?.transformBox??"fill-box",delete f.transformBox),void 0!==e&&(f.x=e),void 0!==i&&(f.y=i),void 0!==a&&(f.scale=a),void 0!==l&&function(t,e,i=1,s=0,a=!0){t.pathLength=1;let l=a?o:n;t[l.offset]=r.px.transform(-s);let h=r.px.transform(e),u=r.px.transform(i);t[l.array]=`${h} ${u}`}(f,l,h,u,!1)}},82182:(t,e,i)=>{i(43891)},87455:(t,e,i)=>{i(44976)},93095:(t,e,i)=>{i.d(e,{n:()=>s});let s=t=>"string"==typeof t&&"svg"===t.toLowerCase()},94198:(t,e,i)=>{i.d(e,{X:()=>s});function s(t){return[t("x"),t("y")]}},96147:(t,e,i)=>{i.d(e,{OU:()=>h,Ql:()=>u,Ww:()=>c,hq:()=>o,o4:()=>l});var s=i(43891),r=i(62662);function o(t,e,i){return i+e*(t-i)}function n(t,e,i,s,r){return void 0!==r&&(t=s+r*(t-s)),s+i*(t-s)+e}function a(t,e=0,i=1,s,r){t.min=n(t.min,e,i,s,r),t.max=n(t.max,e,i,s,r)}function l(t,{x:e,y:i}){a(t.x,e.translate,e.scale,e.originPoint),a(t.y,i.translate,i.scale,i.originPoint)}function h(t,e,i,s=!1){let o,n,a=i.length;if(a){e.x=e.y=1;for(let h=0;h<a;h++){n=(o=i[h]).projectionDelta;let{visualElement:a}=o.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&c(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,l(t,n)),s&&(0,r.HD)(o.latestValues)&&c(t,o.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function u(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,r,o=.5){let n=(0,s.k$)(t.min,t.max,o);a(t,e,i,n,r)}function c(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},96488:(t,e,i)=>{i.d(e,{T:()=>d});var s=i(12115),r=i(6340),o=i(2999),n=i(80845),a=i(19253),l=i(2735),h=i(82885),u=i(95902);let d=t=>(e,i)=>{let d=(0,s.useContext)(o.A),c=(0,s.useContext)(n.t),p=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,o){return{latestValues:function(t,e,i,s){let o={},n=s(t,{});for(let t in n)o[t]=(0,u.u)(n[t]);let{initial:h,animate:d}=t,c=(0,a.e)(t),p=(0,a.O)(t);e&&p&&!c&&!1!==t.inherit&&(void 0===h&&(h=e.initial),void 0===d&&(d=e.animate));let m=!!i&&!1===i.initial,f=(m=m||!1===h)?d:h;if(f&&"boolean"!=typeof f&&!(0,r.N)(f)){let e=Array.isArray(f)?f:[f];for(let i=0;i<e.length;i++){let s=(0,l.a)(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=m?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(i,s,o,t),renderState:e()}})(t,e,d,c);return i?p():(0,h.M)(p)}},98312:(t,e,i)=>{i.d(e,{U:()=>s,_:()=>r});let s=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],r=["initial",...s]},99776:(t,e,i)=>{i.d(e,{J:()=>o});var s=i(74830);let r=t=>!(0,s.S)(t);try{!function(t){"function"==typeof t&&(r=e=>e.startsWith("on")?!(0,s.S)(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}function o(t,e,i){let o={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(r(n)||!0===i&&(0,s.S)(n)||!e&&!(0,s.S)(n)||t.draggable&&n.startsWith("onDrag"))&&(o[n]=t[n]);return o}}}]);