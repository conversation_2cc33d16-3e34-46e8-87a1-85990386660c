'use client';

import { CodeBracketIcon } from '@heroicons/react/24/outline';
import { Handle, Position } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode, ConditionalNodeData } from '@/types/manualBuild';

interface ConditionalNodeProps {
  data: WorkflowNode['data'];
}

export default function ConditionalNode({ data }: ConditionalNodeProps) {
  const config = data.config as ConditionalNodeData['config'];
  const condition = config?.condition;
  const conditionType = config?.conditionType;

  return (
    <div className="relative">
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors"
        style={{ left: -12 }}
      />
      <div className="absolute text-xs text-gray-300 font-medium pointer-events-none" style={{ left: -50, top: '45%' }}>
        Input
      </div>

      {/* Node Body */}
      <div className="min-w-[200px] rounded-lg border-2 border-amber-500 bg-amber-900/20 backdrop-blur-sm shadow-lg">
        {/* Header */}
        <div className="px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-amber-500/20 to-amber-600/10">
          <div className="p-2 rounded-lg bg-amber-500/20 text-amber-500">
            <CodeBracketIcon className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <div className="font-medium text-white text-sm">
              {data.label}
            </div>
            <div className="text-xs text-gray-400 mt-1">
              Branch workflow based on conditions
            </div>
          </div>
          <div className="w-2 h-2 bg-amber-500 rounded-full" />
        </div>

        {/* Content */}
        <div className="px-4 py-3 border-t border-gray-700/50 space-y-3">
          {condition ? (
            <div className="space-y-2">
              <div className="text-sm text-gray-300">
                Condition: {conditionType || 'custom'}
              </div>
              <div className="text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded font-mono">
                {condition}
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="text-green-300">
                  True: {config?.trueLabel || 'Continue'}
                </div>
                <div className="text-red-300">
                  False: {config?.falseLabel || 'Skip'}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-sm text-gray-300">
                Conditional Logic
              </div>
              <div className="text-xs text-gray-400">
                Configure conditions to branch your workflow into different paths.
              </div>
              <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
                ⚠️ Needs configuration
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Output Handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="true"
        className="w-6 h-6 border-2 border-green-500 bg-green-600 hover:border-green-400 hover:bg-green-500 transition-colors"
        style={{ right: -12, top: '40%' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="false"
        className="w-6 h-6 border-2 border-red-500 bg-red-600 hover:border-red-400 hover:bg-red-500 transition-colors"
        style={{ right: -12, top: '60%' }}
      />

      {/* Handle Labels */}
      <div className="absolute text-xs text-green-300 font-medium pointer-events-none" style={{ right: -50, top: '35%' }}>
        True
      </div>
      <div className="absolute text-xs text-red-300 font-medium pointer-events-none" style={{ right: -50, top: '55%' }}>
        False
      </div>
    </div>
  );
}
